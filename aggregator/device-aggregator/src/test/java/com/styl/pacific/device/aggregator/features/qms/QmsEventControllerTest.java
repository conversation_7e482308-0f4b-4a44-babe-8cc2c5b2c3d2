/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.qms;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.styl.pacific.device.aggregator.features.qms.event.client.QmsEventClient;
import com.styl.pacific.device.aggregator.features.qms.event.controller.QmsEventController;
import com.styl.pacific.device.aggregator.features.qms.event.service.QmsEventServiceImpl;
import com.styl.pacific.order.service.shared.http.qms.event.request.UpsertQmsEventRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class QmsEventControllerTest {

	@Mock
	private QmsEventClient qmsEventClient;

	@InjectMocks
	private QmsEventServiceImpl qmsEventService;

	private QmsEventController qmsEventController;

	@BeforeEach
	void setup() {
		qmsEventController = new QmsEventController(qmsEventService);
	}

	@Test
	public void callCustomer_shouldCallOrderClient() {
		UpsertQmsEventRequest request = mock(UpsertQmsEventRequest.class);
		qmsEventController.callCustomer(request);
		verify(qmsEventClient, times(1)).callCustomer(request);
	}
}
