/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.authz.service.core.features.userroles.request;

import com.styl.pacific.domain.range.InstantDateTimeRange;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserRoleId;
import java.util.Collection;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor
public class UserRoleFilter {
	private UserRoleId byUserRoleId;
	private TenantId byTenantId;
	private String byExternalId;
	private Set<String> byExternalIds;
	private String byRoleTitle;
	private InstantDateTimeRange byCreatedRange;
	private Collection<UserRoleId> includeRoleIds;
	private Set<String> excludeExternalIds;
}
