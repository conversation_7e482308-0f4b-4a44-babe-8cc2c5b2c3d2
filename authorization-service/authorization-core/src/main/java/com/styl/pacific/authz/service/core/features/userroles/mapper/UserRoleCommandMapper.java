/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.authz.service.core.features.userroles.mapper;

import com.styl.pacific.authz.service.core.features.featurenode.entities.FeatureNode;
import com.styl.pacific.authz.service.core.features.userroles.entities.UserRole;
import com.styl.pacific.authz.service.core.features.userroles.request.UpsertUserRoleCommand;
import com.styl.pacific.authz.shared.http.requests.CreateUserRoleRequest;
import com.styl.pacific.authz.shared.http.requests.UpdateUserRoleRequest;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class })
public interface UserRoleCommandMapper {
	UserRoleCommandMapper INSTANCE = Mappers.getMapper(UserRoleCommandMapper.class);

	@Mapping(target = "userRoleId", ignore = true)
	@Mapping(target = "externalId", ignore = true)
	UpsertUserRoleCommand toUpdateCommand(CreateUserRoleRequest source);

	@Mapping(target = "userRoleId.value", source = "id")
	@Mapping(target = "externalId", ignore = true)
	UpsertUserRoleCommand toUpdateCommand(UpdateUserRoleRequest request);

	@Mapping(target = "id.value", source = "command.userRoleId.value")
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedBy", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "deletedAt", ignore = true)
	UserRole toEntity(TenantId tenantId, UpsertUserRoleCommand command, List<FeatureNode> permissions);

}
