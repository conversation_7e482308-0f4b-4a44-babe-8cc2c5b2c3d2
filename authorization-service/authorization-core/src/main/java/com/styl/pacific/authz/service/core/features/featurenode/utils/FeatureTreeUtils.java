/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.authz.service.core.features.featurenode.utils;

import com.styl.pacific.authz.service.core.features.featurenode.entities.FeatureNode;
import com.styl.pacific.domain.permissions.FeatureNodeType;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class FeatureTreeUtils {
	public static List<FeatureNode> buildTree(List<FeatureNode> featureNodes) {
		final var nodes = featureNodes.stream()
				.map(FeatureNode::cloneNode)
				.toList();
		final var nodeMap = nodes.stream()
				.collect(Collectors.toMap(FeatureNode::getNodePath, Function.identity()));

		nodeMap.values()
				.forEach(child -> getParentNode(nodes, child).ifPresent(parent -> parent.getChildren()
						.add(child)));

		final var tree = nodeMap.values()
				.stream()
				.filter(node -> FeatureNodeType.MODULE.equals(node.getNodeType()))
				.sorted(Comparator.comparingInt(FeatureNode::getDisplayOrder))
				.toList();
		tree.forEach(it -> it.getChildren()
				.sort(Comparator.comparingInt(FeatureNode::getDisplayOrder)));
		return tree;
	}

	private static Optional<FeatureNode> getParentNode(List<FeatureNode> featureNodes, FeatureNode child) {
		return featureNodes.stream()
				.filter(parent -> !child.getNodePath()
						.equals(parent.getNodePath()) && child.getNodePath()
								.startsWith(parent.getNodePath()))
				.findAny();
	}

}
