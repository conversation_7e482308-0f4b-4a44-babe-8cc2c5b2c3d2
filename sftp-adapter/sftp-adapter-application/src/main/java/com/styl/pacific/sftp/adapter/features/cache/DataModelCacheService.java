/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.sftp.adapter.features.cache;

import com.styl.pacific.sftp.adapter.features.cache.entities.CustomerFamilyEntryCache;
import com.styl.pacific.sftp.adapter.features.cache.request.CreateCustomerFamilyEntryCacheCommand;
import com.styl.pacific.sftp.adapter.features.mappingjob.entities.TenantMappingJobId;
import java.util.Optional;

public interface DataModelCacheService {
	void createFamilyCustomerCacheEntry(CreateCustomerFamilyEntryCacheCommand command);

	Optional<CustomerFamilyEntryCache> getMainCustomerByFamilyCode(Long tenantId, TenantMappingJobId jobId,
			String familyCode);

	void clearCache(Long tenantId, TenantMappingJobId id);

	boolean isExistedGroupPath(Long tenantId, TenantMappingJobId jobId, String groupPath);

	void createGroupCacheEntry(Long tenantId, TenantMappingJobId jobId, String groupPath);

	String swapCustomerNoToMainUserIfPossible(Long tenantId, TenantMappingJobId jobId, String customerNo);
}
