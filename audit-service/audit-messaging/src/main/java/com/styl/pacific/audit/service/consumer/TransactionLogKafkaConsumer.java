/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.audit.service.consumer;

import com.styl.pacific.audit.service.domain.dto.command.CreateTransactionLogCommand;
import com.styl.pacific.audit.service.domain.service.TransactionLogService;
import com.styl.pacific.audit.service.mapper.AuditMessageMapper;
import com.styl.pacific.kafka.audit.avro.model.CreateTransactionLogAvroCommand;
import com.styl.pacific.kafka.consumer.KafkaConsumer;
import jakarta.annotation.PostConstruct;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@ConditionalOnProperty(name = "pacific.kafka.audit-service.consumers.create-transaction-log.enabled", havingValue = "true")
public class TransactionLogKafkaConsumer implements KafkaConsumer<UUID, CreateTransactionLogAvroCommand> {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	private final TransactionLogService transactionLogService;

	@PostConstruct
	public void init() {
		logger.info("Constructed TransactionLogKafkaConsumer");
	}

	@Override
	@RetryableTopic(backoff = @Backoff(delayExpression = "#{${pacific.kafka.audit-service.consumers.retry-interval-ms}}"), attempts = "${pacific.kafka.audit-service.consumers.retry-attempts}", autoCreateTopics = "false", dltTopicSuffix = "-${pacific.kafka.audit-service.consumers.create-transaction-log.topic-name}-dlt", retryTopicSuffix = "-${pacific.kafka.audit-service.consumers.create-transaction-log.topic-name}-retry")
	@KafkaListener(id = "${pacific.kafka.audit-service.consumers.create-transaction-log.group-id}", topics = "${pacific.kafka.audit-service.consumers.create-transaction-log.topic-name}")
	public void receive(CreateTransactionLogAvroCommand avroCommand, UUID key, Integer partition, Long offset) {
		CreateTransactionLogCommand command = AuditMessageMapper.INSTANCE.toDomainCommand(avroCommand);
		logger.debug("Received CreateTransactionLogCommand with tenantId: {}, txId {}, requestId {}", command
				.tenantId(), command.txId(), command.requestId());
		transactionLogService.create(command);
	}
}
