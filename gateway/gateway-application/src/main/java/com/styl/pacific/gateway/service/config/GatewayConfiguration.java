/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.gateway.service.config;

import java.util.Optional;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Configuration
public class GatewayConfiguration {

	@Bean
	KeyResolver keyResolver() {
		return exchange -> {
			// Extracting the client IP address from request
			String ip = Optional.ofNullable(exchange.getRequest()
					.getRemoteAddress())
					.map(address -> address.getAddress()
							.getHostAddress())
					.orElse("unknown");
			// Use custom key resolver to resolve IP-based keys
			return Mono.just(ip);
		};
	}
}
