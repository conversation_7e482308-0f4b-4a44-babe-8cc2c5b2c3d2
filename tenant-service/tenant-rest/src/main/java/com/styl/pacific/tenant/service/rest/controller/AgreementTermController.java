/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.rest.controller;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.tenant.service.domain.dto.SaveAgreementTermCommand;
import com.styl.pacific.tenant.service.domain.entity.AgreementTerm;
import com.styl.pacific.tenant.service.domain.port.input.service.AgreementTermDomainService;
import com.styl.pacific.tenant.service.rest.mapper.TenantControllerMapper;
import com.styl.pacific.tenant.service.shared.http.api.AgreementTermApi;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.SaveAgreementTermRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.AgreementTermResponse;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@AllArgsConstructor
public class AgreementTermController implements AgreementTermApi {

	private final AgreementTermDomainService agreementTermDomainService;

	private final RequestContext requestContext;

	@Override
	public AgreementTermResponse getAgreementTerm() {
		AgreementTerm agreementTerm = agreementTermDomainService.getAgreementTerm(requestContext.getTenantId());
		return TenantControllerMapper.INSTANCE.toAgreementTermResponse(agreementTerm);
	}

	public AgreementTermResponse saveAgreementTerm(SaveAgreementTermRequest request) {
		SaveAgreementTermCommand command = TenantControllerMapper.INSTANCE.toSaveAgreementTermCommand(request,
				requestContext.getTenantId());
		AgreementTerm agreementTerm = agreementTermDomainService.saveAgreementTerm(command);
		return TenantControllerMapper.INSTANCE.toAgreementTermResponse(agreementTerm);
	}
}
