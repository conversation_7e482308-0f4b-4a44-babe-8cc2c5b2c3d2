/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.rest.controller;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.tenant.service.domain.dto.SaveCommunicationSettingsCommand;
import com.styl.pacific.tenant.service.domain.entity.CommunicationSettings;
import com.styl.pacific.tenant.service.domain.entity.FeedbackEmail;
import com.styl.pacific.tenant.service.domain.port.input.service.CommunicationSettingsDomainService;
import com.styl.pacific.tenant.service.rest.mapper.TenantControllerMapper;
import com.styl.pacific.tenant.service.shared.http.api.CommunicationSettingsApi;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.SaveCommunicationSettingsRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.CommunicationSettingsResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.FeedbackEmailResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.IntercomTokenResponse;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@AllArgsConstructor
public class CommunicationSettingsController implements CommunicationSettingsApi {

	private final CommunicationSettingsDomainService communicationSettingsDomainService;

	private final RequestContext requestContext;

	@Override
	public FeedbackEmailResponse getFeedbackEmail() {
		FeedbackEmail feedbackEmail = communicationSettingsDomainService.getFeedbackEmail(requestContext.getTenantId());
		return TenantControllerMapper.INSTANCE.toFeedbackEmailResponse(feedbackEmail);
	}

	@Override
	public CommunicationSettingsResponse getCommunicationSettings() {
		CommunicationSettings communicationSettings = communicationSettingsDomainService.getCommunicationSettings(
				requestContext.getTenantId());
		return TenantControllerMapper.INSTANCE.toCommunicationSettingsResponse(communicationSettings);
	}

	@Override
	public CommunicationSettingsResponse saveCommunicationSettings(SaveCommunicationSettingsRequest request) {
		SaveCommunicationSettingsCommand command = TenantControllerMapper.INSTANCE.toSaveCommunicationSettingsCommand(
				request, requestContext.getTenantId());
		CommunicationSettings communicationSettings = communicationSettingsDomainService.saveCommunicationSettings(
				command);
		return TenantControllerMapper.INSTANCE.toCommunicationSettingsResponse(communicationSettings);
	}

	@Override
	public IntercomTokenResponse generateIntercomToken() {
		String appId = communicationSettingsDomainService.getCommunicationSettings(requestContext.getTenantId())
				.getIntercomAppId();
		String token = communicationSettingsDomainService.generateIntercomToken(requestContext.getTenantId(),
				requestContext.getTokenClaim());
		return IntercomTokenResponse.builder()
				.appId(appId)
				.token(token)
				.build();
	}
}
