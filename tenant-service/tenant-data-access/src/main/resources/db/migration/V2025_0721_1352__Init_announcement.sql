CREATE
    TABLE
        tb_announcement(
            id BIGINT PRIMARY KEY NOT NULL,
            tenant_id BIGINT NOT NULL,
            content TEXT,
            start_display_at TIMESTAMP(6) WITH TIME ZONE,
            stop_display_at TIMESTAMP(6) WITH TIME ZONE,
            is_enabled BOOLEAN,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            deleted_at TIMESTAMP(6) WITH TIME ZONE
        );

CREATE
    UNIQUE INDEX IF NOT EXISTS tb_announcement_tenant_id_uidx ON
    tb_announcement(tenant_id);