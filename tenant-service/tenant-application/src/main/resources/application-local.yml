---
server:
  port: 9201
logging:
  level:
    com.styl.pacific: TRACE
spring:
  jpa:
    hibernate.ddl-auto: none
    open-in-view: false
    show-sql: true
  datasource:
    url: *********************************************************************
    username: postgres
    password: postgres
    platform: postgres
    driver-class-name: org.postgresql.Driver
    replica:
      url: *********************************************************************
      username: postgres
      password: postgres
      platform: postgres
      driver-class-name: org.postgresql.Driver
  flyway:
    enabled: true
pacific:
  tracing:
    enabled: true
    otlp:
      endpoint: http://localhost:4317
kafka-config:
  bootstrap-servers: "localhost:19092, localhost:29092, localhost:39092"
  schema-registry-url-key: schema.registry.url
  schema-registry-url: http://localhost:8081
