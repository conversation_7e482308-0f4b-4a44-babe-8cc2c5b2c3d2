/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.data.access;

import static org.assertj.core.api.Assertions.assertThat;

import com.styl.pacific.tenant.service.data.access.entity.TenantEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TenantEntityTest {

	private TenantEntity tenantEntity;
	private TenantEntity identicalTenantEntity;
	private TenantEntity differentTenantEntity;

	@BeforeEach
	public void setUp() {
		tenantEntity = new TenantEntity();
		tenantEntity.setId(1L);
		tenantEntity.setName("Test Tenant");
		tenantEntity.setEmail("<EMAIL>");
		tenantEntity.setPhoneNumber("1234567890");
		tenantEntity.setAddressLine1("123 Main St");
		tenantEntity.setAddressLine2("Apt 4B");
		tenantEntity.setCity("Test City");
		tenantEntity.setCountry("Test Country");
		tenantEntity.setPostalCode("12345");

		identicalTenantEntity = new TenantEntity();
		identicalTenantEntity.setId(1L);
		identicalTenantEntity.setName("Test Tenant");
		identicalTenantEntity.setEmail("<EMAIL>");
		identicalTenantEntity.setPhoneNumber("1234567890");
		identicalTenantEntity.setAddressLine1("123 Main St");
		identicalTenantEntity.setAddressLine2("Apt 4B");
		identicalTenantEntity.setCity("Test City");
		identicalTenantEntity.setCountry("Test Country");
		identicalTenantEntity.setPostalCode("12345");

		differentTenantEntity = new TenantEntity();
		differentTenantEntity.setId(2L);
		differentTenantEntity.setName("Different Tenant");
		differentTenantEntity.setEmail("<EMAIL>");
		differentTenantEntity.setPhoneNumber("0987654321");
		differentTenantEntity.setAddressLine1("456 Another St");
		differentTenantEntity.setAddressLine2("Apt 5C");
		differentTenantEntity.setCity("Another City");
		differentTenantEntity.setCountry("Another Country");
		differentTenantEntity.setPostalCode("67890");
	}

	@Test
	void testEqualsWhenIdenticalObjectThenReturnTrue() {
		// Act & Assert
		assertThat(tenantEntity).isEqualTo(identicalTenantEntity);
	}

	@Test
	void testEqualsWhenDifferentObjectThenReturnFalse() {
		// Act & Assert
		assertThat(tenantEntity).isNotEqualTo(differentTenantEntity);
	}

	@Test
	void testEqualsWhenNullThenReturnFalse() {
		// Act & Assert
		assertThat(tenantEntity).isNotEqualTo(null);
	}

	@Test
	void testEqualsWhenDifferentClassThenReturnFalse() {
		// Act & Assert
		assertThat(tenantEntity).isNotEqualTo(new Object());
	}

	@Test
	void testEqualsWhenSameFieldsThenReturnTrue() {
		// Arrange
		TenantEntity anotherIdenticalTenantEntity = new TenantEntity();
		anotherIdenticalTenantEntity.setId(1L);
		anotherIdenticalTenantEntity.setName("Test Tenant");
		anotherIdenticalTenantEntity.setEmail("<EMAIL>");
		anotherIdenticalTenantEntity.setPhoneNumber("1234567890");
		anotherIdenticalTenantEntity.setAddressLine1("123 Main St");
		anotherIdenticalTenantEntity.setAddressLine2("Apt 4B");
		anotherIdenticalTenantEntity.setCity("Test City");
		anotherIdenticalTenantEntity.setCountry("Test Country");
		anotherIdenticalTenantEntity.setPostalCode("12345");

		// Act & Assert
		assertThat(tenantEntity).isEqualTo(anotherIdenticalTenantEntity);
	}
}