/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.rest.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.application.rest.dto.ErrorResponse;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.domain.exception.GlobalErrorCode;
import com.styl.pacific.tenant.service.config.IntegrationTestConfiguration;
import com.styl.pacific.tenant.service.domain.dto.BusinessFeatureItem;
import com.styl.pacific.tenant.service.domain.dto.SubmitBusinessFeatureCommand;
import com.styl.pacific.tenant.service.domain.output.publisher.TenantCreatedEventPublisher;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.CreateTenantRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.UpdateTenantRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.ListBusinessFeatureMasterDataResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.ListBusinessFeatureResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.ListTenantsResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.reactive.server.FluxExchangeResult;

/**
 * <AUTHOR>
 */

@MockBean(TenantCreatedEventPublisher.class)
@TestMethodOrder(OrderAnnotation.class)
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
class TenantControllerIntegrationTest extends BaseWebClientWithDbTest {

	public static final String DOMAIN_TEST = "pacific.styl.solutions";
	public static final String TENANT_NAME = "Test Tenant 234";
	private static Long tenantId = null;
	private static Long createdAt = null;
	private final ObjectMapper objectMapper = new ObjectMapper();

	private static Stream<Arguments> provideParametersForFindTenantsTest() {
		return Stream.of(Arguments.of(0, 10, "name", "asc"), Arguments.of(1, 20, "email", "desc"), Arguments.of(2, 30,
				"phoneNumber", "asc"), Arguments.of(6, 70, "status", "asc"), Arguments.of(7, 80, "createdAt", "desc"),
				Arguments.of(8, 90, "updatedAt", "asc"), Arguments.of(9, 100, "name", "desc"), Arguments.of(9, 250,
						"name", "desc"), Arguments.of(9999, 100, "name", "desc")
		// Add more sets of parameters if needed
		);
	}

	@Order(1)
	@Test
	void testCreateTenantWhenValidCommandThenReturnTenantResponse() throws Exception {
		// Arrange
		CreateTenantRequest command = CreateTenantRequest.builder()
				.name(TENANT_NAME)
				.businessRegNo("1234567890")
				.businessType("SCHOOL")
				.email("<EMAIL>")
				.phoneNumber("+6583939482")
				.addressLine1("123 Test St")
				.addressLine2("Apt 4")
				.city("Singapore")
				.country("SG")
				.postalCode("123456")
				.logoPath("bucket:image/logo.png")
				.build();

		// Act & Assert
		FluxExchangeResult<TenantResponse> response = webClient.post()
				.uri("/api/tenant/tenants")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(objectMapper.writeValueAsString(command))
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(TenantResponse.class);

		TenantResponse tenant = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenant);
		tenantId = tenant.getTenantId();
		logger.info("Tenant ID created: {}", tenantId);
		Assertions.assertNotNull(tenantId);
		assertEquals(TENANT_NAME, tenant.getName());
		assertEquals("1234567890", tenant.getBusinessRegNo());
		assertEquals("SCHOOL", tenant.getBusinessType());
		assertEquals("<EMAIL>", tenant.getEmail());
		assertEquals("+6583939482", tenant.getPhoneNumber());
		assertEquals("123 Test St", tenant.getAddressLine1());
		assertEquals("Apt 4", tenant.getAddressLine2());
		assertEquals("Singapore", tenant.getCity());
		assertEquals("SG", tenant.getCountry()
				.getCountryCode());
		assertEquals("Singapore", tenant.getCountry()
				.getCountryName());
		assertEquals("123456", tenant.getPostalCode());
		assertEquals("NEW", tenant.getStatus());
		assertEquals("bucket:image/logo.png", tenant.getLogo()
				.path());
		assertNotNull(tenant.getLogo()
				.url());
		assertTrue(tenant.getLogo()
				.url()
				.startsWith("http"));
		assertNull(tenant.getActivatedAt());
		assertTrue(tenant.getCreatedAt() > 0, "createdAt is not greater than 0");
		assertTrue(tenant.getUpdatedAt() > 0, "updatedAt is not greater than 0");

	}

	@Order(2)
	@Test
	void testGetTenantWhenValidTenantIdThenReturnTenantResponse() {

		// Validate the previous test case
		logger.info("Tenant ID to Get: {}", tenantId);
		Assertions.assertNotNull(tenantId);

		// Act & Assert
		FluxExchangeResult<TenantResponse> response = webClient.get()
				.uri("/api/tenant/tenants/{tenantId}", tenantId)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(TenantResponse.class);

		TenantResponse tenantResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenantResponse);
		assertEquals(tenantId, tenantResponse.getTenantId());
		assertEquals(TENANT_NAME, tenantResponse.getName());
		assertEquals("<EMAIL>", tenantResponse.getEmail());
		assertEquals("+6583939482", tenantResponse.getPhoneNumber());
		assertEquals("123 Test St", tenantResponse.getAddressLine1());
		assertEquals("Apt 4", tenantResponse.getAddressLine2());
		assertEquals("Singapore", tenantResponse.getCity());
		assertEquals("Singapore", tenantResponse.getCountry()
				.getCountryName());
		assertEquals("123456", tenantResponse.getPostalCode());
		assertTrue(tenantResponse.getCreatedAt() > 0, "createdAt is not greater than 0");
		assertTrue(tenantResponse.getUpdatedAt() > 0, "updatedAt is not greater than 0");
	}

	@Order(3)
	@Test
	void testDeleteTenantWhenValidTenantIdThenReturnNoContent() {

		// Validate the previous test case
		logger.info("Tenant ID to delete: {}", tenantId);
		Assertions.assertNotNull(tenantId);

		// Act & Assert
		webClient.delete()
				.uri("/api/tenant/tenants/{tenantId}", tenantId)
				.exchange()
				.expectStatus()
				.isNoContent();
	}

	@Order(4)
	@Test
	void testGetTenantWhenIncorrectTenantIdThenReturnBadRequest() {

		// Act & Assert
		FluxExchangeResult<ErrorResponse> response = webClient.get()
				.uri("/api/tenant/tenants/{tenantId}", tenantId)
				.exchange()
				.expectStatus()
				.isBadRequest()
				.returnResult(ErrorResponse.class);

		ErrorResponse errorResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(errorResponse);
		assertEquals(GlobalErrorCode.TENANT_NOT_FOUND.getValue(), errorResponse.getCode());
	}

	@Order(5)
	@Test
	void testDeleteTenantWhenNonExistTenantIdThenReturnNoContent() {

		// Act & Assert
		webClient.delete()
				.uri("/api/tenant/tenants/{tenantId}", tenantId)
				.exchange()
				.expectStatus()
				.isNoContent();
	}

	// test case to update a tenant after deletion
	@Order(6)
	@Test
	void testUpdateTenantWhenTenantDeletedThenReturnBadRequest() throws Exception {
		// Arrange
		CreateTenantRequest command = CreateTenantRequest.builder()
				.name(TENANT_NAME)
				.businessRegNo("191817161514")
				.businessType("CORPORATE")
				.email("<EMAIL>")
				.phoneNumber("+6583939482")
				.addressLine1("123 Test St")
				.addressLine2("Apt 4")
				.city("Singapore")
				.country("SG")
				.postalCode("123456")
				.build();

		// Act & Assert
		FluxExchangeResult<ErrorResponse> response = webClient.put()
				.uri("/api/tenant/tenants/{tenantId}", tenantId)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(objectMapper.writeValueAsString(command))
				.exchange()
				.expectStatus()
				.isBadRequest()
				.returnResult(ErrorResponse.class);

		ErrorResponse error = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(error);
		assertEquals(GlobalErrorCode.TENANT_NOT_FOUND.getValue(), error.getCode());
		assertEquals("Tenant not found with id: " + tenantId, error.getDetails()
				.getFirst());
	}

	// test case to create another tenant
	@Order(7)
	@Test
	void testCreateAnotherTenantWhenValidCommandThenReturnTenantResponse() throws Exception {
		// Arrange
		CreateTenantRequest command = CreateTenantRequest.builder()
				.name("Another Tenant")
				.businessRegNo("1234567890")
				.businessType("SCHOOL")
				.email("<EMAIL>")
				.phoneNumber("+6583939482")
				.addressLine1("123 Another St")
				.addressLine2("Apt 4")
				.city("Singapore")
				.country("SG")
				.postalCode("123456")
				.build();

		// Act & Assert
		FluxExchangeResult<TenantResponse> response = webClient.post()
				.uri("/api/tenant/tenants")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(objectMapper.writeValueAsString(command))
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(TenantResponse.class);

		TenantResponse tenant = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenant);
		Assertions.assertNotNull(tenant.getTenantId());
		tenantId = tenant.getTenantId();
		assertEquals("Another Tenant", tenant.getName());
		assertEquals("1234567890", tenant.getBusinessRegNo());
		assertEquals("SCHOOL", tenant.getBusinessType());
		//status
		assertEquals("NEW", tenant.getStatus());
		//activation Date
		assertNull(tenant.getActivatedAt());
		assertEquals("<EMAIL>", tenant.getEmail());
		assertEquals("+6583939482", tenant.getPhoneNumber());
		assertEquals("123 Another St", tenant.getAddressLine1());
		assertEquals("Apt 4", tenant.getAddressLine2());
		assertEquals("Singapore", tenant.getCity());
		assertEquals("Singapore", tenant.getCountry()
				.getCountryName());
		assertEquals("123456", tenant.getPostalCode());
		assertTrue(tenant.getCreatedAt() > 0, "createdAt is not greater than 0");
		assertTrue(tenant.getUpdatedAt() > 0, "updatedAt is not greater than 0");

		createdAt = tenant.getCreatedAt();
	}

	// test case to test the updateTenant API
	@Order(8)
	@Test
	void testUpdateTenantWhenValidCommandThenReturnTenantResponse() throws Exception {
		// Validate the previous test case
		Assertions.assertNotNull(createdAt);

		// Arrange
		UpdateTenantRequest command = UpdateTenantRequest.builder()
				.name("Another Tenant")
				.businessRegNo("191817161514")
				.businessType("CORPORATE")
				.email("<EMAIL>")
				.phoneNumber("+6583939482")
				.addressLine1("123 Test St")
				.addressLine2("Apt 4")
				.city("Singapore")
				.country("SG")
				.postalCode("123456")
				.build();

		// Act & Assert
		FluxExchangeResult<TenantResponse> response = webClient.put()
				.uri("/api/tenant/tenants/{tenantId}", tenantId)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(objectMapper.writeValueAsString(command))
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(TenantResponse.class);

		TenantResponse tenant = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenant);
		assertEquals(tenantId, tenant.getTenantId());
		assertEquals("Another Tenant", tenant.getName());
		assertEquals("191817161514", tenant.getBusinessRegNo());
		assertEquals("CORPORATE", tenant.getBusinessType());
		assertEquals("NEW", tenant.getStatus());
		assertNull(tenant.getActivatedAt());
		assertEquals("<EMAIL>", tenant.getEmail());
		assertEquals("+6583939482", tenant.getPhoneNumber());
		assertEquals("123 Test St", tenant.getAddressLine1());
		assertEquals("Apt 4", tenant.getAddressLine2());
		assertEquals("Singapore", tenant.getCity());
		assertEquals("Singapore", tenant.getCountry()
				.getCountryName());
		assertEquals("123456", tenant.getPostalCode());
		assertTrue(tenant.getUpdatedAt() > createdAt, "updatedAt is not greater than createdAt");

	}

	// test case to test getTenant API for the updated tenant
	@Order(9)
	@Test
	void testGetTenantWhenNewTenantIdThenReturnTenantResponse() {

		// Validate the previous test case
		logger.info("Tenant ID: {}", tenantId);
		Assertions.assertNotNull(tenantId);

		// Act & Assert
		FluxExchangeResult<TenantResponse> response = webClient.get()
				.uri("/api/tenant/tenants/{tenantId}", tenantId)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(TenantResponse.class);

		TenantResponse tenantResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenantResponse);
		assertEquals(tenantId, tenantResponse.getTenantId());
		assertEquals("Another Tenant", tenantResponse.getName());
		assertEquals("<EMAIL>", tenantResponse.getEmail());
		assertEquals("+6583939482", tenantResponse.getPhoneNumber());
		assertEquals("123 Test St", tenantResponse.getAddressLine1());
		assertEquals("Apt 4", tenantResponse.getAddressLine2());
		assertEquals("Singapore", tenantResponse.getCity());
		assertEquals("Singapore", tenantResponse.getCountry()
				.getCountryName());
		assertEquals("123456", tenantResponse.getPostalCode());
		assertTrue(tenantResponse.getCreatedAt() > 0, "createdAt is not greater than 0");
		assertTrue(tenantResponse.getUpdatedAt() > tenantResponse.getCreatedAt(),
				"updatedAt is not greater than createdAt");
	}

	// test case add multiple tenants
	@Order(10)
	@Test
	void testCreateMultipleTenantsWhenValidCommandThenReturnTenantResponse() throws Exception {
		// Arrange
		// loop and add tenant
		for (int i = 1; i <= 101; i++) {
			CreateTenantRequest command = CreateTenantRequest.builder()
					.name("Tenant Dummy" + i)
					.businessRegNo("1234567890")
					.businessType("SCHOOL")
					.email("tenant" + i
							+ "@tenant.com")
					.phoneNumber("+6583939482")
					.addressLine1("123 Test St")
					.addressLine2("Apt 4")
					.city("Singapore")
					.country("SG")
					.postalCode("123456")
					.build();
			// Act & Assert
			FluxExchangeResult<TenantResponse> response = webClient.post()
					.uri("/api/tenant/tenants")
					.contentType(MediaType.APPLICATION_JSON)
					.bodyValue(objectMapper.writeValueAsString(command))
					.exchange()
					.expectStatus()
					.isOk()
					.returnResult(TenantResponse.class);

			TenantResponse tenant = response.getResponseBody()
					.blockFirst();
			Assertions.assertNotNull(tenant);
			Assertions.assertNotNull(tenant.getTenantId());
			assertEquals("Tenant Dummy" + i, tenant.getName());
		}
	}

	// test case to get all tenants
	@Order(11)
	@ParameterizedTest
	@MethodSource("provideParametersForFindTenantsTest")
	void testFindTenantsWhenValidQueryThenReturnListTenantsResponse(int page, int size, String sortField,
			String sortDirection) {
		// Arrange
		// Act & Assert
		// add params: page, size, sortField, sortDirection
		FluxExchangeResult<ListTenantsResponse> response = webClient.get()
				.uri(uriBuilder -> uriBuilder.path("/api/tenant/tenants")
						.queryParam("page", page)
						.queryParam("size", size)
						.queryParam("sortFields", sortField)
						.queryParam("sortDirection", sortDirection)
						.build())
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(ListTenantsResponse.class);

		// Assert
		ListTenantsResponse tenants = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenants);
		Assertions.assertNotNull(tenants.getContent());
		assertTrue(tenants.getTotalElements() > 100);
		assertEquals(page, tenants.getPage());
		assertEquals(Math.floor((double) tenants.getTotalElements() / size) + 1, tenants.getTotalPages());
		assertEquals(sortField + ","
				+ sortDirection.toUpperCase(), tenants.getSort()
						.getFirst());
	}

	// test case to get all tenants with name query
	@Order(13)
	@Test
	void testFindTenantsWhenValidNameQueryThenReturnListTenantsResponse() {
		// Arrange
		// Act & Assert
		FluxExchangeResult<ListTenantsResponse> response = webClient.get()
				.uri(uriBuilder -> uriBuilder.path("/api/tenant/tenants")
						.queryParam("filter.name", "Tenant Dummy")
						.queryParam("page", 0)
						.queryParam("size", 10)
						.queryParam("sortFields", "name")
						.queryParam("sortDirection", "asc")
						.build())
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(ListTenantsResponse.class);

		// Assert
		ListTenantsResponse tenants = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenants);
		Assertions.assertNotNull(tenants.getContent());
		assertEquals(101, tenants.getTotalElements());
		assertEquals(0, tenants.getPage());
		assertEquals(11, tenants.getTotalPages());
		assertTrue(("name,asc").equalsIgnoreCase(tenants.getSort()
				.getFirst()));
	}

	@Order(14)
	@Test
	void testFindTenantsWhenValidNameQueryThenReturnListTenantsResponseWithOneItem() {
		// Arrange
		// Act & Assert
		FluxExchangeResult<ListTenantsResponse> response = webClient.get()
				.uri(uriBuilder -> uriBuilder.path("/api/tenant/tenants")
						.queryParam("filter.name", "99")
						.queryParam("page", 0)
						.queryParam("size", 10)
						.queryParam("sortFields", "name")
						.queryParam("sortDirection", "asc")
						.build())
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(ListTenantsResponse.class);

		// Assert
		ListTenantsResponse tenants = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenants);
		Assertions.assertNotNull(tenants.getContent());
		assertEquals(1, tenants.getTotalElements());
		assertEquals(0, tenants.getPage());
		assertEquals(1, tenants.getTotalPages());
		assertTrue(("name,asc").equalsIgnoreCase(tenants.getSort()
				.getFirst()));
	}

	@Order(15)
	@Test
	void testSubmitBusinessFeatureWhenValidCommandThenReturnBusinessFeatureResponse() throws Exception {
		// Arrange
		BusinessFeatureItem businessFeatureItem = BusinessFeatureItem.builder()
				.code("customer_self_registration")
				.enabled(true)
				.build();
		SubmitBusinessFeatureCommand submitBusinessFeatureCommand = SubmitBusinessFeatureCommand.builder()
				.tenantId(tenantId)
				.items(List.of(businessFeatureItem))
				.build();

		// Act & Assert
		FluxExchangeResult<ListBusinessFeatureResponse> responses = webClient.post()
				.uri("/api/tenant/tenants/{id}/businessFeatures/submit", tenantId)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(objectMapper.writeValueAsString(submitBusinessFeatureCommand))
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(ListBusinessFeatureResponse.class);

		ListBusinessFeatureResponse businessFeatureResponses = responses.getResponseBody()
				.blockFirst();
		assertNotNull(businessFeatureResponses);
		assertNotNull(businessFeatureResponses.getContent());
		assertEquals(1, businessFeatureResponses.getContent()
				.size());
		assertEquals(businessFeatureItem.getCode(), businessFeatureResponses.getContent()
				.getFirst()
				.getCode());
		assertEquals(true, businessFeatureResponses.getContent()
				.getFirst()
				.isEnabled());
	}

	@Order(16)
	@Test
	void testGetBusinessFeatureByTenantIdThenReturnBusinessFeatureResponse() {

		// Act & Assert
		FluxExchangeResult<ListBusinessFeatureResponse> responses = webClient.get()
				.uri("/api/tenant/tenants/{id}/businessFeatures", tenantId)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(ListBusinessFeatureResponse.class);

		ListBusinessFeatureResponse businessFeatureResponses = responses.getResponseBody()
				.blockFirst();
		assertNotNull(businessFeatureResponses);
		assertNotNull(businessFeatureResponses.getContent());
		assertEquals(1, businessFeatureResponses.getContent()
				.size());
	}

	@Order(17)
	@Test
	void testGetBusinessFeatureMasterDataThenReturnBusinessFeatureMasterDataResponse() {

		// Act & Assert
		FluxExchangeResult<ListBusinessFeatureMasterDataResponse> responses = webClient.get()
				.uri("/api/tenant/tenants/businessFeatures")
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(ListBusinessFeatureMasterDataResponse.class);

		ListBusinessFeatureMasterDataResponse businessFeatureResponses = responses.getResponseBody()
				.blockFirst();
		assertNotNull(businessFeatureResponses);
		assertNotNull(businessFeatureResponses.getContent());
	}

}
