/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.data.access;

import static org.assertj.core.api.Assertions.assertThat;

import com.styl.pacific.tenant.service.data.access.entity.BusinessFeatureEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class BusinessFeatureEntityTest {

	private BusinessFeatureEntity businessFeatureEntity;
	private BusinessFeatureEntity differentBusinessFeatureEntity;

	@BeforeEach
	public void setUp() {
		businessFeatureEntity = new BusinessFeatureEntity();
		businessFeatureEntity.setId(1L);
		businessFeatureEntity.setName("C-Portal");
		businessFeatureEntity.setCode("code1");
		businessFeatureEntity.setDescription("description");
		businessFeatureEntity.setEnabled(true);

		differentBusinessFeatureEntity = new BusinessFeatureEntity();
		differentBusinessFeatureEntity.setId(2L);
		differentBusinessFeatureEntity.setName("C-Portal");
		differentBusinessFeatureEntity.setCode("code1234");
		differentBusinessFeatureEntity.setDescription("description123");
		differentBusinessFeatureEntity.setEnabled(true);
	}

	@Test
	void testEqualsWhenDifferentObjectThenReturnFalse() {
		// Act & Assert
		assertThat(businessFeatureEntity).isNotEqualTo(differentBusinessFeatureEntity);
	}

	@Test
	void testEqualsWhenNullThenReturnFalse() {
		// Act & Assert
		assertThat(businessFeatureEntity).isNotEqualTo(null);
	}

	@Test
	void testEqualsWhenDifferentClassThenReturnFalse() {
		// Act & Assert
		assertThat(businessFeatureEntity).isNotEqualTo(new Object());
	}

	@Test
	void testEqualsWhenSameFieldsThenReturnTrue() {
		// Arrange
		BusinessFeatureEntity businessFeatureEntityOther = new BusinessFeatureEntity();
		businessFeatureEntityOther.setId(1L);
		businessFeatureEntityOther.setName("C-Portal");
		businessFeatureEntityOther.setCode("code1");
		businessFeatureEntityOther.setDescription("description");
		businessFeatureEntityOther.setEnabled(true);

		// Act & Assert
		assertThat(businessFeatureEntity).isEqualTo(businessFeatureEntityOther);
	}
}