/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.rest.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.application.rest.dto.ErrorResponse;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.domain.exception.GlobalErrorCode;
import com.styl.pacific.tenant.service.config.IntegrationTestConfiguration;
import com.styl.pacific.tenant.service.domain.output.publisher.TenantCreatedEventPublisher;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.CreateTenantRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.UpdateTenantSettingsRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.DomainAvailabilityResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.ListDomainResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantInfoResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import java.util.Objects;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.reactive.server.FluxExchangeResult;

/**
 * <AUTHOR>
 */

@MockBean(TenantCreatedEventPublisher.class)
@TestMethodOrder(OrderAnnotation.class)
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
class TenantDomainControllerIntegrationTest extends BaseWebClientWithDbTest {

	public static final String DOMAIN_TEST = "pacific.styl.solutions";
	public static final String TENANT_NAME = "Test Tenant 123";
	private static Long tenantId = null;
	private static String domainId = null;
	private static String alternativeDomainId = null;
	private final ObjectMapper objectMapper = new ObjectMapper();

	@Order(1)
	@Test
	void testCreateTenantWhenValidCommandThenReturnTenantResponse() throws Exception {
		// Arrange
		CreateTenantRequest command = CreateTenantRequest.builder()
				.name(TENANT_NAME)
				.businessRegNo("1234567890")
				.businessType("SCHOOL")
				.email("<EMAIL>")
				.phoneNumber("+6583939482")
				.addressLine1("123 Test St")
				.addressLine2("Apt 4")
				.city("Singapore")
				.country("SG")
				.postalCode("123456")
				.logoPath("bucket:image/logo.png")
				.build();

		// Act & Assert
		FluxExchangeResult<TenantResponse> response = webClient.post()
				.uri("/api/tenant/tenants")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(objectMapper.writeValueAsString(command))
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(TenantResponse.class);

		TenantResponse tenant = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenant);
		tenantId = tenant.getTenantId();
		logger.info("Tenant ID created: {}", tenantId);
		Assertions.assertNotNull(tenantId);
		assertEquals(TENANT_NAME, tenant.getName());
		assertEquals("1234567890", tenant.getBusinessRegNo());
		assertEquals("SCHOOL", tenant.getBusinessType());
		assertEquals("<EMAIL>", tenant.getEmail());
		assertEquals("+6583939482", tenant.getPhoneNumber());
		assertEquals("123 Test St", tenant.getAddressLine1());
		assertEquals("Apt 4", tenant.getAddressLine2());
		assertEquals("Singapore", tenant.getCity());
		assertEquals("SG", tenant.getCountry()
				.getCountryCode());
		assertEquals("Singapore", tenant.getCountry()
				.getCountryName());
		assertEquals("123456", tenant.getPostalCode());
		assertEquals("NEW", tenant.getStatus());
		assertEquals("bucket:image/logo.png", tenant.getLogo()
				.path());
		assertNotNull(tenant.getLogo()
				.url());
		assertTrue(tenant.getLogo()
				.url()
				.startsWith("http"));
		assertNull(tenant.getActivatedAt());
		assertTrue(tenant.getCreatedAt() > 0, "createdAt is not greater than 0");
		assertTrue(tenant.getUpdatedAt() > 0, "updatedAt is not greater than 0");

	}

	@Order(2)
	@Test
	void updateTenantSettingsWhenValidRequestThenReturnUpdatedSettings() throws Exception {
		// Arrange
		UpdateTenantSettingsRequest request = UpdateTenantSettingsRequest.builder()
				.defaultDomain(DOMAIN_TEST)
				.currency("SGD")
				.dateFormat("dd/MM/yyyy")
				.timeFormat("HH:mm:ss")
				.timeZone("Asia/Singapore")
				.build();

		// Act & Assert
		FluxExchangeResult<TenantSettingsResponse> response = webClient.put()
				.uri("/api/tenant/tenants/{tenantId}/settings", tenantId)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(objectMapper.writeValueAsString(request))
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(TenantSettingsResponse.class);

		TenantSettingsResponse tenantSettings = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenantSettings);
		Assertions.assertEquals(DOMAIN_TEST, tenantSettings.getDefaultDomain());
		Assertions.assertEquals("SGD", tenantSettings.getCurrency()
				.getCurrencyCode());
		Assertions.assertEquals("dd/MM/yyyy", tenantSettings.getDateFormat());
		Assertions.assertEquals("HH:mm:ss", tenantSettings.getTimeFormat());
		Assertions.assertEquals("Asia/Singapore", tenantSettings.getTimeZone()
				.getZoneId());

	}

	@Order(3)
	@Test
	void checkAvailabilityOfExistingDomainThenReturnFalse() {

		// Act & Assert
		FluxExchangeResult<DomainAvailabilityResponse> response = webClient.get()
				.uri(uriBuilder -> uriBuilder.path("/api/tenant/domains/availability")
						.queryParam("domain", DOMAIN_TEST)
						.build())
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(DomainAvailabilityResponse.class);

		boolean isAvailable = Objects.requireNonNull(response.getResponseBody()
				.blockFirst())
				.isAvailable();
		Assertions.assertFalse(isAvailable);
	}

	@Order(4)
	@Test
	void checkAvailabilityOfNonExistingDomainThenReturnTrue() {

		// Act & Assert
		FluxExchangeResult<DomainAvailabilityResponse> response = webClient.get()
				.uri(uriBuilder -> uriBuilder.path("/api/tenant/domains/availability")
						.queryParam("domain", "non-existing.styl.solutions")
						.build())
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(DomainAvailabilityResponse.class);

		boolean isAvailable = Objects.requireNonNull(response.getResponseBody()
				.blockFirst())
				.isAvailable();
		Assertions.assertTrue(isAvailable);
	}

	@Order(5)
	@Test
	void checkAvailabilityOfInvalidDomainThenReturnFalse() {

		// Act & Assert
		FluxExchangeResult<DomainAvailabilityResponse> response = webClient.get()
				.uri(uriBuilder -> uriBuilder.path("/api/tenant/domains/availability")
						.queryParam("domain", "styl.sg")
						.build())
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(DomainAvailabilityResponse.class);

		boolean isAvailable = Objects.requireNonNull(response.getResponseBody()
				.blockFirst())
				.isAvailable();
		Assertions.assertFalse(isAvailable);
	}

	@Order(6)
	@ParameterizedTest
	@ValueSource(strings = { "id.styl.sg", "meta.styl.sg", "keycloak.styl.solutions", "device.styl.solutions",
			"admin.styl.solutions", "dev.styl.solutions", "sit.styl.solutions", "uat.styl.solutions",
			"staging.styl.solutions" })
	void checkAvailabilityOfBlacklistDomainThenReturnFalse(String domain) {

		// Act & Assert
		FluxExchangeResult<DomainAvailabilityResponse> response = webClient.get()
				.uri(uriBuilder -> uriBuilder.path("/api/tenant/domains/availability")
						.queryParam("domain", "id.styl.sg")
						.build())
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(DomainAvailabilityResponse.class);

		boolean isAvailable = Objects.requireNonNull(response.getResponseBody()
				.blockFirst())
				.isAvailable();
		Assertions.assertFalse(isAvailable);
	}

	@Order(19)
	@Test
	void getTenantInfoWhenInactiveDomainThenReturnTenantResponse() {

		// Act & Assert
		FluxExchangeResult<ErrorResponse> response = webClient.get()
				.uri("/api/tenant/tenants/info", DOMAIN_TEST)
				.header("X-Forwarded-Host", DOMAIN_TEST)
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isBadRequest()
				.returnResult(ErrorResponse.class);

		ErrorResponse errorResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(errorResponse);
		Assertions.assertEquals(GlobalErrorCode.TENANT_NOT_FOUND.getValue(), errorResponse.getCode());
		// Add more assertions as needed
	}

	@Order(19)
	@Test
	void getTenantInfoWhenInvalidDomainThenReturnNotFound() {

		// Act & Assert
		FluxExchangeResult<ErrorResponse> response = webClient.get()
				.uri("/api/tenant/tenants/info", "invalid.styl.solutions")
				.header("X-Forwarded-Host", "invalid.styl.solutions")
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isBadRequest()
				.returnResult(ErrorResponse.class);

		ErrorResponse errorResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(errorResponse);
		Assertions.assertEquals(GlobalErrorCode.TENANT_NOT_FOUND.getValue(), errorResponse.getCode());
		// Add more assertions as needed
	}

	@Order(22)
	@Test
	void getListDomainWithDefaultFilter() {

		// Act & Assert
		FluxExchangeResult<ListDomainResponse> response = webClient.get()
				.uri("/api/tenant/domains")
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(ListDomainResponse.class);

		ListDomainResponse tenantResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenantResponse);
		Assertions.assertEquals(1, tenantResponse.getTotalElements());
		Assertions.assertEquals("tenant.name,ASC", tenantResponse.getSort()
				.getFirst());
	}

	@Order(23)
	@Test
	void getListDomainWithDomainNameFilter() {

		// Act & Assert
		FluxExchangeResult<ListDomainResponse> response = webClient.get()
				.uri(uriBuilder -> uriBuilder.path("/api/tenant/domains")
						.queryParam("filter.domainName", "styl.solutions")
						.build())
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(ListDomainResponse.class);

		ListDomainResponse tenantResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenantResponse);
		Assertions.assertEquals(1, tenantResponse.getTotalElements());
	}

	@Order(24)
	@Test
	void getListDomainWithInvalidDomainNameFilter() {

		// Act & Assert
		FluxExchangeResult<ListDomainResponse> response = webClient.get()
				.uri(uriBuilder -> uriBuilder.path("/api/tenant/domains")
						.queryParam("filter.domainName", "styl.sg")
						.build())
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(ListDomainResponse.class);

		ListDomainResponse tenantResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenantResponse);
		Assertions.assertEquals(0, tenantResponse.getTotalElements());
	}

	@Order(25)
	@Test
	void getListDomainWithTenantNameFilter() {

		// Act & Assert
		FluxExchangeResult<ListDomainResponse> response = webClient.get()
				.uri(uriBuilder -> uriBuilder.path("/api/tenant/domains")
						.queryParam("filter.tenantName", TENANT_NAME)
						.build())
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(ListDomainResponse.class);

		ListDomainResponse domainResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(domainResponse);
		Assertions.assertEquals(1, domainResponse.getTotalElements());

		domainId = domainResponse.getContent()
				.getFirst()
				.getId();

	}

	@Order(26)
	@Test
	void getListDomainSortByDomainName() {

		// Act & Assert
		FluxExchangeResult<ListDomainResponse> response = webClient.get()
				.uri(uriBuilder -> uriBuilder.path("/api/tenant/domains")
						.queryParam("sortFields", "domainName")
						.queryParam("sortDirection", "DESC")
						.build())
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(ListDomainResponse.class);

		ListDomainResponse tenantResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenantResponse);
		Assertions.assertEquals(1, tenantResponse.getTotalElements());
		Assertions.assertEquals("domainName,DESC", tenantResponse.getSort()
				.getFirst());

	}

	@Order(27)
	@Test
	void testDeleteDefaultDomainThenReturnBadRequest() {

		// Act & Assert
		FluxExchangeResult<ErrorResponse> response = webClient.delete()
				.uri("/api/tenant/domains/{domainId}/delete", domainId)
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isBadRequest()
				.returnResult(ErrorResponse.class);

		ErrorResponse errorResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(errorResponse);
		Assertions.assertEquals(GlobalErrorCode.TENANT_DOMAIN_NAME_IN_USED.getValue(), errorResponse.getCode());
	}

	@Order(28)
	@Test
	void activateDomain() {
		// Act & Assert
		FluxExchangeResult<Void> response = webClient.put()
				.uri("/api/tenant/domains/{domainId}/activate", domainId)
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(Void.class);
	}

	@Order(29)
	@Test
	void getTenantInfoWhenForwardedHostContainsMultiValuesThenGetFirst() {

		// Act & Assert
		FluxExchangeResult<TenantInfoResponse> response = webClient.get()
				.uri("/api/tenant/tenants/info")
				.header("Host", DOMAIN_TEST)
				.header("X-Forwarded-Host", DOMAIN_TEST + ",invalid.styl.solutions")
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(TenantInfoResponse.class);

		TenantInfoResponse tenantResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenantResponse);
		Assertions.assertEquals(tenantId, tenantResponse.getTenantId());
		// Add more assertions as needed
	}

	@Order(29)
	@Test
	void getTenantInfoWhenValidDomainThenReturnTenantResponse() {
		FluxExchangeResult<TenantInfoResponse> response = webClient.get()
				.uri("/api/tenant/tenants/info", DOMAIN_TEST)
				.header("X-Forwarded-Host", DOMAIN_TEST)
				.accept(MediaType.APPLICATION_JSON)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(TenantInfoResponse.class);

		TenantInfoResponse tenantResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(tenantResponse);
		Assertions.assertEquals(tenantId, tenantResponse.getTenantId());
	}

}
