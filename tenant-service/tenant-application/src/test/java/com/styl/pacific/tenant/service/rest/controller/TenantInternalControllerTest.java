/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.rest.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.aws.s3.config.PresignerConfiguration;
import com.styl.pacific.aws.s3.config.S3ConfigProperties;
import com.styl.pacific.aws.s3.mapper.PresignerContextProvider;
import com.styl.pacific.domain.valueobject.AgreementTermId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.tenant.service.config.MvcTestConfiguration;
import com.styl.pacific.tenant.service.domain.dto.GetTenantQuery;
import com.styl.pacific.tenant.service.domain.entity.AgreementTerm;
import com.styl.pacific.tenant.service.domain.entity.Tenant;
import com.styl.pacific.tenant.service.domain.exception.TenantNotFoundException;
import com.styl.pacific.tenant.service.domain.port.input.service.AgreementTermDomainService;
import com.styl.pacific.tenant.service.domain.port.input.service.TenantDomainService;
import com.styl.pacific.tenant.service.rest.exception.handler.TenantDomainExceptionHandler;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.AgreementTermResponse;
import java.time.Instant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

@DirtiesContext
@WebMvcTest(TenantInternalController.class)
@Import(value = { TenantDomainExceptionHandler.class, TenantInternalController.class, PresignerConfiguration.class,
		S3ConfigProperties.class, PresignerContextProvider.class })
@ContextConfiguration(classes = { MvcTestConfiguration.class })
class TenantInternalControllerTest {

	@Autowired
	private MockMvc mockMvc;

	@MockitoBean
	private TenantDomainService tenantDomainService;

	@MockitoBean
	private AgreementTermDomainService agreementTermDomainService;

	private ObjectMapper objectMapper;

	@BeforeEach
	public void setUp() {
		objectMapper = new ObjectMapper();
	}

	@Test
	void testGetAgreementTermWhenValidRealmIdThenReturnAgreementTermResponse() throws Exception {
		// Arrange
		String realmId = "test-realm";
		Long tenantId = 1L;
		Long agreementTermId = 100L;
		String content = "This is the agreement term content";

		// Create a tenant with the given realmId
		Tenant tenant = Tenant.builder()
				.id(new TenantId(tenantId))
				.name("Test Tenant")
				.build();

		// Create an agreement term for the tenant
		Instant now = Instant.now();
		AgreementTerm agreementTerm = AgreementTerm.builder()
				.id(new AgreementTermId(agreementTermId))
				.tenantId(new TenantId(tenantId))
				.content(content)
				.createdAt(now)
				.updatedAt(now)
				.build();

		// Create the expected response
		AgreementTermResponse response = AgreementTermResponse.builder()
				.id(String.valueOf(agreementTermId))
				.tenantId(String.valueOf(tenantId))
				.content(content)
				.build();

		// Mock the service calls
		when(tenantDomainService.getTenant(any(GetTenantQuery.class))).thenReturn(tenant);
		when(agreementTermDomainService.getAgreementTerm(tenantId)).thenReturn(agreementTerm);

		// Act & Assert
		mockMvc.perform(get("/api/internal/tenant/tenants/{realmId}/tnc", realmId))
				.andExpect(status().isOk())
				.andExpect(content().json(objectMapper.writeValueAsString(response)));

		// Verify that the services were called with the correct parameters
		verify(tenantDomainService).getTenant(any(GetTenantQuery.class));
		verify(agreementTermDomainService).getAgreementTerm(tenantId);
	}

	@Test
	@Disabled
	void testGetAgreementTermWhenInvalidRealmIdThenReturnNotFound() throws Exception {
		// Arrange
		String realmId = "invalid-realm";

		// Mock the service to throw an exception when the tenant is not found
		when(tenantDomainService.getTenant(any(GetTenantQuery.class))).thenThrow(new TenantNotFoundException(
				"Tenant not found with realmId: " + realmId));

		// Act & Assert
		mockMvc.perform(get("/api/internal/tenant/tenants/{realmId}/tnc", realmId))
				.andExpect(status().isBadRequest());

		// Verify that the service was called with the correct parameters
		verify(tenantDomainService).getTenant(any(GetTenantQuery.class));
		// Verify that the agreementTermDomainService was not called
		verify(agreementTermDomainService, never()).getAgreementTerm(anyLong());
	}
}
