/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.spi.processors.transaction.request;

import com.styl.pacific.domain.enums.PaymentTransactionRemark;
import com.styl.pacific.domain.enums.PaymentTransactionStatus;
import com.styl.pacific.domain.valueobject.IdempotencyKey;
import com.styl.pacific.domain.valueobject.PaymentMethodId;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.enums.PaymentTransactionType;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;

@Builder
@Getter
@With
@RequiredArgsConstructor
public class CreateOfflineTransactionCommand {
	private final IdempotencyKey offlineIdempotencyKey;

	private final PaymentTransactionStatus transactionStatus;

	private final PaymentTransactionRemark transactionRemark;

	@Builder.Default
	private final PaymentTransactionType transactionType = PaymentTransactionType.PURCHASE;

	private final String description;

	private final String paymentMethodDisplayName;

	private final String paymentReference;

	private final String customerId;

	private final String customerEmail;

	private final String customerName;

	private final String deviceId;

	private final String currencyCode;

	private final String terminalId;

	private final String userCardId;

	private final Long fee;

	private final Long amount;

	private final Long netAmount;

	private final PaymentMethodId paymentMethodId;

	private final PaymentProcessorId paymentProcessorId;

	private final BigDecimal surchargeRate;

	private final Long fixedSurcharge;

	private final String systemSource;

	private final String merchantName;

	private final Instant initiatedAt;

	private final Instant paidAt;

	private final String transactionNumber;

	private final String deviceTransactionNumber;

	private final Map<String, Object> settlementData;

	private final Map<String, Object> extraSettlementData;
}
