/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.transactions.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.PaymentSessionId;
import com.styl.pacific.domain.valueobject.PaymentTransactionId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.settlements.valueobject.PaymentSettlementData;
import com.styl.pacific.payment.service.core.features.transactions.entities.PaymentTransaction;
import com.styl.pacific.payment.spi.processors.transaction.request.CreateClientOfflineTransactionCommand;
import com.styl.pacific.payment.spi.processors.transaction.request.CreateOfflineTransactionCommand;
import java.util.Optional;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface PaymentOfflineTransactionMapper {
	PaymentOfflineTransactionMapper INSTANCE = Mappers.getMapper(PaymentOfflineTransactionMapper.class);

	@Mapping(target = "appliedSurchargeRate", source = "command.surchargeRate")
	@Mapping(target = "appliedFixedSurcharge", source = "command.fixedSurcharge")
	@Mapping(target = "version", ignore = true)
	@Mapping(target = "lastReversedAt", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "reversedIdempotencyKeys", ignore = true)
	@Mapping(target = "refundedAmount", ignore = true)
	@Mapping(target = "processorParams", ignore = true)
	@Mapping(target = "sessionData", ignore = true)
	@Mapping(target = "sessionVersion", ignore = true)
	@Mapping(target = "settlementData", ignore = true)
	@Mapping(target = "extraSettlementData", ignore = true)
	@Mapping(target = "isAsync", constant = "false")
	@Mapping(target = "isOffline", constant = "true")
	PaymentTransaction toCreateOfflineTransactionCommand(PaymentTransactionId id, PaymentSessionId paymentSessionId,
			Boolean isReversible, TenantId tenantId, String paymentMethodDisplayName,
			CreateOfflineTransactionCommand command);

	@AfterMapping
	default void afterOfflineTransactionCommand(@MappingTarget PaymentTransaction.PaymentTransactionBuilder builder,
			CreateOfflineTransactionCommand command) {
		Optional.ofNullable(command.getSettlementData())
				.ifPresent(settlementData -> builder.settlementData(PaymentSettlementData.builder()
						.data(settlementData)
						.build()));

		Optional.ofNullable(command.getExtraSettlementData())
				.ifPresent(extraSettlementData -> builder.settlementData(PaymentSettlementData.builder()
						.data(extraSettlementData)
						.build()));
	}

	@Mapping(target = "paymentTransactionId", source = "id")
	CreateClientOfflineTransactionCommand toClientOfflineTransactionCommand(PaymentTransaction transaction);
}
