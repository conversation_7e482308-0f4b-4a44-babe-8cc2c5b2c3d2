/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.transactions.service;

import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.valueobject.PaymentTransactionId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.customers.CustomerRepository;
import com.styl.pacific.payment.service.core.features.methods.PaymentMethodQueryService;
import com.styl.pacific.payment.service.core.features.sessions.idgenerator.PaymentSessionIdGenerator;
import com.styl.pacific.payment.service.core.features.settlements.valueobject.PaymentSettlementData;
import com.styl.pacific.payment.service.core.features.transactions.PaymentOfflineEventPublisher;
import com.styl.pacific.payment.service.core.features.transactions.PaymentTransactionRepository;
import com.styl.pacific.payment.service.core.features.transactions.PaymentTransactionService;
import com.styl.pacific.payment.service.core.features.transactions.entities.PaymentTransaction;
import com.styl.pacific.payment.service.core.features.transactions.events.OfflineTransactionCreatedEvent;
import com.styl.pacific.payment.service.core.features.transactions.idgenerator.PaymentTransactionIdGenerator;
import com.styl.pacific.payment.service.core.features.transactions.mapper.PaymentOfflineTransactionMapper;
import com.styl.pacific.payment.service.core.features.transactions.request.FilterPaymentTransactionQuery;
import com.styl.pacific.payment.service.core.features.transactions.request.PaymentTransactionPaginationQuery;
import com.styl.pacific.payment.service.core.features.validators.PaymentMoneyValidator;
import com.styl.pacific.payment.shared.enums.ReconciliationStatus;
import com.styl.pacific.payment.shared.exceptions.PaymentOfflineProcessorNotMatchException;
import com.styl.pacific.payment.shared.exceptions.PaymentProcessorNotFoundException;
import com.styl.pacific.payment.shared.http.transactions.response.ReconciliationResponse;
import com.styl.pacific.payment.spi.processors.PaymentProcessorManager;
import com.styl.pacific.payment.spi.processors.transaction.request.CreateOfflineTransactionCommand;
import com.styl.pacific.payment.spi.processors.transaction.request.ReconcileTransactionCommand;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentTransactionServiceImpl implements PaymentTransactionService {

	private final PaymentMethodQueryService methodQueryService;
	private final PaymentTransactionIdGenerator transactionIdGenerator;
	private final PaymentSessionIdGenerator paymentSessionIdGenerator;
	private final PaymentProcessorManager processorManager;
	private final PaymentTransactionRepository repository;
	private final CustomerRepository customerRepository;
	private final PaymentOfflineEventPublisher eventPublisher;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public PaymentTransaction createOfflinePaymentTransaction(TenantId tenantId,
			CreateOfflineTransactionCommand command) {
		final var processor = processorManager.getProcessorById(command.getPaymentProcessorId())
				.orElseThrow(PaymentProcessorNotFoundException::new);

		final var transaction = repository.findTransactionByTenantIdAndOfflineIdempotencyKey(tenantId, command
				.getOfflineIdempotencyKey())
				.orElseGet(() -> {
					final var paymentMethod = methodQueryService.getPaymentMethodByTenantIdAndId(tenantId, command
							.getPaymentMethodId())
							.orElse(null);
					if (paymentMethod != null && !paymentMethod.getProcessorId()
							.equals(command.getPaymentProcessorId())) {
						throw new PaymentOfflineProcessorNotMatchException();
					}

					PaymentMoneyValidator.validateMoney(paymentMethod != null
							? paymentMethod.getCurrencyCode()
							: command.getCurrencyCode(), command.getFixedSurcharge(), command.getSurchargeRate(),
							command.getCurrencyCode(), command.getFee(), command.getAmount(), command.getNetAmount());

					var forgedCommand = processor.forgeOfflineTransactionCommand(command);

					if (StringUtils.isEmpty(forgedCommand.getCustomerId()) && StringUtils.isNotEmpty(forgedCommand
							.getUserCardId())) {
						final var userCard = customerRepository.getFullUserCardByCardId(forgedCommand.getUserCardId());
						forgedCommand = forgedCommand.withUserCardId(userCard.getCardId())
								.withCustomerId(userCard.getUser()
										.getId())
								.withCustomerEmail(userCard.getUser()
										.getEmail());
					}

					final var offlineTransaction = PaymentOfflineTransactionMapper.INSTANCE
							.toCreateOfflineTransactionCommand(transactionIdGenerator.generate(),
									paymentSessionIdGenerator.generate(), processor.getSystemProcessorConfiguration()
											.getTransactionReversible(), tenantId, paymentMethod != null
													? paymentMethod.getDisplayName()
													: command.getPaymentMethodDisplayName(), forgedCommand);
					return repository.saveTransaction(offlineTransaction);
				});

		final var updatedTransaction = Optional.ofNullable(processor.createClientOfflineTransaction(
				PaymentOfflineTransactionMapper.INSTANCE.toClientOfflineTransactionCommand(transaction)))
				.filter(settlementData -> transaction.getSettlementData() == null || CollectionUtils.isEmpty(transaction
						.getSettlementData()
						.getData()))
				.map(settlementData -> repository.saveTransaction(transaction.withTransactionNumber(settlementData
						.getTransactionNumber())
						.withUserCardId(settlementData.getUserCardId())
						.withSettlementData(PaymentSettlementData.builder()
								.data(settlementData.getSettlementData())
								.build())))
				.orElse(transaction);

		log.info("Created a new offline transaction for Tenant: {}, Id: {}, TransactionStatus: {}", tenantId.getValue(),
				updatedTransaction.getId()
						.getValue(), updatedTransaction.getTransactionStatus());

		eventPublisher.publishEvent(OfflineTransactionCreatedEvent.builder()
				.eventId(UUID.randomUUID())
				.offlineTransaction(updatedTransaction)
				.build());
		return updatedTransaction;
	}

	@Override
	public List<ReconciliationResponse> reconcile(TenantId tenantId, List<ReconcileTransactionCommand> commands) {
		Set<PaymentTransactionId> ids = commands.stream()
				.map(c -> c.transactionId())
				.collect(Collectors.toSet());
		FilterPaymentTransactionQuery filter = FilterPaymentTransactionQuery.builder()
				.byTransactionIds(ids)
				.byTenantId(tenantId)
				.build();
		PaymentTransactionPaginationQuery query = PaymentTransactionPaginationQuery.builder()
				.filter(filter)
				.build();
		Map<PaymentTransactionId, PaymentTransaction> transactionMap = repository.queryTransactions(query)
				.getContent()
				.stream()
				.collect(Collectors.toConcurrentMap(PaymentTransaction::getId, Function.identity()));
		List<ReconciliationResponse> responses = new ArrayList<>();

		// Handle not found transactions
		ids.stream()
				.filter(id -> !transactionMap.containsKey(id))
				.forEach(id -> {
					responses.add(createResponse(id, ReconciliationStatus.TRANSACTION_NOT_FOUND));
					transactionMap.remove(id);
				});

		// Handle already reconciled transactions
		transactionMap.values()
				.stream()
				.filter(t -> t.getTransactionRemark() == null)
				.forEach(t -> {
					responses.add(createResponse(t.getId(), ReconciliationStatus.TRANSACTION_ALREADY_RECONCILED));
					transactionMap.remove(t.getId());
				});

		// Process valid reconciliation commands
		commands.stream()
				.filter(c -> transactionMap.containsKey(c.transactionId()))
				.forEach(command -> {
					PaymentTransaction transaction = transactionMap.get(command.transactionId());
					Map<String, Object> data = Optional.ofNullable(transaction.getSettlementData())
							.map(settlementData -> settlementData.getData())
							.orElseGet(HashMap::new);
					data.putAll(command.reconcileData());
					transaction = transaction.withTransactionRemark(null) // clear remark after reconcile
							.withSettlementData(PaymentSettlementData.builder()
									.data(data)
									.build());
					try {
						repository.saveTransaction(transaction);
						responses.add(createResponse(transaction.getId(), ReconciliationStatus.SUCCESS));
					} catch (Exception e) {
						log.error("Failed to reconcile transaction Id: {}", transaction.getId()
								.getValue(), e);
						responses.add(createResponse(transaction.getId(), ReconciliationStatus.FAILED));
					}
				});
		return responses;
	}

	private ReconciliationResponse createResponse(PaymentTransactionId transactionId, ReconciliationStatus status) {
		String id = MapstructCommonDomainMapper.INSTANCE.baseLongIdToString(transactionId);
		return ReconciliationResponse.builder()
				.transactionId(id)
				.status(status)
				.build();
	}
}
