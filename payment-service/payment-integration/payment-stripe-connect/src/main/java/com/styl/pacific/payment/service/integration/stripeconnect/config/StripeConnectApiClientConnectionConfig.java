/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.stripeconnect.config;

import com.stripe.net.RequestOptions;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "pacific.stripe-connect")
public class StripeConnectApiClientConnectionConfig {
	private String basedUrl;
	private Integer connectTimeoutInMillisecond;
	private Integer readTimeoutInMillisecond;
	private Integer maxNetworkRetries;
	private String platformApiKey;
	private String platformWebhookDomain;

	public RequestOptions generatePlatformRequestOption() {
		return RequestOptions.builder()
				.setApiKey(platformApiKey)
				.setBaseUrl(basedUrl)
				.setReadTimeout(readTimeoutInMillisecond)
				.setConnectTimeout(connectTimeoutInMillisecond)
				.setMaxNetworkRetries(maxNetworkRetries)
				.build();
	}

	public RequestOptions generatePlatformRequestOption(String stripeConnectedAccountKey) {
		return RequestOptions.builder()
				.setApiKey(platformApiKey)
				.setStripeAccount(stripeConnectedAccountKey)
				.setBaseUrl(basedUrl)
				.setReadTimeout(readTimeoutInMillisecond)
				.setConnectTimeout(connectTimeoutInMillisecond)
				.setMaxNetworkRetries(maxNetworkRetries)
				.build();
	}
}
