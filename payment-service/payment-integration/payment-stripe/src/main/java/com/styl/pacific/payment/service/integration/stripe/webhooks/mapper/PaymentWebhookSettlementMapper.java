/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.stripe.webhooks.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.payment.service.integration.stripe.sessions.valueobject.StripePaymentSessionMetadata;
import com.styl.pacific.payment.spi.processors.settlements.events.PaymentSettlementCommandRequestEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface PaymentWebhookSettlementMapper {
	PaymentWebhookSettlementMapper INSTANCE = Mappers.getMapper(PaymentWebhookSettlementMapper.class);

	@Mapping(target = "paidAt", ignore = true)
	@Mapping(target = "isAsync", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "transactionStatus", ignore = true)
	@Mapping(target = "settlementData", ignore = true)
	@Mapping(target = "extraSettlementData", ignore = true)
	@Mapping(target = "eventId", ignore = true)
	PaymentSettlementCommandRequestEvent toPaymentSettlementRequestEvent(StripePaymentSessionMetadata clientData);
}