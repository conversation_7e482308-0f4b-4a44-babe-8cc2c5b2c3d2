/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.smartbuddy.settlements.valueobject;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.styl.pacific.payment.shared.enums.SbCardType;
import com.styl.pacific.payment.shared.enums.SbPaymentMode;
import com.styl.pacific.payment.shared.enums.SbSofType;
import com.styl.pacific.payment.shared.http.settlement.request.smartbuddy.SbSpeed;
import com.styl.pacific.payment.shared.http.settlement.request.smartbuddy.SbTransactionItem;
import java.math.BigInteger;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SbPaymentSettlementData {
	private BigInteger amount;
	private String cardId;
	private SbCardType cardType;
	private SbSofType sofType;
	private String cepasSettlementData;
	private Integer errorCode;
	private String acquirerErrorCode;
	private List<SbTransactionItem> items;
	private Long offlineReconcileTimestamp;
	private String offlineSEQ;
	private SbPaymentMode paymentMode;
	private String posNetworkType;
	private String posCellBand;
	private String posSignalStrength;
	private String readerAccessTech;
	private String readerCellBand;
	private Integer readerSignalStrength;
	private String readerTid;
	private String readerMid;
	private String readerId;
	private String schoolId;
	private String storeId;
	private String storeName;
	private SbSpeed speed;
	private Integer stan;
	private Long timestamp;
}
