/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.smartbuddy.connector;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.constant.CommonConstants;
import com.styl.pacific.common.feign.utils.CryptoUtils;
import com.styl.pacific.payment.service.integration.smartbuddy.client.CalebClient;
import com.styl.pacific.payment.service.integration.smartbuddy.client.SSSClient;
import com.styl.pacific.payment.service.integration.smartbuddy.client.request.ApiKeyClientRequest;
import com.styl.pacific.payment.service.integration.smartbuddy.client.request.CreateCalebTransactionClientRequest;
import com.styl.pacific.payment.service.integration.smartbuddy.mapper.SettlementDataMapper;
import com.styl.pacific.payment.service.integration.smartbuddy.settlements.valueobject.SbPaymentSettlementData;
import com.styl.pacific.payment.shared.enums.CalebCardType;
import com.styl.pacific.payment.shared.enums.CalebSubmitTransactionStatus;
import com.styl.pacific.payment.shared.enums.CalebTransactionSource;
import com.styl.pacific.payment.shared.enums.SbPaymentMode;
import com.styl.pacific.payment.shared.enums.SbSofType;
import com.styl.pacific.payment.shared.exceptions.PaymentDataParsingException;
import com.styl.pacific.payment.shared.exceptions.PaymentTransactionCalebSubmissionException;
import com.styl.pacific.payment.shared.http.settlement.request.smartbuddy.SbTransactionItem;
import com.styl.pacific.payment.shared.http.smartbuddy.response.SchoolResponse;
import com.styl.pacific.payment.spi.processors.smartbuddy.connector.SbConnector;
import com.styl.pacific.payment.spi.processors.smartbuddy.request.CreateCalebTransactionRequest;
import com.styl.pacific.payment.spi.processors.smartbuddy.response.SbApiKeyResponse;
import com.styl.pacific.payment.spi.processors.smartbuddy.response.SbCalebTransactionResponse;
import com.styl.pacific.payment.spi.processors.smartbuddy.response.SbPaging;
import java.nio.charset.StandardCharsets;
import java.security.KeyPair;
import java.security.PublicKey;
import java.time.Instant;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class SbConnectorImpl implements SbConnector {
	private final SSSClient sssClient;
	private final CalebClient calebClient;
	@Value("${pacific.clients.caleb.secret-key}")
	private String calebSecretKey;
	private final ObjectMapper objectMapper;
	private static final String SOF_TYPE_SSS = "SSS";

	@Override
	public SbPaging<SchoolResponse> getSchools(String apiKey, String secretKey) {
		String nonce = generateNonce();
		String signature = CryptoUtils.createSignature(secretKey, null, null, nonce);
		return sssClient.getSchools(apiKey, nonce, signature);
	}

	@Override
	public SbApiKeyResponse getTerminalApiKey(String apiKey, String secretKey) {
		KeyPair keyPair = CryptoUtils.generateRsaKeyPair();
		ApiKeyClientRequest request = buildApiKeyRequest(keyPair.getPublic());
		String nonce = generateNonce();
		String jsonBody = serializeRequest(request);
		String signature = CryptoUtils.createSignature(secretKey, jsonBody.getBytes(StandardCharsets.UTF_8), null,
				nonce);
		SbApiKeyResponse response = sssClient.getTerminalApiKey(apiKey, nonce, signature, request);
		return response.withFasSecretKey(CryptoUtils.decryptSecretKey(response.fasSecretKey(), keyPair.getPrivate()));
	}

	@Override
	public SbApiKeyResponse renewThirdPartyApiKey(String apiKey, String secretKey) {
		KeyPair keyPair = CryptoUtils.generateRsaKeyPair();
		ApiKeyClientRequest request = buildApiKeyRequest(keyPair.getPublic());
		String nonce = generateNonce();
		String jsonBody = serializeRequest(request);
		String signature = CryptoUtils.createSignature(secretKey, jsonBody.getBytes(StandardCharsets.UTF_8), null,
				nonce);
		SbApiKeyResponse response = sssClient.reviewThirdPartyApiKey(apiKey, nonce, signature, request);
		return response.withFasSecretKey(CryptoUtils.decryptSecretKey(response.fasSecretKey(), keyPair.getPrivate()));
	}

	@Override
	public SbCalebTransactionResponse submitTransactionToCaleb(CreateCalebTransactionRequest request) {
		Map<String, Object> settlementDataMap = request.settlementData();
		SbPaymentSettlementData sbPaymentSettlementData = objectMapper.convertValue(settlementDataMap,
				SbPaymentSettlementData.class);

		// If the transaction has an error code different from 0, skip submitting to Caleb
		if (sbPaymentSettlementData == null || sbPaymentSettlementData.getErrorCode() != 0) {
			log.info("Skipping Caleb submission for transaction {} with error code {}.", request.transactionId(),
					sbPaymentSettlementData != null ? sbPaymentSettlementData.getErrorCode() : "null");
			return SbCalebTransactionResponse.builder()
					.status(CalebSubmitTransactionStatus.failed)
					.build();
		}

		String cardType = CalebCardType.from(sbPaymentSettlementData.getCardType()
				.getValue());
		if (cardType == null) {
			log.error("Unsupported card type {} for Smart Buddy transaction {}, skip submitting to Caleb.",
					sbPaymentSettlementData.getCardType(), request.transactionId());
			return SbCalebTransactionResponse.builder()
					.status(CalebSubmitTransactionStatus.failed)
					.build();
		}

		boolean isOffline = sbPaymentSettlementData.getPaymentMode()
				.equals(SbPaymentMode.OFFLINE) ? true : false;

		int totalQuantity = totalQuantity(sbPaymentSettlementData.getItems());
		CalebTransactionSource txnSource = SbSofType.SSS == sbPaymentSettlementData.getSofType()
				? CalebTransactionSource.TERABITE_FAS
				: CalebTransactionSource.TERABITE;
		CreateCalebTransactionClientRequest clientRequest = SettlementDataMapper.INSTANCE.toRequest(request
				.transactionId(), txnSource, totalQuantity, cardType, isOffline, sbPaymentSettlementData);
		SbCalebTransactionResponse response = calebClient.submitTransaction(calebSecretKey, clientRequest);
		// Submission failed, throw exception to trigger retry
		if (CalebSubmitTransactionStatus.failed.equals(response.status())) {
			throw new PaymentTransactionCalebSubmissionException(
					"Failed to submit transaction to Caleb for PaymentTransactionId: " + request.transactionId());
		}
		return response;
	}

	private int totalQuantity(List<SbTransactionItem> items) {
		return items.stream()
				.mapToInt(SbTransactionItem::quantity)
				.sum();
	}

	private String generateNonce() {
		return Instant.now()
				.toEpochMilli() + CommonConstants.HASHTAG + UUID.randomUUID();
	}

	private ApiKeyClientRequest buildApiKeyRequest(PublicKey publicKey) {
		return ApiKeyClientRequest.builder()
				.publicKey(Base64.getEncoder()
						.encodeToString(publicKey.getEncoded()))
				.build();
	}

	private String serializeRequest(ApiKeyClientRequest request) {
		try {
			return objectMapper.writeValueAsString(request);
		} catch (JsonProcessingException e) {
			throw new PaymentDataParsingException("Error serializing ApiKeyClientRequest", e);
		}
	}
}
