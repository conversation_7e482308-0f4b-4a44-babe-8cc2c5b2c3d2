/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.smartbuddy.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.payment.service.integration.smartbuddy.client.request.CalebTransactionItem;
import com.styl.pacific.payment.service.integration.smartbuddy.client.request.CreateCalebTransactionClientRequest;
import com.styl.pacific.payment.service.integration.smartbuddy.settlements.valueobject.SbPaymentSettlementData;
import com.styl.pacific.payment.shared.enums.CalebTransactionSource;
import com.styl.pacific.payment.shared.http.settlement.request.smartbuddy.SbTransactionItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface SettlementDataMapper {

	SettlementDataMapper INSTANCE = Mappers.getMapper(SettlementDataMapper.class);

	@Mapping(target = "id", source = "transactionId")
	@Mapping(target = "terminalId", source = "settlementData.readerTid")
	@Mapping(target = "merchantId", source = "settlementData.readerMid")
	@Mapping(target = "schoolCode", source = "settlementData.schoolId")
	@Mapping(target = "cardId", source = "settlementData.cardId")
	@Mapping(target = "cardType", source = "cardType")
	@Mapping(target = "txnSource", source = "txnSource")
	@Mapping(target = "amount", source = "settlementData.amount")
	@Mapping(target = "totalQuantity", source = "totalQuantity")
	@Mapping(target = "stan", source = "settlementData.stan")
	@Mapping(target = "timestamp", source = "settlementData.timestamp")
	@Mapping(target = "itemStoreId", source = "settlementData.storeId")
	@Mapping(target = "itemStoreName", source = "settlementData.storeName")
	@Mapping(target = "isOffline", source = "isOffline")
	@Mapping(target = "items", source = "settlementData.items")
	CreateCalebTransactionClientRequest toRequest(String transactionId, CalebTransactionSource txnSource,
			Integer totalQuantity, String cardType, boolean isOffline, SbPaymentSettlementData settlementData);

	@Mapping(target = "itemId", source = "item.itemId")
	@Mapping(target = "itemName", source = "item.itemName")
	@Mapping(target = "itemType", constant = "OTHER")
	@Mapping(target = "quantity", source = "item.quantity")
	@Mapping(target = "price", source = "item.itemPrice")
	CalebTransactionItem toCalebTransactionItem(SbTransactionItem item);
}
