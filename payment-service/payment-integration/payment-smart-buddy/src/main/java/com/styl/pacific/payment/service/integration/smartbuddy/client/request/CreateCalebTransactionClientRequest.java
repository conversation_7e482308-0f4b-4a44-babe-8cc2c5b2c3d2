/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.smartbuddy.client.request;

import com.styl.pacific.payment.shared.enums.CalebTransactionSource;
import java.math.BigInteger;
import java.util.List;
import lombok.Builder;

@Builder
public record CreateCalebTransactionClientRequest(String id,
		String terminalId,
		String merchantId,
		String schoolCode,
		String cardId,
		String cardType,
		CalebTransactionSource txnSource,
		BigInteger amount,
		Integer totalQuantity,
		Integer stan,
		Long timestamp,
		String itemStoreId,
		String itemStoreName,
		boolean isOffline,
		List<CalebTransactionItem> items) {
}
