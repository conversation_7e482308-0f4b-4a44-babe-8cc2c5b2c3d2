CREATE
    TABLE
        IF NOT EXISTS payment_connected_accounts(
            id BIGSERIAL NOT NULL CONSTRAINT payment_connected_accounts_pk PRIMARY KEY,
            tenant_id BIGINT NOT NULL,
            is_active BOOLEAN NOT NULL,
            client_external_id VARCHAR(255) NOT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW(),
            updated_at TIMESTAMP(6) DEFAULT NOW()
        );

CREATE
    UNIQUE INDEX IF NOT EXISTS payment_connected_accounts_client_external_id_uidx ON
    payment_connected_accounts(
        tenant_id,
        client_external_id
    );

ALTER TABLE
    IF EXISTS payment_methods ADD COLUMN IF NOT EXISTS payment_connected_account_id BIGINT;

ALTER TABLE
    IF EXISTS payment_methods ADD CONSTRAINT payment_methods_connected_account_fk FOREIGN KEY(payment_connected_account_id) REFERENCES payment_connected_accounts(id);
