/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.http.methods.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.domain.dto.FileResponse;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.enums.AcceptedApplication;
import com.styl.pacific.payment.shared.http.connectedaccount.response.PaymentConnectedAccountResponse;
import java.time.Instant;
import java.util.Set;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@Builder
@RequiredArgsConstructor
public class PaymentMethodResponse {
	private final String id;
	private final PaymentProcessorId processorId;
	private final String tenantId;
	private final String displayName;
	private final FileResponse icon;
	private final String description;
	private final Boolean isActive;
	private final String paymentInstruction;
	private final String surchargeRate;
	private final Long fixedSurcharge;
	private final CurrencyResponse currency;
	private final String surchargeTitle;

	@JsonProperty("transactionReversible")
	private final boolean isTransactionReversible;

	private final PaymentConnectedAccountResponse connectedAccount;

	private final PaymentProcessorConfigurationResponse processorConfig;
	private final Set<AcceptedApplication> acceptedApplications;

	private final Instant createdAt;
	private final Instant updatedAt;
	private final String createdBy;
	private final String updatedBy;

}
