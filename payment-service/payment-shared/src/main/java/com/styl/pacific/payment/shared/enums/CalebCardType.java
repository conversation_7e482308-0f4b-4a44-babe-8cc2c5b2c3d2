/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.enums;

import java.util.Set;

public enum CalebCardType {
	SB_CARD("SBCard", SbCardType.NETS2_0.getValue(), SbCardType.NETS_2_0_SB.getValue(), SbCardType.NETS_2_0_ATM
			.getValue(), SbCardType.NPC.getValue(), SbCardType.HTNS_PA_POSB.getValue(), SbCardType.POSB_PASSION
					.getValue(), SbCardType.UPI_ATM.getValue(), SbCardType.SSS_SB_ATM.getValue(), SbCardType.SSS_NFP
							.getValue()),
	EZ_LINK("EZ-Link", SbCardType.EZLINK.getValue(), SbCardType.EZL_20_ASC.getValue(), SbCardType.EZL_30_ASC.getValue(),
			SbCardType.SSS_EZL.getValue(), SbCardType.SSS_EZL_30.getValue()),
	CONCESSION("CONCESSION", SbCardType.CONCESSION.getValue(), SbCardType.CCS_20_ASC.getValue(), SbCardType.CCS_30_ASC
			.getValue(), SbCardType.SSS_CCS_30.getValue(), SbCardType.SSS_CONCESSION.getValue()),
	NFP("NFP", SbCardType.SSS_NFP.getValue(), SbCardType.NFP.getValue());

	private final String mappedValue;
	private final Set<String> aliases;

	CalebCardType(String mappedValue, String... aliases) {
		this.mappedValue = mappedValue;
		this.aliases = Set.of(aliases);
	}

	public String getMappedValue() {
		return mappedValue;
	}

	public static String from(String rawValue) {
		if (rawValue == null)
			return null;
		for (CalebCardType type : values()) {
			if (type.aliases.contains(rawValue)) {
				return type.mappedValue;
			}
		}
		return null;
	}
}
