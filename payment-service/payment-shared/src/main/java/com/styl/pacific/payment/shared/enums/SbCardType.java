/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.exc.MismatchedInputException;

public enum SbCardType {
	NETS2_0("NETS2.0"),
	NETS_2_0_SB("NETS 2.0 (SB)"),
	NETS_2_0_ATM("NETS 2.0 (ATM)"),
	NPC("NPC"),
	HTNS_PA_POSB("HTNS PA POSB"),
	POSB_PASSION("POSB PAssion"),
	UPI_ATM("UPI ATM"),
	EZLINK("EZLINK"),
	EZL_20_ASC("EZL_20_ASC"),
	EZL_30_ASC("EZL_30_ASC"),
	CONCESSION("CONCESSION"),
	CCS_20_ASC("CCS_20_ASC"),
	CCS_30_ASC("CCS_30_ASC"),
	SSS_CCS_30("SSS (CCS_30)"),
	SSS_CONCESSION("SSS (CONCESSION)"),
	SSS_EZL("SSS (EZL)"),
	SSS_EZL_30("SSS (EZL_30)"),
	SSS_MIFARE("SSS (MIFARE)"),
	SSS_NFP("SSS (NFP)"),
	SSS_SB_ATM("SSS (SB/ATM)"),
	SSS_NPC("SSS (NPC)"),
	NFP("NFP");

	private final String value;

	SbCardType(String value) {
		this.value = value;
	}

	@JsonValue
	public String getValue() {
		return value;
	}

	@JsonCreator
	public static SbCardType fromValue(String value) throws MismatchedInputException {
		for (SbCardType type : values()) {
			if (type.value.equalsIgnoreCase(value)) {
				return type;
			}
		}
		throw MismatchedInputException.from(null, SbCardType.class, String.format("Invalid card type: %s", value));
	}
}
