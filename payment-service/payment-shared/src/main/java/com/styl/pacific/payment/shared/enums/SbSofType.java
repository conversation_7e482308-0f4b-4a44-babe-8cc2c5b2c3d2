/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.exc.MismatchedInputException;

public enum SbSofType {
	NETS2_0("NETS2.0"),
	EZLINK("EZLINK"),
	EZL_20_ASC("EZL_20_ASC"),
	EZL_30_ASC("EZL_30_ASC"),
	CONCESSION("CONCESSION"),
	CCS_20_ASC("CCS_20_ASC"),
	CCS_30_ASC("CCS_30_ASC"),
	SSS("SSS"),
	NFP("NFP"),
	UNKNOWN("UNKNOWN");

	private final String value;

	SbSofType(String value) {
		this.value = value;
	}

	@JsonValue
	public String getValue() {
		return value;
	}

	@JsonCreator
	public static SbSofType fromValue(String value) throws MismatchedInputException {
		for (SbSofType type : values()) {
			if (type.value.equalsIgnoreCase(value)) {
				return type;
			}
		}
		throw MismatchedInputException.from(null, SbSofType.class, String.format("Invalid card type: %s", value));
	}
}
