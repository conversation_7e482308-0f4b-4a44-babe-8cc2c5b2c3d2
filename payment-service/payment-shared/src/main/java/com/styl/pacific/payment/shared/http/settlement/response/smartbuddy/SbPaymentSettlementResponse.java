/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.http.settlement.response.smartbuddy;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.constants.PaymentProcessorConstants;
import com.styl.pacific.payment.shared.enums.SbCardType;
import com.styl.pacific.payment.shared.enums.SbPaymentMode;
import com.styl.pacific.payment.shared.enums.SbSofType;
import com.styl.pacific.payment.shared.http.settlement.request.smartbuddy.SbSpeed;
import com.styl.pacific.payment.shared.http.settlement.request.smartbuddy.SbTransactionItem;
import com.styl.pacific.payment.shared.http.settlement.response.PaymentSettlementDataResponse;
import java.math.BigInteger;
import java.util.List;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@JsonTypeName(PaymentProcessorConstants.PaymentProcessorIds.SMART_BUDDY_PAYMENT)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SbPaymentSettlementResponse extends PaymentSettlementDataResponse {
	private BigInteger amount;
	private String cardId;
	private SbCardType cardType;
	private SbSofType sofType;
	private String cepasSettlementData;
	private Integer errorCode;
	private String acquirerErrorCode;
	private List<SbTransactionItem> items;
	private Long offlineReconcileTimestamp;
	private String offlineSEQ;
	private SbPaymentMode paymentMode;
	private String posNetworkType;
	private String posCellBand;
	private String posSignalStrength;
	private String readerAccessTech;
	private String readerCellBand;
	private Integer readerSignalStrength;
	private String readerTid;
	private String readerMid;
	private String readerId;
	private String schoolId;
	private String storeId;
	private String storeName;
	private SbSpeed speed;
	private Integer stan;
	private Long timestamp;

	@JsonCreator
	@Builder
	protected SbPaymentSettlementResponse(String failureCode, String failureDescription, BigInteger amount,
			String cardId, SbCardType cardType, SbSofType sofType, String cepasSettlementData, Integer errorCode,
			String acquirerErrorCode, String offlineSEQ, Long offlineReconcileTimestamp, SbPaymentMode paymentMode,
			String posNetworkType, String posCellBand, String posSignalStrength, String readerAccessTech,
			String readerCellBand, Integer readerSignalStrength, String readerTid, String readerMid, String readerId,
			String schoolId, String storeId, String storeName, SbSpeed speed, Integer stan, Long timestamp) {
		super(PaymentProcessorId.SMART_BUDDY_PAYMENT, failureCode, failureDescription);
		this.amount = amount;
		this.cardId = cardId;
		this.cardType = cardType;
		this.sofType = sofType;
		this.cepasSettlementData = cepasSettlementData;
		this.errorCode = errorCode;
		this.acquirerErrorCode = acquirerErrorCode;
		this.offlineReconcileTimestamp = offlineReconcileTimestamp;
		this.offlineSEQ = offlineSEQ;
		this.paymentMode = paymentMode;
		this.posNetworkType = posNetworkType;
		this.posCellBand = posCellBand;
		this.posSignalStrength = posSignalStrength;
		this.readerAccessTech = readerAccessTech;
		this.readerCellBand = readerCellBand;
		this.readerSignalStrength = readerSignalStrength;
		this.readerTid = readerTid;
		this.readerMid = readerMid;
		this.readerId = readerId;
		this.schoolId = schoolId;
		this.storeId = storeId;
		this.storeName = storeName;
		this.speed = speed;
		this.stan = stan;
		this.timestamp = timestamp;
	}
}
