/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.http.controller.transactions;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.domain.enums.PaymentTransactionRemark;
import com.styl.pacific.domain.enums.PaymentTransactionStatus;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.messaging.offline.publisher.kafka.KafkaOfflineTransactionCreatedEventPublisher;
import com.styl.pacific.payment.service.config.IntegrationTestConfiguration;
import com.styl.pacific.payment.service.config.PaymentIntegrationTestContainer;
import com.styl.pacific.payment.service.core.features.transactions.PaymentTransactionQueryService;
import com.styl.pacific.payment.service.core.features.transactions.PaymentTransactionRepository;
import com.styl.pacific.payment.service.data.access.clients.tenants.TenantClient;
import com.styl.pacific.payment.shared.enums.AcceptedApplication;
import com.styl.pacific.payment.shared.enums.PaymentSessionStatus;
import com.styl.pacific.payment.shared.enums.PaymentTransactionType;
import com.styl.pacific.payment.shared.enums.ReconciliationStatus;
import com.styl.pacific.payment.shared.enums.SbCardType;
import com.styl.pacific.payment.shared.enums.SbPaymentMode;
import com.styl.pacific.payment.shared.enums.SbSofType;
import com.styl.pacific.payment.shared.http.methods.request.CreatePaymentMethodRequest;
import com.styl.pacific.payment.shared.http.methods.request.smartbuddy.SbPaymentMethodConfigurationRequest;
import com.styl.pacific.payment.shared.http.methods.response.PaymentMethodResponse;
import com.styl.pacific.payment.shared.http.sessions.request.CreatePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.sessions.request.smartbuddy.SbPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.response.PaymentSessionResponse;
import com.styl.pacific.payment.shared.http.settlement.request.SettlePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.settlement.request.smartbuddy.SbPaymentSettlementSessionRequest;
import com.styl.pacific.payment.shared.http.settlement.request.smartbuddy.SbSpeed;
import com.styl.pacific.payment.shared.http.settlement.request.smartbuddy.SbTransactionItem;
import com.styl.pacific.payment.shared.http.settlement.response.smartbuddy.SbPaymentSettlementResponse;
import com.styl.pacific.payment.shared.http.transactions.request.CreateOfflineTransactionRequest;
import com.styl.pacific.payment.shared.http.transactions.request.ReconcileTransactionListRequest;
import com.styl.pacific.payment.shared.http.transactions.request.ReconcileTransactionRequest;
import com.styl.pacific.payment.shared.http.transactions.request.smartbuddy.SbReconcileDataRequest;
import com.styl.pacific.payment.shared.http.transactions.response.PaymentTransactionResponse;
import com.styl.pacific.payment.shared.http.transactions.response.ReconciliationResponse;
import com.styl.pacific.payment.spi.processors.smartbuddy.connector.SbConnector;
import com.styl.pacific.payment.spi.processors.smartbuddy.response.SbApiKeyResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.commons.util.StringUtils;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
public class SbPaymentTransactionControllerIT extends PaymentIntegrationTestContainer {

	@MockitoBean
	private TenantClient tenantClient;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private PaymentTransactionQueryService transactionQueryService;

	@MockitoBean
	private SbConnector sbConnector;

	@MockitoSpyBean
	private PaymentTransactionRepository repository;

	@MockitoBean
	private KafkaOfflineTransactionCreatedEventPublisher kafkaOfflineTransactionCreatedEventPublisher;

	private static final String SSS_API_KEY = "SSS_API_KEY";
	private static final String SSS_SECRET_KEY = "SSS_SECRET_KEY";
	private static final String SSS_TERMINAL_API_KEY = "SSS_TERMINAL_API_KEY";
	private static final String SSS_TERMINAL_SECRET_KEY = "SSS_TERMINAL_SECRET_KEY";
	private static final Long SSS_EXPIRY = 1790996668000L;
	private static String sbPaymentMethodId;
	private static String sbPaymentSessionId;
	private static String sbPaymentTransactionId;
	private static String sbPaymentOfflineTransactionId;
	private SbReconcileDataRequest reconcileDataRequest;
	private SbPaymentSettlementSessionRequest settlementDataExpect;
	private SbSpeed speed;

	@BeforeEach
	void setUp() {
		Long offlineReconcileTimestamp = Instant.now()
				.getEpochSecond();

		speed = SbSpeed.builder()
				.tapCardTime(548L)
				.checkSignalTime(0L)
				.openSocketTime(0L)
				.handshakingTime(0L)
				.sendToReceivedResponse(0L)
				.startToFinishTxnTime(1762L)
				.build();

		List<SbTransactionItem> items = List.of(SbTransactionItem.builder()
				.freeEntry(false)
				.itemId(1001L)
				.itemName("Item 1")
				.quantity(2)
				.itemPrice(new java.math.BigInteger("500"))
				.build(), SbTransactionItem.builder()
						.freeEntry(false)
						.itemId(1002L)
						.itemName("Item 2")
						.quantity(3)
						.itemPrice(new java.math.BigInteger("1000"))
						.build());

		settlementDataExpect = SbPaymentSettlementSessionRequest.builder()
				.amount(new BigInteger("3500"))
				.cardId("CARD12345")
				.cardType(SbCardType.NETS2_0)
				.sofType(SbSofType.SSS)
				.cepasSettlementData("CEPAS_DATA_01")
				.errorCode(0)
				.acquirerErrorCode("00")
				.items(items)
				.offlineSEQ("OFFLINE_SEQ_001")
				.paymentMode(SbPaymentMode.ONLINE)
				.posNetworkType("4G")
				.posCellBand("BAND_3")
				.posSignalStrength("-70dBm")
				.readerAccessTech("NFC")
				.readerCellBand("BAND_7")
				.readerSignalStrength(-65)
				.readerTid("READER_TID_001")
				.readerMid("READER_MID_001")
				.readerId("READER_ID_001")
				.schoolId("01")
				.storeId("STORE_001")
				.storeName("Main Cafeteria")
				.speed(speed)
				.stan(123456)
				.timestamp(Instant.now()
						.toEpochMilli())
				.build();

		reconcileDataRequest = SbReconcileDataRequest.builder()
				.acquirerErrorCode("01")
				.errorCode(01)
				.offlineReconcileTimestamp(offlineReconcileTimestamp)
				.offlineSEQ("123")
				.build();
	}

	@Test
	@Order(0)
	public void encounterErrorDuringReconciliation_shouldReturnErrorResponse() {
		createSmartBuddyPaymentMethod();
		createSmartBuddyPaymentSession();
		createSmartBuddyPaymentSettlement();

		doThrow(new RuntimeException("Internal server error")).when(repository)
				.saveTransaction(any());
		List<ReconcileTransactionRequest> transactions = List.of(ReconcileTransactionRequest.builder()
				.transactionId(sbPaymentTransactionId)
				.reconcileData(reconcileDataRequest)
				.build());
		ReconcileTransactionListRequest request = ReconcileTransactionListRequest.builder()
				.transactions(transactions)
				.build();
		// Act & Assert
		webClient.put()
				.uri("/api/payment/transactions/reconcile")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(new ParameterizedTypeReference<List<ReconciliationResponse>>() {
				})
				.consumeWith(response -> {
					List<ReconciliationResponse> list = response.getResponseBody();
					assertTrue(list.size() == 1);
					ReconciliationResponse reconciledTransaction = list.get(0);
					assertEquals(sbPaymentTransactionId, reconciledTransaction.transactionId());
					assertEquals(ReconciliationStatus.FAILED, reconciledTransaction.status());
				});
	}

	@Test
	@Order(1)
	public void testReconcileTransactions_shouldReturnReconciliationResponses() {
		String notFoundTransactionId = "9999999999999";
		List<ReconcileTransactionRequest> transactions = List.of(ReconcileTransactionRequest.builder()
				.transactionId(sbPaymentTransactionId)
				.reconcileData(reconcileDataRequest)
				.build(), ReconcileTransactionRequest.builder()
						.transactionId(notFoundTransactionId)
						.reconcileData(reconcileDataRequest)
						.build());
		ReconcileTransactionListRequest request = ReconcileTransactionListRequest.builder()
				.transactions(transactions)
				.build();
		// Act & Assert
		webClient.put()
				.uri("/api/payment/transactions/reconcile")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(new ParameterizedTypeReference<List<ReconciliationResponse>>() {
				})
				.consumeWith(response -> {
					List<ReconciliationResponse> list = response.getResponseBody();
					assertTrue(list.size() == 2);
					Map<String, ReconciliationResponse> map = list.stream()
							.collect(Collectors.toMap(ReconciliationResponse::transactionId, r -> r));

					ReconciliationResponse reconciled = map.get(sbPaymentTransactionId);
					assertNotNull(reconciled);
					assertEquals(ReconciliationStatus.SUCCESS, reconciled.status());

					ReconciliationResponse notFound = map.get(notFoundTransactionId);
					assertNotNull(notFound);
					assertEquals(ReconciliationStatus.TRANSACTION_NOT_FOUND, notFound.status());
				});
	}

	@Test
	@Order(2)
	public void reconcileAnAlreadyReconciledTransaction_shouldReturnErrorResponse() {
		List<ReconcileTransactionRequest> transactions = List.of(ReconcileTransactionRequest.builder()
				.transactionId(sbPaymentTransactionId)
				.reconcileData(reconcileDataRequest)
				.build());
		ReconcileTransactionListRequest request = ReconcileTransactionListRequest.builder()
				.transactions(transactions)
				.build();
		// Act & Assert
		webClient.put()
				.uri("/api/payment/transactions/reconcile")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(new ParameterizedTypeReference<List<ReconciliationResponse>>() {
				})
				.consumeWith(response -> {
					List<ReconciliationResponse> list = response.getResponseBody();
					assertTrue(list.size() == 1);
					ReconciliationResponse reconciledTransaction = list.get(0);
					assertEquals(sbPaymentTransactionId, reconciledTransaction.transactionId());
					assertEquals(ReconciliationStatus.TRANSACTION_ALREADY_RECONCILED, reconciledTransaction.status());
				});
	}

	@Test
	@Order(3)
	void testCreateOfflinePaymentTransactionSuccessfullyWhenValidRequest() {
		// Arrange
		final var request = CreateOfflineTransactionRequest.builder()
				.offlineIdempotencyKey("offline-idempotency-key-01")
				.description("description")
				.paymentReference("paymentReference")
				.customerId("1234567890001")
				.userCardId("card-id-1234567890001")
				.customerEmail("<EMAIL>")
				.customerName("customer-1234567890001-name")
				.deviceId("device-id-1234567890001")
				.currencyCode("SGD")
				.amount(2222L)
				.netAmount(2222L)
				.fee(0L)
				.paymentMethodId(Long.valueOf(sbPaymentMethodId))
				.paymentProcessorId(PaymentProcessorId.SMART_BUDDY_PAYMENT)
				.surchargeRate(new BigDecimal("0.00"))
				.initiatedAt(Instant.now())
				.paymentMethodDisplayName("Smart Buddy Payment Title")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.transactionRemark(PaymentTransactionRemark.RECONCILIATION_REQUIRED)
				.paidAt(Instant.now())
				.transactionNumber("transaction-number-customer-1234567890001")
				.deviceTransactionNumber("device-number-customer-1234567890001")
				.settlementData(settlementDataExpect)
				.build();

		final var offlinePaymentTransactionCreatedEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.payments.avro.model.PaymentOfflineTransactionCreatedEventAvroModel.class);

		// Act & Assert
		webClient.post()
				.uri("/api/payment/transactions/offline")
				.contentType(MediaType.APPLICATION_JSON)
				.headers(this::setHeaders)
				.bodyValue(request)
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentTransactionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(sbPaymentMethodId, actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.SMART_BUDDY_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals(request.getPaymentReference(), actual.getPaymentReference());
					Assertions.assertEquals(request.getCustomerId(), actual.getCustomerId());
					Assertions.assertEquals(request.getCustomerEmail(), actual.getCustomerEmail());
					Assertions.assertEquals(request.getCustomerName(), actual.getCustomerName());
					Assertions.assertEquals(request.getCurrencyCode(), actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(request.getDescription(), actual.getDescription());
					Assertions.assertEquals(request.getDeviceId(), actual.getDeviceId());
					Assertions.assertEquals(request.getUserCardId(), actual.getUserCardId());
					Assertions.assertEquals(request.getTransactionNumber(), actual.getTransactionNumber());
					Assertions.assertEquals(request.getPaidAt()
							.toEpochMilli(), actual.getPaidAt()
									.toEpochMilli());
					Assertions.assertEquals(request.getOfflineIdempotencyKey(), actual.getOfflineIdempotencyKey());
					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());
					Assertions.assertEquals(PaymentTransactionRemark.RECONCILIATION_REQUIRED, actual
							.getTransactionRemark());

					final var transaction = transactionQueryService.getTransactionByTenantIdAndId(new TenantId(2L),
							MapstructCommonDomainMapper.INSTANCE.longToPaymentTransactionId(Long.valueOf(actual
									.getId())));

					verify(kafkaOfflineTransactionCreatedEventPublisher, times(1)).publish(
							offlinePaymentTransactionCreatedEventCaptor.capture());
					final var offlineTransactionCreatedEvent = offlinePaymentTransactionCreatedEventCaptor.getValue();

					Assertions.assertEquals(actual.getPaymentSessionId(), offlineTransactionCreatedEvent
							.getPaymentSessionId());
					Assertions.assertEquals(Long.valueOf(sbPaymentMethodId), offlineTransactionCreatedEvent
							.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.SMART_BUDDY_PAYMENT.name(),
							offlineTransactionCreatedEvent.getPaymentProcessorId());
					Assertions.assertEquals(transaction.getPaymentReference(), offlineTransactionCreatedEvent
							.getPaymentReference());
					Assertions.assertEquals(transaction.getCustomerId(), offlineTransactionCreatedEvent
							.getCustomerId());
					Assertions.assertEquals(transaction.getCustomerEmail(), offlineTransactionCreatedEvent
							.getCustomerEmail());
					Assertions.assertEquals(transaction.getCustomerName(), offlineTransactionCreatedEvent
							.getCustomerName());
					Assertions.assertEquals(transaction.getCurrencyCode(), offlineTransactionCreatedEvent
							.getCurrencyCode());
					Assertions.assertEquals(transaction.getDeviceId(), offlineTransactionCreatedEvent.getDeviceId());
					Assertions.assertEquals(transaction.getAmount(), offlineTransactionCreatedEvent.getAmount());
					Assertions.assertEquals(transaction.getNetAmount(), offlineTransactionCreatedEvent.getNetAmount());
					Assertions.assertEquals(transaction.getFee(), offlineTransactionCreatedEvent.getFee());
					Assertions.assertEquals(transaction.getSystemSource(), offlineTransactionCreatedEvent
							.getSystemSource());
					Assertions.assertEquals(transaction.getDescription(), offlineTransactionCreatedEvent
							.getDescription());
					Assertions.assertEquals(com.styl.pacific.kafka.payments.avro.model.PaymentTransactionType.PURCHASE,
							offlineTransactionCreatedEvent.getTransactionType());
					Assertions.assertEquals(transaction.getPaidAt()
							.toEpochMilli(), offlineTransactionCreatedEvent.getPaidAt());
					Assertions.assertEquals(transaction.getAppliedFixedSurcharge(), offlineTransactionCreatedEvent
							.getAppliedFixedSurcharge());

					Assertions.assertEquals(0, transaction.getAppliedSurchargeRate()
							.compareTo(offlineTransactionCreatedEvent.getAppliedSurchargeRate()));

					Assertions.assertEquals(transaction.getOfflineIdempotencyKey()
							.getValue(), offlineTransactionCreatedEvent.getOfflineIdempotencyKey());
					Assertions.assertEquals(transaction.getUserCardId(), offlineTransactionCreatedEvent
							.getUserCardId());
					sbPaymentOfflineTransactionId = actual.getId();

				});
	}

	@Test
	@Order(4)
	public void testReconcileOfflineTransactions_shouldReturnReconciliationResponses() {
		String notFoundTransactionId = "9999999999999";
		List<ReconcileTransactionRequest> transactions = List.of(ReconcileTransactionRequest.builder()
				.transactionId(sbPaymentOfflineTransactionId)
				.reconcileData(reconcileDataRequest)
				.build(), ReconcileTransactionRequest.builder()
						.transactionId(notFoundTransactionId)
						.reconcileData(reconcileDataRequest)
						.build());
		ReconcileTransactionListRequest request = ReconcileTransactionListRequest.builder()
				.transactions(transactions)
				.build();
		// Act & Assert
		webClient.put()
				.uri("/api/payment/transactions/reconcile")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(new ParameterizedTypeReference<List<ReconciliationResponse>>() {
				})
				.consumeWith(response -> {
					List<ReconciliationResponse> list = response.getResponseBody();
					assertTrue(list.size() == 2);
					Map<String, ReconciliationResponse> map = list.stream()
							.collect(Collectors.toMap(ReconciliationResponse::transactionId, r -> r));

					ReconciliationResponse reconciled = map.get(sbPaymentOfflineTransactionId);
					assertNotNull(reconciled);
					assertEquals(ReconciliationStatus.SUCCESS, reconciled.status());

					ReconciliationResponse notFound = map.get(notFoundTransactionId);
					assertNotNull(notFound);
					assertEquals(ReconciliationStatus.TRANSACTION_NOT_FOUND, notFound.status());
				});
	}

	private void createSmartBuddyPaymentMethod() {
		final var sbConfig = SbPaymentMethodConfigurationRequest.builder()
				.sssEnable(true)
				.apiKey(SSS_API_KEY)
				.secretKey(SSS_SECRET_KEY)
				.expiresAt(SSS_EXPIRY)
				.schoolId("01")
				.schoolName("Test school")
				.build();

		final var request = CreatePaymentMethodRequest.builder()
				.displayName("Smart Buddy Payment")
				.processorId(PaymentProcessorId.SMART_BUDDY_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.isActive(Boolean.TRUE)
				.description("Smart Buddy Payment Description")
				.paymentInstruction("Smart Buddy Payment Instruction")
				.surchargeRate(new BigDecimal("0.00"))
				.fixedSurcharge(0L)
				.currencyCode("SGD")
				.surchargeTitle("Smart Buddy Payment Title")
				.processorConfig(sbConfig)
				.build();

		when(tenantClient.getTenantSettings(anyLong())).thenReturn(ResponseEntity.ok(TenantSettingsResponse.builder()
				.currency(CurrencyResponse.builder()
						.currencyCode(request.getCurrencyCode())
						.build())
				.build()));

		SbApiKeyResponse sbTerminalKey = SbApiKeyResponse.builder()
				.fasApiKey(SSS_TERMINAL_API_KEY)
				.fasSecretKey(SSS_TERMINAL_SECRET_KEY)
				.expiry(SSS_EXPIRY)
				.build();
		when(sbConnector.getTerminalApiKey(any(String.class), any(String.class))).thenReturn(sbTerminalKey);

		// Act & Assert
		webClient.post()
				.uri("/api/payment/methods")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentMethodResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					assertNotNull(actual);
					sbPaymentMethodId = actual.getId();
				});
	}

	private void createSmartBuddyPaymentSession() {
		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(Long.valueOf(sbPaymentMethodId))
				.description("Payment Order #123456")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1000L)
				.netAmount(1000L)
				.fee(0L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.merchantName("Merchant Name")
				.systemSource("System Source 01")
				.processorParams(SbPaymentSessionParamsRequest.builder()
						.expiredInMilliseconds(Duration.ofMinutes(10)
								.toMillis())
						.build())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(sbPaymentMethodId, actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.SMART_BUDDY_PAYMENT, actual.getPaymentProcessorId());
					sbPaymentSessionId = actual.getPaymentSessionId();
				});
	}

	private void createSmartBuddyPaymentSettlement() {
		final var request = SettlePaymentSessionRequest.builder()
				.paidAt(Instant.now())
				.settlementData(settlementDataExpect)
				.transactionNumber("TXN No 1234")
				.extraSettlementData(Map.of("value3", "03", "value4", "04"))
				.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.transactionRemark(PaymentTransactionRemark.RECONCILIATION_REQUIRED)
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions/{paymentSessionId}/settle", sbPaymentSessionId)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentTransactionResponse.class)
				.consumeWith(response -> {
					final var actualTransaction = response.getResponseBody();
					assertNotNull(actualTransaction);
					Assertions.assertTrue(StringUtils.isNotBlank(actualTransaction.getPaymentSessionId()));
					Assertions.assertEquals(sbPaymentMethodId, actualTransaction.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.SMART_BUDDY_PAYMENT, actualTransaction
							.getPaymentProcessorId());
					SbPaymentSettlementResponse sbPaymentSettlementResponse = objectMapper.convertValue(
							actualTransaction.getSettlementData(), SbPaymentSettlementResponse.class);
					assertEquals(settlementDataExpect.getAmount(), sbPaymentSettlementResponse.getAmount());
					assertEquals(settlementDataExpect.getCardId(), sbPaymentSettlementResponse.getCardId());
					assertEquals(settlementDataExpect.getCardType(), sbPaymentSettlementResponse.getCardType());
					assertEquals(settlementDataExpect.getSofType(), sbPaymentSettlementResponse.getSofType());
					assertEquals(settlementDataExpect.getCepasSettlementData(), sbPaymentSettlementResponse
							.getCepasSettlementData());
					assertEquals(settlementDataExpect.getErrorCode(), sbPaymentSettlementResponse.getErrorCode());
					assertEquals(settlementDataExpect.getAcquirerErrorCode(), sbPaymentSettlementResponse
							.getAcquirerErrorCode());
					assertEquals(settlementDataExpect.getOfflineSEQ(), sbPaymentSettlementResponse.getOfflineSEQ());
					assertEquals(settlementDataExpect.getPaymentMode(), sbPaymentSettlementResponse.getPaymentMode());
					assertEquals(settlementDataExpect.getPosNetworkType(), sbPaymentSettlementResponse
							.getPosNetworkType());
					assertEquals(settlementDataExpect.getPosCellBand(), sbPaymentSettlementResponse.getPosCellBand());
					assertEquals(settlementDataExpect.getPosSignalStrength(), sbPaymentSettlementResponse
							.getPosSignalStrength());
					assertEquals(settlementDataExpect.getReaderAccessTech(), sbPaymentSettlementResponse
							.getReaderAccessTech());
					assertEquals(settlementDataExpect.getReaderCellBand(), sbPaymentSettlementResponse
							.getReaderCellBand());
					assertEquals(settlementDataExpect.getReaderSignalStrength(), sbPaymentSettlementResponse
							.getReaderSignalStrength());
					assertEquals(settlementDataExpect.getReaderTid(), sbPaymentSettlementResponse.getReaderTid());
					assertEquals(settlementDataExpect.getReaderMid(), sbPaymentSettlementResponse.getReaderMid());
					assertEquals(settlementDataExpect.getReaderId(), sbPaymentSettlementResponse.getReaderId());
					assertEquals(settlementDataExpect.getSchoolId(), sbPaymentSettlementResponse.getSchoolId());
					assertEquals(settlementDataExpect.getStoreId(), sbPaymentSettlementResponse.getStoreId());
					assertEquals(settlementDataExpect.getStoreName(), sbPaymentSettlementResponse.getStoreName());
					assertEquals(speed, sbPaymentSettlementResponse.getSpeed());
					sbPaymentTransactionId = actualTransaction.getId();
				});
	}

	private void setHeaders(HttpHeaders headers) {
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(2));
	}
}
