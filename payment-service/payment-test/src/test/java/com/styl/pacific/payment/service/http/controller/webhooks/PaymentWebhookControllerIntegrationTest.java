/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.http.controller.webhooks;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stripe.model.WebhookEndpoint;
import com.styl.pacific.common.test.utils.GenerateHttpHeader;
import com.styl.pacific.common.test.utils.HeaderGenerator;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.config.IntegrationTestConfiguration;
import com.styl.pacific.payment.service.config.PaymentIntegrationTestContainer;
import com.styl.pacific.payment.service.config.PaymentMethodInitIntegrationSupporter;
import com.styl.pacific.payment.service.core.features.methods.PaymentMethodCommandService;
import com.styl.pacific.payment.service.core.features.methods.request.UpsertPaymentMethodCommand;
import com.styl.pacific.payment.service.data.access.clients.tenants.TenantClient;
import com.styl.pacific.payment.service.integration.stripe.clients.StripeApiClient;
import com.styl.pacific.payment.service.integration.stripe.config.StripePaymentMethodConfiguration;
import com.styl.pacific.payment.service.integration.stripe.enums.StripeEventType;
import com.styl.pacific.payment.shared.enums.AcceptedApplication;
import com.styl.pacific.payment.shared.http.methods.request.stripe.StripePaymentMethodConfigurationRequest;
import com.styl.pacific.payment.shared.http.webhooks.response.PaymentWebhookEndpointResponse;
import com.styl.pacific.payment.spi.processors.methods.valueobject.SimplePaymentMethodConfiguration;
import com.styl.pacific.payment.spi.processors.webhooks.valueobject.PaymentWebhookEndpointConfig;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
class PaymentWebhookControllerIntegrationTest extends PaymentIntegrationTestContainer {

	@MockitoBean
	private StripeApiClient stripeApiClient;

	@MockitoBean
	private TenantClient tenantClient;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private PaymentMethodCommandService methodCommandService;

	private PaymentMethodInitIntegrationSupporter paymentMethodSupporter;

	@BeforeEach
	void setUp() {
		paymentMethodSupporter = new PaymentMethodInitIntegrationSupporter(tenantClient, methodCommandService);
	}

	@Test
	void testGenerateWebhookWhenValidRequest() {
		final var tenantId = new TenantId(10_000L);
		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.1234))
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var webhookPaymentIntentEndpoint = new WebhookEndpoint();
		webhookPaymentIntentEndpoint.setId("123");
		webhookPaymentIntentEndpoint.setApiVersion("2024-06-20");
		webhookPaymentIntentEndpoint.setUrl("http://webhook.url");
		webhookPaymentIntentEndpoint.setSecret("Secret");
		webhookPaymentIntentEndpoint.setEnabledEvents(List.of(StripeEventType.PAYMENT_INTENT_SUCCEEDED.getEvent()));

		when(stripeApiClient.createWebhookEndpoint(any(Long.class), eq(StripeEventType.PAYMENT_INTENT_SUCCEEDED
				.getEvent()), any(String.class), any(StripePaymentMethodConfiguration.class), any(
						PaymentWebhookEndpointConfig.class))).thenReturn(webhookPaymentIntentEndpoint);

		final var webhookCheckoutWebhookEndpoint = new WebhookEndpoint();
		webhookCheckoutWebhookEndpoint.setId("1234");
		webhookCheckoutWebhookEndpoint.setApiVersion("2024-06-20");
		webhookCheckoutWebhookEndpoint.setUrl("http://webhook.url");
		webhookCheckoutWebhookEndpoint.setSecret("Secret");
		webhookCheckoutWebhookEndpoint.setEnabledEvents(List.of(StripeEventType.CHECKOUT_SESSION_ASYNC_PAYMENT_SUCCEEDED
				.getEvent()));
		when(stripeApiClient.createWebhookEndpoint(any(Long.class), eq(
				StripeEventType.CHECKOUT_SESSION_ASYNC_PAYMENT_SUCCEEDED.getEvent()), any(String.class), any(
						StripePaymentMethodConfiguration.class), any(PaymentWebhookEndpointConfig.class))).thenReturn(
								webhookCheckoutWebhookEndpoint);

		// Act & Assert
		webClient.post()
				.uri("/api/payment/methods/{methodId}/webhook-endpoints", stripePaymentMethod.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(List.class)
				.consumeWith(response -> {
					final List<PaymentWebhookEndpointResponse> actual = response.getResponseBody()
							.stream()
							.map(it -> objectMapper.convertValue(it, PaymentWebhookEndpointResponse.class))
							.toList();

					actual.forEach(endpoint -> {
						Assertions.assertEquals(tenantId.getValue()
								.toString(), endpoint.getTenantId());
						Assertions.assertEquals(stripePaymentMethod.getId()
								.getValue()
								.toString(), endpoint.getPaymentMethodId());
						Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, endpoint
								.getPaymentProcessorId());

						Assertions.assertEquals("2024-06-20", endpoint.getClientWebhookVersion());
						Assertions.assertTrue(List.of(StripeEventType.CHECKOUT_SESSION_ASYNC_PAYMENT_SUCCEEDED
								.getEvent(), StripeEventType.PAYMENT_INTENT_SUCCEEDED.getEvent())
								.contains(endpoint.getEventType()));
					});

				});
	}

}
