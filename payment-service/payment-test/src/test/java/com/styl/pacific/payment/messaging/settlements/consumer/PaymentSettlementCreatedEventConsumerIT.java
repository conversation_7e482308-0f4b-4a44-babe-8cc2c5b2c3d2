/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.messaging.settlements.consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.domain.valueobject.PaymentTransactionId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.kafka.payments.avro.model.PaymentSettlementCreatedEventAvroModel;
import com.styl.pacific.kafka.payments.avro.model.PaymentTransactionStatus;
import com.styl.pacific.kafka.payments.avro.model.PaymentTransactionType;
import com.styl.pacific.kafka.producer.service.KafkaProducer;
import com.styl.pacific.payment.service.config.IntegrationTestConfiguration;
import com.styl.pacific.payment.service.config.PaymentIntegrationTestContainer;
import com.styl.pacific.payment.service.core.features.settlements.valueobject.PaymentSettlementData;
import com.styl.pacific.payment.service.core.features.transactions.PaymentTransactionQueryService;
import com.styl.pacific.payment.service.core.features.transactions.entities.PaymentTransaction;
import com.styl.pacific.payment.service.integration.smartbuddy.client.CalebClient;
import com.styl.pacific.payment.service.integration.smartbuddy.client.request.CalebTransactionItem;
import com.styl.pacific.payment.service.integration.smartbuddy.client.request.CreateCalebTransactionClientRequest;
import com.styl.pacific.payment.service.integration.smartbuddy.mapper.SettlementDataMapper;
import com.styl.pacific.payment.service.integration.smartbuddy.settlements.valueobject.SbPaymentSettlementData;
import com.styl.pacific.payment.shared.enums.CalebCardType;
import com.styl.pacific.payment.shared.enums.CalebSubmitTransactionStatus;
import com.styl.pacific.payment.shared.enums.CalebTransactionSource;
import com.styl.pacific.payment.shared.enums.SbCardType;
import com.styl.pacific.payment.shared.enums.SbPaymentMode;
import com.styl.pacific.payment.shared.enums.SbSofType;
import com.styl.pacific.payment.shared.exceptions.PaymentTransactionCalebSubmissionException;
import com.styl.pacific.payment.shared.http.settlement.request.smartbuddy.SbSpeed;
import com.styl.pacific.payment.shared.http.settlement.request.smartbuddy.SbTransactionItem;
import com.styl.pacific.payment.spi.processors.smartbuddy.connector.SbConnector;
import com.styl.pacific.payment.spi.processors.smartbuddy.response.SbCalebTransactionResponse;
import java.math.BigInteger;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.support.SendResult;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
public class PaymentSettlementCreatedEventConsumerIT extends PaymentIntegrationTestContainer {

	private static final Long TENANT_ID = 2L;
	private static String PAYMENT_TRANSACTION_ID = "255982434026527744";

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private KafkaProducer<UUID, PaymentSettlementCreatedEventAvroModel> producer;

	@Value(value = "${pacific.kafka.publishers.payment-service.payment-settlement-created-event.topic-name}")
	private String TOPIC;
	@Value("${pacific.clients.caleb.secret-key}")
	private String secretKey;

	@MockitoSpyBean
	private CalebClient calebClient;

	@MockitoSpyBean
	private SbConnector sbConnector;

	@MockitoSpyBean
	private PaymentSettlementCreatedEventConsumer settlementCreatedEventConsumer;

	@MockitoBean
	private PaymentTransactionQueryService paymentTransactionQueryService;

	private List<SbTransactionItem> items;
	private Map<String, Object> settlementDataExpect;
	private SbSpeed speed;

	@BeforeEach
	void setup() {

		speed = SbSpeed.builder()
				.tapCardTime(548L)
				.checkSignalTime(0L)
				.openSocketTime(0L)
				.handshakingTime(0L)
				.sendToReceivedResponse(0L)
				.startToFinishTxnTime(1762L)
				.build();

		items = List.of(SbTransactionItem.builder()
				.freeEntry(false)
				.itemId(1001L)
				.itemName("Item 1")
				.quantity(2)
				.itemPrice(new java.math.BigInteger("500"))
				.build(), SbTransactionItem.builder()
						.freeEntry(false)
						.itemId(1002L)
						.itemName("Item 2")
						.quantity(3)
						.itemPrice(new java.math.BigInteger("1000"))
						.build());
		settlementDataExpect = Map.ofEntries(Map.entry("amount", BigInteger.valueOf(1000)), Map.entry("cardId",
				"card-12345"), Map.entry("cardType", SbCardType.EZLINK), Map.entry("sofType", SbSofType.EZLINK), Map
						.entry("cepasSettlementData", "some-data"), Map.entry("errorCode", 0), Map.entry(
								"acquirerErrorCode", "00"), Map.entry("items", items), Map.entry("offlineSEQ",
										"offline-001"), Map.entry("paymentMode", SbPaymentMode.ONLINE), Map.entry(
												"posNetworkType", "4G"), Map.entry("posCellBand", "BAND_3"), Map.entry(
														"posSignalStrength", "-70"), Map.entry("readerAccessTech",
																"NFC"), Map.entry("readerCellBand", "BAND_3"), Map
																		.entry("readerSignalStrength", 80), Map.entry(
																				"readerTid", "TID12345"), Map.entry(
																						"readerMid", "MID12345"), Map
																								.entry("readerId",
																										"READER12345"),
				Map.entry("schoolId", "SCHOOL123"), Map.entry("storeId", "STORE123"), Map.entry("storeName",
						"Store Name"), Map.entry("speed", speed), Map.entry("stan", 123456), Map.entry("timestamp",
								Instant.now()
										.toEpochMilli()));
		PaymentSettlementData settlementData = PaymentSettlementData.builder()
				.data(settlementDataExpect)
				.build();
		PaymentTransaction transaction = PaymentTransaction.builder()
				.settlementData(settlementData)
				.build();
		when(paymentTransactionQueryService.getTransactionByTenantIdAndId(any(TenantId.class), any(
				PaymentTransactionId.class))).thenReturn(transaction);
	}

	@Test
	@Order(0)
	void shouldProcessPaymentSettlementCreatedEventSuccess() throws Exception {
		// Arrange
		PaymentSettlementCreatedEventAvroModel settlementEvent = PaymentSettlementCreatedEventAvroModel.newBuilder()
				.setId(UUID.randomUUID())
				.setTenantId(TENANT_ID)
				.setPaymentTransactionId(PAYMENT_TRANSACTION_ID)
				.setPaymentSessionId("session-12345")
				.setPaymentMethodId(123L)
				.setTransactionType(PaymentTransactionType.PURCHASE)
				.setTransactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.setPaymentMethodDisplayName("SMART_BUDDY")
				.setPaymentProcessorId(PaymentProcessorId.SMART_BUDDY_PAYMENT.name())
				.setCurrencyCode("USD")
				.setAmount(1000L)
				.setNetAmount(1000L)
				.setIsAsync(false)
				.setSessionVersion(Instant.now()
						.toEpochMilli())
				.setPaidAt(Instant.now()
						.toEpochMilli())
				.setInitiatedAt(Instant.now()
						.toEpochMilli())
				.setCreatedAt(Instant.now()
						.toEpochMilli())
				.build();
		// Act
		CompletableFuture<SendResult<UUID, PaymentSettlementCreatedEventAvroModel>> completableFuture = new CompletableFuture<>();
		completableFuture.orTimeout(5000, TimeUnit.MILLISECONDS);
		producer.send(TOPIC, settlementEvent.getId(), settlementEvent, (uuidTenantCreatedEventAvroModelSendResult,
				throwable) -> {
			if (throwable != null) {
				completableFuture.completeExceptionally(throwable);

			} else {
				completableFuture.complete(uuidTenantCreatedEventAvroModelSendResult);
			}
		});

		// Assert
		assertNotNull(completableFuture.get());

		ArgumentCaptor<String> secretKeyCaptor = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<CreateCalebTransactionClientRequest> requestCaptor = ArgumentCaptor.forClass(
				CreateCalebTransactionClientRequest.class);
		verify(calebClient, timeout(5000).times(1)).submitTransaction(secretKeyCaptor.capture(), requestCaptor
				.capture());

		String capturedSecretKey = secretKeyCaptor.getValue();
		CreateCalebTransactionClientRequest capturedRequest = requestCaptor.getValue();
		SbPaymentSettlementData expected = objectMapper.convertValue(settlementDataExpect,
				SbPaymentSettlementData.class);

		// Optionally, verify values
		assertEquals(secretKey, capturedSecretKey);
		assertNotNull(capturedRequest);
		assertEquals(settlementEvent.getPaymentTransactionId(), capturedRequest.id());
		assertEquals(expected.getReaderTid(), capturedRequest.terminalId());
		assertEquals(expected.getReaderMid(), capturedRequest.merchantId());
		assertEquals(expected.getCardId(), capturedRequest.cardId());
		assertEquals(CalebCardType.EZ_LINK.getMappedValue(), capturedRequest.cardType());
		assertEquals(CalebTransactionSource.TERABITE, capturedRequest.txnSource());
		assertEquals(expected.getSchoolId(), capturedRequest.schoolCode());
		assertEquals(expected.getAmount(), capturedRequest.amount());
		assertEquals(expected.getStan(), capturedRequest.stan());
		assertEquals(expected.getTimestamp(), capturedRequest.timestamp());
		assertEquals(expected.getStoreId(), capturedRequest.itemStoreId());
		assertEquals(expected.getStoreName(), capturedRequest.itemStoreName());
		assertEquals(totalQuantity(items), capturedRequest.totalQuantity());
		assertEquals(2, capturedRequest.items()
				.size());
		items.stream()
				.forEach(i -> {
					CalebTransactionItem calebTransactionItem = SettlementDataMapper.INSTANCE.toCalebTransactionItem(i);
					boolean found = capturedRequest.items()
							.stream()
							.anyMatch(r -> r.itemId()
									.equals(calebTransactionItem.itemId()) && r.itemName()
											.equals(calebTransactionItem.itemName()) && r.quantity()
													.equals(calebTransactionItem.quantity()) && r.price()
															.equals(calebTransactionItem.price()));
					assertEquals(true, found);
				});
	}

	@Test
	@Order(1)
	void shouldProcessOnlySmartBuddyEvent() throws Exception {
		Map<String, String> settlementDataExpect = Map.ofEntries(Map.entry("abc", "123"), Map.entry("xyz", "456"));
		PaymentSettlementCreatedEventAvroModel settlementEvent = PaymentSettlementCreatedEventAvroModel.newBuilder()
				.setId(UUID.randomUUID())
				.setTenantId(TENANT_ID)
				.setPaymentTransactionId(PAYMENT_TRANSACTION_ID)
				.setPaymentSessionId("session-12345")
				.setPaymentMethodId(123L)
				.setTransactionType(PaymentTransactionType.PURCHASE)
				.setTransactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.setPaymentMethodDisplayName("SMART_BUDDY")
				.setPaymentProcessorId(PaymentProcessorId.NETS_TERMINAL_PAYMENT.name())
				.setCurrencyCode("USD")
				.setAmount(1000L)
				.setNetAmount(1000L)
				.setIsAsync(false)
				.setSessionVersion(Instant.now()
						.toEpochMilli())
				.setPaidAt(Instant.now()
						.toEpochMilli())
				.setInitiatedAt(Instant.now()
						.toEpochMilli())
				.setCreatedAt(Instant.now()
						.toEpochMilli())
				.build();
		// Act
		CompletableFuture<SendResult<UUID, PaymentSettlementCreatedEventAvroModel>> completableFuture = new CompletableFuture<>();
		completableFuture.orTimeout(5000, TimeUnit.MILLISECONDS);
		producer.send(TOPIC, settlementEvent.getId(), settlementEvent, (uuidTenantCreatedEventAvroModelSendResult,
				throwable) -> {
			if (throwable != null) {
				completableFuture.completeExceptionally(throwable);

			} else {
				completableFuture.complete(uuidTenantCreatedEventAvroModelSendResult);
			}
		});

		// Assert
		assertNotNull(completableFuture.get());

		verify(sbConnector, timeout(5000).times(0)).submitTransactionToCaleb(any());
	}

	@Test
	@Order(2)
	void shouldThrowExceptionWhenSubmittingTransactionToCalebFails() throws ExecutionException, InterruptedException {
		SbCalebTransactionResponse response = SbCalebTransactionResponse.builder()
				.body("Failed to send TXN")
				.status(CalebSubmitTransactionStatus.failed)
				.build();
		doReturn(response).when(calebClient)
				.submitTransaction(any(), any());

		PaymentSettlementCreatedEventAvroModel settlementEvent = PaymentSettlementCreatedEventAvroModel.newBuilder()
				.setId(UUID.randomUUID())
				.setTenantId(TENANT_ID)
				.setPaymentTransactionId(PAYMENT_TRANSACTION_ID)
				.setPaymentSessionId("session-12345")
				.setPaymentMethodId(123L)
				.setTransactionType(PaymentTransactionType.PURCHASE)
				.setTransactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.setPaymentMethodDisplayName("SMART_BUDDY")
				.setPaymentProcessorId(PaymentProcessorId.SMART_BUDDY_PAYMENT.name())
				.setCurrencyCode("USD")
				.setAmount(1000L)
				.setNetAmount(1000L)
				.setIsAsync(false)
				.setSessionVersion(Instant.now()
						.toEpochMilli())
				.setPaidAt(Instant.now()
						.toEpochMilli())
				.setInitiatedAt(Instant.now()
						.toEpochMilli())
				.setCreatedAt(Instant.now()
						.toEpochMilli())
				.build();
		// Act
		CompletableFuture<SendResult<UUID, PaymentSettlementCreatedEventAvroModel>> completableFuture = new CompletableFuture<>();
		completableFuture.orTimeout(5000, TimeUnit.MILLISECONDS);
		producer.send(TOPIC, settlementEvent.getId(), settlementEvent, (uuidTenantCreatedEventAvroModelSendResult,
				throwable) -> {
			if (throwable != null) {
				completableFuture.completeExceptionally(throwable);

			} else {
				completableFuture.complete(uuidTenantCreatedEventAvroModelSendResult);
			}
		});

		// Assert
		assertNotNull(completableFuture.get());

		Assertions.assertThrows(PaymentTransactionCalebSubmissionException.class, () -> settlementCreatedEventConsumer
				.receive(settlementEvent, null, null, null));
	}

	private int totalQuantity(List<SbTransactionItem> items) {
		return items.stream()
				.mapToInt(SbTransactionItem::quantity)
				.sum();
	}

	private String convertToJson(List<SbTransactionItem> items) throws JsonProcessingException {
		return objectMapper.writeValueAsString(items);
	}
}