---
server:
  port: 9206
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/plain
    min-response-size: 10240
logging:
  level:
    com.styl.pacific: INFO

management:
  tracing:
    sampling:
      probability: 1.0
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  endpoint:
    health:
      probes:
        enabled: true
      metrics:
        enabled: true
  endpoints:
    web:
      exposure:
        include: "*"

spring:
  application:
    name: payment-service
  jpa:
    open-in-view: false
    show-sql: false
    database-platform: com.styl.pacific.data.access.jpa.dialect.ExtendedPostgresDialect
    properties:
      hibernate:
        dialect: com.styl.pacific.data.access.jpa.dialect.ExtendedPostgresDialect
        jdbc:
          time_zone: UTC
          batch_size: 50
        format_sql: false
        generate_statistics: false
        order_inserts: true
        order_updates: true
        query:
          enable_lazy_load_no_trans: true
          fail_on_pagination_over_collection_fetch: true
          in_clause_parameter_padding: true
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
  datasource:
    url: **********************************************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
  flyway:
    enabled: true
    locations: classpath:db/migration
  cloud:
    openfeign:
      client:
        config:
          default:
            connectTimeout: 5000
            readTimeout: 5000
            loggerLevel: FULL
            errorDecoder: com.styl.pacific.common.feign.exception.PacificFeignErrorDecoder
            requestInterceptors:
              - com.styl.pacific.common.feign.interceptor.PlatformFeignHeaderForwarderInterceptor
  sql:
    init:
      platform: postgres
pacific:
  tracing:
    otlp:
      endpoint: http://jeager.svc.monitoring.svc.cluster.local:4317
  clients:
    tenant-service:
      url: http://tenant-svc.application.svc.cluster.local:9201
    user-service:
      url:  http://user-svc.application.svc.cluster.local:9202
    wallet-service:
      url: http://wallet-svc.application.svc.cluster.local:9210
    sss:
      url: https://fas-dev.styl.solutions
    caleb:
      url: https://caleb-vpc-endpoint
      secret-key: <caleb-secret-key>
  aws:
    s3:
      endpoint:
      region: ap-southeast-1
      accessKey:
      secretKey:
    dynamodb:
      init-table:
        enable: false
        packageName: com.styl.pacific.payment.service.data.access.dynamo
      endpoint:
      accessKey:
      secretKey:
      region: ap-southeast-1
  payment:
    session-expiry-job-config:
      enabled: true
      cron-expression: "* */5 * * * *"
      lock-at-least-for: PT5S
      lock-at-most-for: PT15M
      number-of-checking-partition: 48 # This means job would scan 2 days (48 partition scan).
      max-size-per-partition: 2000

  stripe-connect:
    init-webhook-endpoint-enabled: true
    platform-api-key: Stripe API Account Key # Stripe Secret Key, or Restricted Key including PaymentIntents(Read), Checkout Sessions (Write), All Connect resources (Write), Webhook Endpoints (Write), All Payment Links resources (Write)
    platform-webhook-domain: localhost # Domain only, no protocol
    onboarding-validation-check-enabled: true

  kafka:
    consumers:
      wallet-service:
        wallet-transaction-created-event:
          enabled: true
          group-id: payment-service-wallet-transaction-created-event
          topic-name: wallet-service-wallet-transaction-queue
          retry-attempts: 5
          retry-interval-ms: 3500
      payment-service:
        payment-reversal-command:
          enabled: true
          group-id: payment-service-payment-reversal-command
          topic-name: payment-service-payment-reversal-command
          retry-attempts: 5
          retry-interval-ms: 3500
        payment-settlement-command-event:
          enabled: true
          group-id: payment-service-payment-settlement-command-event
          topic-name: payment-service-payment-settlement-command-event
          retry-attempts: 5
          retry-interval-ms: 3500
        payment-settlement-created-event:
          enabled: true
          group-id: caleb-group-payment-settlement-created-event
          topic-name: payment-service-payment-settlement-created-event
          retry-attempts: 5
          retry-interval-ms: 30000          # 30 seconds
          retry-max-delay-ms: 17280000      # 4.8 hours
    publishers:
      payment-service:
        payment-settlement-created-event:
          topic-name: payment-service-payment-settlement-created-event
        payment-settlement-command-event:
          topic-name: payment-service-payment-settlement-command-event
        payment-reversed-event:
          topic-name: payment-service-payment-reversed-event
        offline-transaction-created-event:
          topic-name: payment-service-offline-transaction-created-event
      notification-service:
        notification-created-event:
          topic-name: notification-service-notification-command
  schedule:
    payment:
      api-key-rotation-job:
        enabled: true
        cron-expression: "0 0 0 * * *" # At midnight every day
        lock-at-least-for: PT30S
        lock-at-most-for: PT15M
        batch-size: 50

kafka-config:
  bootstrap-servers: "localhost:19092, localhost:29092, localhost:39092"
  schema-registry-url-key: schema.registry.url
  schema-registry-url: http://localhost:8081
  num-of-partitions: 3
  replication-factor: 3
kafka-producer-config:
  key-serializer-class: org.apache.kafka.common.serialization.UUIDSerializer
  value-serializer-class: io.confluent.kafka.serializers.KafkaAvroSerializer
  compression-type: none
  acks: all
  batch-size: 16384
  batch-size-boost-factor: 100
  linger-ms: 5
  request-timeout-ms: 60000
  retry-count: 5
kafka-consumer-config:
  key-deserializer: org.apache.kafka.common.serialization.UUIDDeserializer
  value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
  auto-offset-reset: earliest
  specific-avro-reader-key: specific.avro.reader
  specific-avro-reader: true
  batch-listener: false
  auto-startup: true
  concurrency-level: 1
  session-timeout-ms: 10000
  heartbeat-interval-ms: 3000
  max-poll-interval-ms: 300000
  max-poll-records: 500
  max-partition-fetch-bytes-default: 1048576
  max-partition-fetch-bytes-boost-factor: 1
  poll-timeout-ms: 150