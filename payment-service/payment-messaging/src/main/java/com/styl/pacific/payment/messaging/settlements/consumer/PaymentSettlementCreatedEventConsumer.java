/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.messaging.settlements.consumer;

import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.domain.valueobject.PaymentTransactionId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.kafka.consumer.KafkaConsumer;
import com.styl.pacific.kafka.payments.avro.model.PaymentSettlementCreatedEventAvroModel;
import com.styl.pacific.payment.service.core.features.transactions.PaymentTransactionQueryService;
import com.styl.pacific.payment.service.core.features.transactions.entities.PaymentTransaction;
import com.styl.pacific.payment.spi.processors.smartbuddy.connector.SbConnector;
import com.styl.pacific.payment.spi.processors.smartbuddy.request.CreateCalebTransactionRequest;
import jakarta.annotation.PostConstruct;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "pacific.kafka.consumers.payment-service.payment-settlement-created-event.enabled", havingValue = "true")
public class PaymentSettlementCreatedEventConsumer implements
		KafkaConsumer<UUID, PaymentSettlementCreatedEventAvroModel> {
	private final SbConnector sbConnector;
	private final PaymentTransactionQueryService paymentTransactionQueryService;

	@PostConstruct
	public void init() {
		log.info("Constructed PaymentSettlementCreatedEventConsumer");
	}

	@Override
	@RetryableTopic(backoff = @Backoff(delayExpression = "#{${pacific.kafka.consumers.payment-service.payment-settlement-created-event.retry-interval-ms}}", multiplier = 4.9, maxDelayExpression = "#{${pacific.kafka.consumers.payment-service.payment-settlement-created-event.retry-max-delay-ms}}"), attempts = "${pacific.kafka.consumers.payment-service.payment-settlement-created-event.retry-attempts}", autoCreateTopics = "false", dltTopicSuffix = "-${pacific.kafka.consumers.payment-service.payment-settlement-created-event.group-id}-dlt", retryTopicSuffix = "-${pacific.kafka.consumers.payment-service.payment-settlement-created-event.group-id}-retry")
	@KafkaListener(groupId = "${pacific.kafka.consumers.payment-service.payment-settlement-created-event.group-id}", topics = "${pacific.kafka.consumers.payment-service.payment-settlement-created-event.topic-name}")
	public void receive(PaymentSettlementCreatedEventAvroModel event, UUID key, Integer partion, Long offset) {
		log.info("Received PaymentSettlementCreatedEventAvroModel [{}]", event.getTenantId());

		if (!PaymentProcessorId.SMART_BUDDY_PAYMENT.equals(event.getPaymentProcessorId())) {
			TenantId tenantId = MapstructCommonDomainMapper.INSTANCE.longToTenantId(event.getTenantId());
			PaymentTransactionId paymentTransactionId = MapstructCommonDomainMapper.INSTANCE
					.stringToPaymentTransactionId(event.getPaymentTransactionId());
			PaymentTransaction transaction = paymentTransactionQueryService.getTransactionByTenantIdAndId(tenantId,
					paymentTransactionId);
			sbConnector.submitTransactionToCaleb(CreateCalebTransactionRequest.builder()
					.transactionId(event.getPaymentTransactionId())
					.settlementData(transaction.getSettlementData()
							.getData())
					.build());
		}
	}
}
