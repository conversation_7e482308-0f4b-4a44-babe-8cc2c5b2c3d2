<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.styl.pacific.user.service</groupId>
        <artifactId>user-service</artifactId>
        <version>2.1.0</version>
    </parent>
    <artifactId>user-shared</artifactId>
    <properties>
        <maven.deploy.skip>false</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-validator</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.styl.pacific.notification.service</groupId>-->
        <!--            <artifactId>notification-shared</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>
</project>
