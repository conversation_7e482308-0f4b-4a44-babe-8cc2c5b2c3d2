/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class UserPreferenceKeyConstants {
	public static final String ACCOUNT_DATE_TIME_FORMAT = "ACCOUNT_DATE_TIME_FORMAT";
	public static final String SECURITY_MFA = "SECURITY_MFA";
	public static final String NOTIFICATION_PAYMENT_TRANSACTIONS_SUCCEEDED = "NOTIFICATION_PAYMENT_TRANSACTIONS_SUCCEEDED";
	public static final String NOTIFICATION_WALLET_BALANCE_CHANGED = "NOTIFICATION_WALLET_BALANCE_CHANGED";
	public static final String NOTIFICATION_ORDER_STATUS_UPDATED = "NOTIFICATION_ORDER_STATUS_UPDATED";
	public static final String NOTIFICATION_PRE_ORDER_CONFIRMED = "NOTIFICATION_PRE_ORDER_CONFIRMED";
}
