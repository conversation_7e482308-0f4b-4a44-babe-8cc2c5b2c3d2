/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.apis;

import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.user.shared.http.userbanneditems.request.SaveUserBannedItemRequest;
import com.styl.pacific.user.shared.http.userbanneditems.response.UserBannedItemResponse;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

public interface UserBannedItemApi {

	@PostMapping(path = "/api/user/users/{userId}/banned-items")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.CUSTOMER_BANNED_ITEM_MGMT_UPDATE, isAllowedCustomerAccess = true)
	List<UserBannedItemResponse> saveUserBannedItems(@PathVariable("userId") Long userId,
			@RequestBody @Valid SaveUserBannedItemRequest request);

	@GetMapping(path = "/api/user/users/{userId}/banned-items")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	List<UserBannedItemResponse> getUserBannedItems(@PathVariable("userId") Long userId);
}
