/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.users.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.user.shared.constants.UserConstants;
import com.styl.pacific.user.shared.enums.UserStatus;
import com.styl.pacific.user.shared.validations.AllowedUserStatus;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@JsonTypeName(UserConstants.UserTypeConstants.CUSTOMER)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AddCustomerUserRequest extends AddUserRequest {

	@Email
	@Length(max = 255)
	private final String email;

	@NotEmpty
	@Length(max = 120)
	private final String firstName;

	@Length(max = 120)
	private final String lastName;

	@Length(max = 100)
	private final String migrationId;

	@Length(max = 64)
	private final String familyCode;

	@Length(max = 32)
	private final String syncTargetUser;

	@AllowedUserStatus(allowed = { UserStatus.ARCHIVED, UserStatus.BLOCKED })
	private final UserStatus userStatus;

	@Builder
	@JsonCreator
	public AddCustomerUserRequest(Long userGroupId, Long userRoleId, String phoneNumber, String avatarPath,
			String avatarHash, String externalId, String firstName, String lastName, Long tenantId, String email,
			String migrationId, String familyCode, String syncTargetUser, UserStatus userStatus) {
		super(UserType.CUSTOMER, userGroupId, userRoleId, phoneNumber, avatarPath, avatarHash, externalId, tenantId);
		this.email = email;
		this.firstName = firstName;
		this.lastName = lastName;
		this.migrationId = migrationId;
		this.syncTargetUser = syncTargetUser;
		this.userStatus = userStatus;
		this.familyCode = familyCode;
	}
}
