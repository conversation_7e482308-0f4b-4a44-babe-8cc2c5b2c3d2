/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.clients.keycloak;

import com.styl.pacific.user.service.data.access.clients.keycloak.configs.KeycloakServiceAccountConfigProperties;
import com.styl.pacific.user.service.data.access.clients.keycloak.response.KeycloakAccessTokenResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "keycloak-service", contextId = "keycloak-service-account-client", url = "${pacific.clients.keycloak-service.url}")
public interface KeycloakServiceAccountClient {

	@PostMapping(value = "/realms/master/protocol/openid-connect/token", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
	KeycloakAccessTokenResponse getServiceAccountAccessToken(MultiValueMap<String, Object> params);

	default KeycloakAccessTokenResponse getServiceAccountAccessToken(
			KeycloakServiceAccountConfigProperties serviceAccountConfigProperties) {
		MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
		params.add("client_id", serviceAccountConfigProperties.getClientId());
		params.add("client_secret", serviceAccountConfigProperties.getClientSecret());
		params.add("grant_type", "client_credentials");
		return getServiceAccountAccessToken(params);
	}

	@DeleteMapping(value = "/admin/realms/{realmId}/users/{userId}")
	void deleteUser(@RequestHeader("Authorization") String bearerToken, @PathVariable("realmId") String realmId,
			@PathVariable("userId") String userId);

}