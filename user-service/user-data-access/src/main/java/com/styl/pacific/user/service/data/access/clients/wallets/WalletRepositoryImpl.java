/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.clients.wallets;

import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.wallet.WalletRepository;
import com.styl.pacific.user.service.core.features.wallet.entities.Wallet;
import com.styl.pacific.user.service.data.access.clients.wallets.mapper.WalletMapper;
import com.styl.pacific.wallet.service.enums.WalletType;
import com.styl.pacific.wallet.service.requests.wallet.FindWalletsRequest;
import com.styl.pacific.wallet.service.requests.wallet.WalletsFilterRequest;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class WalletRepositoryImpl implements WalletRepository {
	private final WalletClient walletClient;

	@Override
	public Optional<Wallet> getDepositWalletByUserId(UserId id) {
		final var result = walletClient.findWallets(FindWalletsRequest.builder()
				.filter(WalletsFilterRequest.builder()
						.customerId(id.getValue())
						.types(List.of(WalletType.DEPOSIT))
						.build())
				.page(0)
				.size(1)
				.build());
		return result.getTotalElements() == 0
				? Optional.empty()
				: Optional.of(WalletMapper.INSTANCE.toModel(result.getContent()
						.getFirst()));
	}

	@Override
	public void deleteWallet(Wallet wallet) {
		log.info("Deleting wallet with Customer: {} WalletID: {}", wallet.getCustomerId(), wallet.getWalletId());
		walletClient.deleteWallet(Long.parseLong(wallet.getWalletId()));
	}
}
