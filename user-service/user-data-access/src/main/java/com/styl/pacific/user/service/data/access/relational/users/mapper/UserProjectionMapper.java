/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.relational.users.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.user.service.data.access.relational.groups.mapper.UserGroupEntityMapper;
import com.styl.pacific.user.service.data.access.relational.permissions.mapper.UserPermissionEntityMapper;
import com.styl.pacific.user.service.data.access.relational.users.entities.UserEntity;
import com.styl.pacific.user.service.data.access.relational.users.projections.UserProjection;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, UserPermissionEntityMapper.class, UserGroupEntityMapper.class })
public interface UserProjectionMapper {
	UserProjectionMapper INSTANCE = Mappers.getMapper(UserProjectionMapper.class);

	@Mapping(target = "totalSubAccounts", ignore = true)
	@Mapping(target = "totalSponsors", ignore = true)
	@Mapping(target = "permissions", ignore = true)
	@Mapping(target = "userGroup", source = "userGroupEntity")
	@Mapping(target = "userGroup.path", source = "userGroupEntity.userGroupPathEntity.path")
	@Mapping(target = "userGroup.parent", source = "userGroupEntity.parentEntity")
	@Mapping(target = "nonCompletedActions", source = "userNonCompletedActions")
	UserProjection toProjectionByEntity(UserEntity user);
}
