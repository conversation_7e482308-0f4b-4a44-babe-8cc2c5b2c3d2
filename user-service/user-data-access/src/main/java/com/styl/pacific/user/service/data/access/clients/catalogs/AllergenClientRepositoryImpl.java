/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.clients.catalogs;

import com.styl.pacific.common.feign.exception.FeignBadRequestException;
import com.styl.pacific.domain.exception.GlobalErrorCode;
import com.styl.pacific.domain.valueobject.AllergenId;
import com.styl.pacific.domain.valueobject.BaseId;
import com.styl.pacific.user.service.core.features.allergens.AllergenRepository;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AllergenClientRepositoryImpl implements AllergenRepository {

	private final AllergenClient allergenClient;

	@Override
	public boolean existsById(AllergenId allergenId) {
		try {
			return Optional.ofNullable(allergenId)
					.map(BaseId::getValue)
					.map(String::valueOf)
					.map(allergenClient::findById)
					.isPresent();
		} catch (FeignBadRequestException e) {
			if (Optional.ofNullable(e.getError())
					.isPresent() && Objects.equals(GlobalErrorCode.ALLERGEN_NOT_FOUND.getValue(), e.getError()
							.getCode())) {
				return false;
			}
			throw e;
		}
	}
}
