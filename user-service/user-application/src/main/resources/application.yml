---
server:
  port: 9202
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/plain
    min-response-size: 10240
logging:
  level:
    com.styl.pacific: DEBUG
    org.hibernate.SQL: INFO
    org.hibernate.orm.jdbc.bind: INFO
    org.hibernate.stat: INFO
    org.hibernate.SQL_SLOW: INFO

management:
  tracing:
    sampling:
      probability: 1.0
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  endpoint:
    health:
      probes:
        enabled: true
      metrics:
        enabled: true
  endpoints:
    web:
      exposure:
        include: "*"

spring:
  application:
    name: user-service
    version: '@project.version@'
  jpa:
    open-in-view: false
    show-sql: false
    database-platform: com.styl.pacific.data.access.jpa.dialect.ExtendedPostgresDialect
    properties:
      hibernate:
        dialect: com.styl.pacific.data.access.jpa.dialect.ExtendedPostgresDialect
        jdbc:
          time_zone: UTC
          batch_size: 50
        format_sql: false
        generate_statistics: false
        order_inserts: true
        order_updates: true
        query:
          enable_lazy_load_no_trans: true
          fail_on_pagination_over_collection_fetch: true
          in_clause_parameter_padding: true
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
  datasource:
    url: *******************************************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
  flyway:
    enabled: true
    locations: classpath:db/migration
  cloud:
    openfeign:
      client:
        config:
          default:
            connectTimeout: 5000
            readTimeout: 5000
            loggerLevel: FULL
            errorDecoder: com.styl.pacific.common.feign.exception.PacificFeignErrorDecoder
            requestInterceptors:
              - com.styl.pacific.common.feign.interceptor.PlatformFeignHeaderForwarderInterceptor
  sql:
    init:
      platform: postgres
pacific:
  platform:
    brand-name: TeraBite
  tracing:
    otlp:
      endpoint: http://jeager.svc.monitoring.svc.cluster.local:4317
  aws:
    s3:
      endpoint:
      region: ap-southeast-1
      accessKey:
      secretKey:
  default-system-tenants:
    timeFormat: HH:mm:ss
    dateFormat: dd/MM/yyyy
    timeZone: Singapore
    currency: SGD
  user-preferences:
    default:
      enabledBackOfficeUserEmailOtp: true
      notifications:
        - key: NOTIFICATION_PAYMENT_TRANSACTIONS_SUCCEEDED
          channels:
            - EMAIL
        - key: NOTIFICATION_WALLET_BALANCE_CHANGED
          channels:
            - EMAIL
        - key: NOTIFICATION_ORDER_STATUS_UPDATED
          channels:
            - EMAIL
        - key: NOTIFICATION_PRE_ORDER_CONFIRMED
          channels:
            - EMAIL

  kafka:
    consumers:
      user-service:
        keycloak-user-event:
          enabled: true
          group-id: user-service-keycloak-user-event
          topic-name: keycloak-user-event
        tenant-created-event:
          enabled: true
          group-id: user-service-tenant-created-event
          topic-name: tenant-service-tenant-created-event
    publishers:
      notification-service:
        notification-created-event:
          topic-name: notification-service-notification-command
      user-service:
        user-created-event:
          topic-name: user-service-user-created-event
  clients:
    tenant-service:
      url: http://tenant-svc.application.svc.cluster.local:9201
    catalog-service:
      url: http://catalog-svc.application.svc.cluster.local:9203
    authz-service:
      url: http://auth-svc.application.svc.cluster.local:9209
    keycloak-service:
      url: http://keycloak.localhost:8280
      service-account:
        client-id: localhost-service-account-test
        client-secret: localhost-service-account-test-secret
    wallet-service:
      url: http://wallet-svc.application.svc.cluster.local:9210

  users:
    deletion:
      scheduling:
        enabled: true
        cron-expression: "0 */20 * * * *" ## Every 20 mins for check
        deleting-user-after: P3D
        lock-at-least-for: PT15S
        lock-at-most-for: PT10M
        max-total-users: 200

    default-system-user:
      id: 1
      email: <EMAIL>
      sso-id: ********-1111-1111-1111-************
    invitations:
      register-expiry:
        default-expiry: PT48H
        available-to-resend: PT1M
  keycloak:
    keycloakUrl: http://localhost:8280
    customer-register-link-pattern: |
      ${KEYCLOAK_URL}/realms/${REALM_ID}/protocol/openid-connect/registrations
      ?client_id=customer-portal
      &response_type=code
      &scope=openid
    backoffice-register-link-pattern: |
      ${KEYCLOAK_URL}/realms/${REALM_ID}/protocol/openid-connect/registrations
      ?client_id=bo-portal
      &response_type=code
      &scope=openid
    default-back-office-redirect-url: https://www.styl.com.sg/
    customer-redirect-url-pattern: "http://${TENANT_DOMAIN}"

kafka-config:
  bootstrap-servers: "localhost:19092, localhost:29092, localhost:39092"
  schema-registry-url-key: schema.registry.url
  schema-registry-url: http://localhost:8081
  num-of-partitions: 3
  replication-factor: 3
kafka-producer-config:
  key-serializer-class: org.apache.kafka.common.serialization.UUIDSerializer
  value-serializer-class: io.confluent.kafka.serializers.KafkaAvroSerializer
  compression-type: none
  acks: all
  batch-size: 16384
  batch-size-boost-factor: 100
  linger-ms: 5
  request-timeout-ms: 60000
  retry-count: 5
kafka-consumer-config:
  key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
  value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
  auto-offset-reset: earliest
  specific-avro-reader-key: specific.avro.reader
  specific-avro-reader: true
  batch-listener: false
  auto-startup: true
  concurrency-level: 1
  session-timeout-ms: 10000
  heartbeat-interval-ms: 3000
  max-poll-interval-ms: 300000
  max-poll-records: 500
  max-partition-fetch-bytes-default: 1048576
  max-partition-fetch-bytes-boost-factor: 1
  poll-timeout-ms: 150
