/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.alternativeaccess.listener;

import com.styl.pacific.user.service.core.features.alternativeaccess.AlternativeUserAccessCommandService;
import com.styl.pacific.user.service.core.features.users.events.UserDeletedEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
@Slf4j
public class AlternativeAccessListener {

	private final AlternativeUserAccessCommandService commandService;

	@EventListener
	@Transactional(rollbackFor = Exception.class)
	public void handleUserDeletedEvent(UserDeletedEvent event) {
		if (event.getUserIds() == null || event.getUserIds()
				.isEmpty() || event.getUser() == null) {
			return;
		}
		log.info("Handle Delete User Alternative Access for User: {}", event.getUserIds());
		if (event.getUser()
				.getIsAlternativeUser()) {
			commandService.deleteAlternativeAccessByAlternativeUserId(event.getUser()
					.getId());
			return;
		}

		commandService.deleteAlternativeAccessByUserId(event.getUser()
				.getId());
	}
}
