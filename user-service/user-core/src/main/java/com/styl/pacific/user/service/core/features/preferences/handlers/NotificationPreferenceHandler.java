/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.preferences.handlers;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.preferences.UserPreferenceRepository;
import com.styl.pacific.user.service.core.features.preferences.defaults.DefaultUserPreferenceConfig;
import com.styl.pacific.user.service.core.features.preferences.entities.NotificationPreferenceData;
import com.styl.pacific.user.service.core.features.preferences.entities.UserPreference;
import com.styl.pacific.user.service.core.features.users.UserQueryService;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.shared.enums.UserPreferenceKey;
import com.styl.pacific.user.shared.exceptions.UserPreferenceNotFoundException;
import java.util.Set;
import org.springframework.stereotype.Component;

@Component
public class NotificationPreferenceHandler extends BaseUserPreferenceHandler {

	private static final Set<UserPreferenceKey> SUPPORTED_KEYS = Set.of(
			UserPreferenceKey.NOTIFICATION_PAYMENT_TRANSACTIONS_SUCCEEDED,
			UserPreferenceKey.NOTIFICATION_WALLET_BALANCE_CHANGED, UserPreferenceKey.NOTIFICATION_ORDER_STATUS_UPDATED,
			UserPreferenceKey.NOTIFICATION_PRE_ORDER_CONFIRMED);
	private final DefaultUserPreferenceConfig configs;

	public NotificationPreferenceHandler(DefaultUserPreferenceConfig configs,
			UserPreferenceRepository userPreferenceRepository, UserQueryService userQueryService) {
		super(userPreferenceRepository, userQueryService);
		this.configs = configs;
	}

	@Override
	public boolean isSupported(UserPreferenceKey key) {
		return SUPPORTED_KEYS.contains(key);

	}

	@Override
	public UserPreference getDefaultPreference(TenantId tenantId, UserId userId, UserPreferenceKey key) {
		return configs.getNotifications()
				.stream()
				.filter(it -> key.equals(it.getKey()))
				.findFirst()
				.map(it -> UserPreference.builder()
						.user(User.builder()
								.id(userId)
								.build())
						.key(key)
						.group(key.getGroup())
						.tenantId(tenantId)
						.isDefault(true)
						.data(NotificationPreferenceData.builder()
								.key(key)
								.channels(it.getChannels())
								.build())
						.build())
				.orElseThrow(UserPreferenceNotFoundException::new);

	}

}
