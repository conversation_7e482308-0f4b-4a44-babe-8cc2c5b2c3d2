/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.requiredactions.entities;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.styl.pacific.user.shared.constants.UserRequiredActionConstants;
import com.styl.pacific.user.shared.enums.UserRequiredActionType;
import lombok.Getter;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = UserRequiredActionMetadata.TYPE_FIELD_NAME, visible = true)
@JsonSubTypes({
		@JsonSubTypes.Type(value = UserRequiredActionAddedNewTenantMetadata.class, name = UserRequiredActionConstants.UserRequiredActionTypeConstants.ADDED_NEW_TENANT),
		@JsonSubTypes.Type(value = UserRequiredActionSubAccountInvitedMetadata.class, name = UserRequiredActionConstants.UserRequiredActionTypeConstants.INVITED_USER_SUB_ACCOUNT), })
@Getter
public abstract class UserRequiredActionMetadata {
	public static final String TYPE_FIELD_NAME = "type";

	private final UserRequiredActionType type;

	protected UserRequiredActionMetadata(UserRequiredActionType type) {
		this.type = type;
	}
}
