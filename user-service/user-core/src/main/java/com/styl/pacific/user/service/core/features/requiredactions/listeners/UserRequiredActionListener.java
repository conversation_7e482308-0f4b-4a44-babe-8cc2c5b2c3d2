/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.requiredactions.listeners;

import com.styl.pacific.user.service.core.features.permissions.entities.UserPermission;
import com.styl.pacific.user.service.core.features.requiredactions.UserRequiredActionCommandService;
import com.styl.pacific.user.service.core.features.requiredactions.entities.UserRequiredActionAddedNewTenantMetadata;
import com.styl.pacific.user.service.core.features.requiredactions.entities.UserRequiredActionSubAccountInvitedMetadata;
import com.styl.pacific.user.service.core.features.requiredactions.request.CreateUserRequiredActionCommand;
import com.styl.pacific.user.service.core.features.subaccounts.events.SubAccountDeletedEvent;
import com.styl.pacific.user.service.core.features.subaccounts.events.SubAccountExistingUserCreatedEvent;
import com.styl.pacific.user.service.core.features.users.events.BackOfficeUserUpdatedEvent;
import com.styl.pacific.user.service.core.features.users.events.UserDeletedEvent;
import com.styl.pacific.user.shared.enums.UserRequiredActionType;
import jakarta.validation.Valid;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
@Slf4j
public class UserRequiredActionListener {

	private final UserRequiredActionCommandService commandService;

	@EventListener
	@Transactional(rollbackFor = Exception.class)
	public void handleUserChangedEvent(BackOfficeUserUpdatedEvent event) {
		if (log.isDebugEnabled()) {
			log.debug("Event: UserId: {}, {} NewPermissions: {}", event.getUser()
					.getId()
					.getValue(), event.getNewPermissions()
							.size(), event.getNewPermissions()
									.stream()
									.map(UserPermission::toString)
									.collect(Collectors.joining(",")));
			log.debug("Event: UserId: {}, {} RemovedPermissions: {}", event.getUser()
					.getId()
					.getValue(), event.getRemovedPermissions()
							.size(), event.getNewPermissions()
									.stream()
									.map(UserPermission::toString)
									.collect(Collectors.joining(",")));
		}

		commandService.deleteUserRequiredActionByUserAndActionTypeAndTenantIds(event.getUser()
				.getId(), UserRequiredActionType.ADDED_NEW_TENANT, event.getRemovedPermissions()
						.stream()
						.map(UserPermission::getTenantId)
						.collect(Collectors.toSet()));

		event.getNewPermissions()
				.forEach(permission -> commandService.createRequiredAction(CreateUserRequiredActionCommand.builder()
						.userId(permission.getUserId())
						.tenantId(permission.getTenantId())
						.actionType(UserRequiredActionType.ADDED_NEW_TENANT)
						.metadata(UserRequiredActionAddedNewTenantMetadata.builder()
								.roleId(permission.getUserRoleId()
										.getValue())
								.tenantId(permission.getTenantId()
										.getValue())
								.build())
						.build()));

	}

	@EventListener
	public void handleUserSubAccountExistingUserCreatedEvent(@Valid SubAccountExistingUserCreatedEvent event) {
		final var subAccount = event.getUserSubAccount();
		commandService.createRequiredAction(CreateUserRequiredActionCommand.builder()
				.userId(subAccount.getSubUser()
						.getId())
				.tenantId(subAccount.getTenantId())
				.actionType(UserRequiredActionType.INVITED_USER_SUB_ACCOUNT)
				.metadata(UserRequiredActionSubAccountInvitedMetadata.builder()
						.sponsorUserId(subAccount.getSponsorUser()
								.getId()
								.getValue())
						.sponsorEmail(subAccount.getSponsorUser()
								.getEmail())
						.sponsorFullName(subAccount.getSponsorUser()
								.getFullName())
						.userSubAccountId(subAccount.getId()
								.getValue())
						.build())
				.build());
	}

	@EventListener
	public void handleSubAccountDeletedEvent(SubAccountDeletedEvent event) {
		commandService.deleteUserRequiredActionByMetadataSubAccountId(event.getSubAccountId());
	}

	@EventListener
	public void handleUserDeletedEvent(UserDeletedEvent event) {
		commandService.deleteUserRequiredActionByUserIds(event.getUserIds());
	}
}
