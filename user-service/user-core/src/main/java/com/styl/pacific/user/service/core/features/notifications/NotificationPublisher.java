/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.notifications;

import com.styl.pacific.user.service.core.features.notifications.entities.JoinedUserWelcomeNotification;
import com.styl.pacific.user.service.core.features.notifications.entities.NewUserWelcomeNotification;
import com.styl.pacific.user.service.core.features.notifications.entities.UserInvitationNotification;
import com.styl.pacific.user.service.core.features.subaccounts.entities.UserSubAccount;

public interface NotificationPublisher {
	void publish(UserInvitationNotification notification);

	void publish(NewUserWelcomeNotification notification);

	void publish(JoinedUserWelcomeNotification notification);

	void publish(UserSubAccount userSubAccount);

	void publish(UserSubAccount userSubAccount, String action);
}
