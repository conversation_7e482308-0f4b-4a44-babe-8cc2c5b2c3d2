/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.userallergens.service;

import com.styl.pacific.domain.valueobject.AllergenId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.user.service.core.features.allergens.AllergenRepository;
import com.styl.pacific.user.service.core.features.userallergens.UserAllergenCommandService;
import com.styl.pacific.user.service.core.features.userallergens.UserAllergenRepository;
import com.styl.pacific.user.service.core.features.userallergens.entities.UserAllergen;
import com.styl.pacific.user.service.core.features.userallergens.mapper.UserAllergenMapper;
import com.styl.pacific.user.service.core.features.userallergens.request.SaveUserAllergensCommand;
import com.styl.pacific.user.service.core.features.userallergens.request.UserAllergenItem;
import com.styl.pacific.user.service.core.features.users.UserRepository;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.service.core.features.users.request.FilterUserQuery;
import com.styl.pacific.user.service.core.features.users.valueobject.UserFetchRule;
import com.styl.pacific.user.shared.exceptions.AllergenNotFoundException;
import com.styl.pacific.user.shared.exceptions.UserAllergenException;
import com.styl.pacific.user.shared.exceptions.UserNotFoundException;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UserAllergenCommandServiceImpl implements UserAllergenCommandService {
	private final UserAllergenRepository userAllergenRepository;
	private final UserRepository userRepository;
	private final AllergenRepository allergenRepository;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<UserAllergen> saveUserAllergens(SaveUserAllergensCommand command) {

		final var user = userRepository.getUserByCondition(FilterUserQuery.builder()
				.byUserId(command.getUserId())
				.build(), UserFetchRule.builder()
						.build())
				.orElseThrow(() -> new UserNotFoundException("User not found"));

		checkDuplicateAllergen(command.getItems());

		Set<AllergenId> userAllergenIds = command.getItems()
				.stream()
				.map(UserAllergenItem::getAllergenId)
				.collect(Collectors.toSet());

		userAllergenRepository.deleteUserAllergensNotChose(command.getTenantId(), command.getUserId(), userAllergenIds);

		List<UserAllergen> userAllergens = buildUserAllergens(command.getTenantId(), user, command.getItems());

		return userAllergenRepository.save(userAllergens);
	}

	List<UserAllergen> buildUserAllergens(TenantId tenantId, User user, List<UserAllergenItem> items) {
		return items.stream()
				.map(item -> {
					if (Optional.ofNullable(item.getId())
							.isEmpty() && !allergenRepository.existsById(item.getAllergenId())) {
						throw new AllergenNotFoundException("Allergen not found with id: " + item.getAllergenId()
								.getValue());
					}
					Optional<UserAllergen> userAllergenExisting = userAllergenRepository
							.getUserAllergenByAllergenIdAndUserIdAndTenantId(item.getAllergenId(), user.getId(),
									tenantId);
					if (userAllergenExisting.isEmpty()) {
						return UserAllergenMapper.INSTANCE.toEntity(tenantId, user, item.getAllergenId(), item
								.isRestricted());
					}
					return UserAllergenMapper.INSTANCE.toEntity(tenantId, user, userAllergenExisting.get(), item
							.isRestricted());
				})
				.toList();
	}

	private void checkDuplicateAllergen(List<UserAllergenItem> items) {
		Set<Long> elements = new HashSet<>();
		items.forEach(item -> {
			if (elements.contains(item.getAllergenId()
					.getValue())) {
				throw new UserAllergenException("Duplicate allergen with allergenId: " + item.getAllergenId()
						.getValue());
			}
			elements.add(item.getAllergenId()
					.getValue());
		});
	}
}
