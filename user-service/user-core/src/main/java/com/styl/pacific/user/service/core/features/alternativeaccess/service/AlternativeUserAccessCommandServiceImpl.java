/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.alternativeaccess.service;

import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.alternativeaccess.AlternativeUserAccessCommandService;
import com.styl.pacific.user.service.core.features.alternativeaccess.AlternativeUserAccessRepository;
import com.styl.pacific.user.service.core.features.alternativeaccess.entities.AlternativeUserAccess;
import com.styl.pacific.user.service.core.features.alternativeaccess.mapper.AlternativeUserAccessMapper;
import com.styl.pacific.user.service.core.features.alternativeaccess.request.CreateAlternativeUserAccessCommand;
import com.styl.pacific.user.service.core.features.alternativeaccess.request.FilterAlternativeUserAccess;
import com.styl.pacific.user.service.core.features.notifications.request.ControlNotificationCommand;
import com.styl.pacific.user.service.core.features.subaccounts.UserSubAccountQueryService;
import com.styl.pacific.user.service.core.features.subaccounts.request.FilterSubAccountQuery;
import com.styl.pacific.user.service.core.features.users.UserCommandService;
import com.styl.pacific.user.service.core.features.users.UserQueryService;
import com.styl.pacific.user.service.core.features.users.request.DeleteUserCommand;
import com.styl.pacific.user.service.core.features.users.request.FilterUserQuery;
import com.styl.pacific.user.service.core.features.users.valueobject.UserFetchRule;
import com.styl.pacific.user.shared.exceptions.AlternativeUserAccessException;
import com.styl.pacific.user.shared.exceptions.AlternativeUserAccessNotFoundException;
import com.styl.pacific.user.shared.exceptions.UserNotFoundException;
import java.time.Instant;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class AlternativeUserAccessCommandServiceImpl implements AlternativeUserAccessCommandService {
	private final AlternativeUserAccessRepository repository;
	private final UserQueryService userQueryService;
	private final UserSubAccountQueryService userSubAccountQueryService;
	private final UserCommandService userCommandService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AlternativeUserAccess createAlternativeUserAccess(UserId mainUserId, TenantId tenantId,
			CreateAlternativeUserAccessCommand command, ControlNotificationCommand notificationControl) {
		final var mainUser = userQueryService.getUserByConditions(FilterUserQuery.builder()
				.byUserId(mainUserId)
				.byUserTypes(Set.of(command.getUserType()))
				.byTenantId(tenantId)
				.build(), UserFetchRule.builder()
						.build())
				.orElseThrow(() -> new UserNotFoundException("User not found"));
		if (!UserType.CUSTOMER.equals(mainUser.getUserType())) {
			throw new AlternativeUserAccessException("Other user type is not supported except CUSTOMER");
		}

		final var alternativeUser = Optional.ofNullable(command.getUserId())
				.map(id -> {
					final var existingUser = userQueryService.getSingleUserById(id)
							.orElseThrow(() -> new UserNotFoundException("User not found"));
					return userCommandService.updateSingleUser(AlternativeUserAccessMapper.INSTANCE
							.toUpdateCustomerCommand(mainUser.getFamilyCode(), existingUser), ControlNotificationCommand
									.builder()
									.isSkipNotification(notificationControl.isSkipNotification())
									.build());
				})
				.orElseGet(() -> userCommandService.addSingleUser(AlternativeUserAccessMapper.INSTANCE
						.toCreateCustomerCommand(tenantId, command, mainUser.getFamilyCode()),
						ControlNotificationCommand.builder()
								.isSkipNotification(notificationControl.isSkipNotification())
								.build()));

		if (userSubAccountQueryService.countSubAccounts(FilterSubAccountQuery.builder()
				.bySponsorId(alternativeUser.getId())
				.byTenantId(tenantId)
				.build()) != 0) {
			throw new AlternativeUserAccessException(
					"Cannot create alternative access user who is main user of sub account");
		}

		return repository.save(AlternativeUserAccess.builder()
				.tenantId(tenantId)
				.user(mainUser)
				.alternativeUser(alternativeUser)
				.build());
	}

	@Override
	@Transactional
	public void deleteCustomerAlternativeAccess(TenantId tenantId, UserId userId, UserId alternativeUserId) {
		final var alternativeUserAccess = repository.findAlternativeUserAccessByTenantIdAndUserIdAndAlternativeUserId(
				tenantId, userId, alternativeUserId)
				.orElseThrow(() -> new AlternativeUserAccessNotFoundException("Alternative user access not found"));

		repository.deleteAlternativeUserAccess(alternativeUserAccess);
		userCommandService.deleteUsersInSchedule(DeleteUserCommand.builder()
				.tenantId(tenantId)
				.byUserId(alternativeUserId)
				.schedulingDeletedAt(Instant.now())
				.build());
	}

	@Override
	public void deleteAlternativeAccessByAlternativeUserId(UserId alternativeUserId) {
		repository.deleteAlternativeUserAccess(FilterAlternativeUserAccess.builder()
				.byAlternativeUserId(alternativeUserId)
				.build());
	}

	@Override
	public void deleteAlternativeAccessByUserId(UserId userId) {
		repository.deleteAlternativeUserAccess(FilterAlternativeUserAccess.builder()
				.byUserId(userId)
				.build());
	}
}
