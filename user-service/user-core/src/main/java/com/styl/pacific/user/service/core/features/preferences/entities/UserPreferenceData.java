/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.preferences.entities;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.styl.pacific.user.shared.constants.UserPreferenceKeyConstants;
import com.styl.pacific.user.shared.enums.UserPreferenceKey;
import lombok.Getter;
import lombok.Setter;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = UserPreferenceData.TYPE_FIELD_NAME, visible = true)
@JsonSubTypes({
		@JsonSubTypes.Type(value = DateTimeFormatPreferenceData.class, name = UserPreferenceKeyConstants.ACCOUNT_DATE_TIME_FORMAT),
		@JsonSubTypes.Type(value = SecurityMfaPreferenceData.class, name = UserPreferenceKeyConstants.SECURITY_MFA),
		@JsonSubTypes.Type(value = NotificationPreferenceData.class, name = UserPreferenceKeyConstants.NOTIFICATION_PAYMENT_TRANSACTIONS_SUCCEEDED),
		@JsonSubTypes.Type(value = NotificationPreferenceData.class, name = UserPreferenceKeyConstants.NOTIFICATION_WALLET_BALANCE_CHANGED),
		@JsonSubTypes.Type(value = NotificationPreferenceData.class, name = UserPreferenceKeyConstants.NOTIFICATION_ORDER_STATUS_UPDATED),
		@JsonSubTypes.Type(value = NotificationPreferenceData.class, name = UserPreferenceKeyConstants.NOTIFICATION_PRE_ORDER_CONFIRMED) })
@Getter
@Setter
public class UserPreferenceData {
	public static final String TYPE_FIELD_NAME = "key";
	private final UserPreferenceKey key;

	protected UserPreferenceData(UserPreferenceKey key) {
		this.key = key;
	}
}
