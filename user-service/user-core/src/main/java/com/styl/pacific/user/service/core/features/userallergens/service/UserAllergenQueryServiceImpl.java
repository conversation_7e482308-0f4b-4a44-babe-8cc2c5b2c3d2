/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.userallergens.service;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.userallergens.UserAllergenQueryService;
import com.styl.pacific.user.service.core.features.userallergens.UserAllergenRepository;
import com.styl.pacific.user.service.core.features.userallergens.entities.UserAllergen;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UserAllergenQueryServiceImpl implements UserAllergenQueryService {
	private final UserAllergenRepository userAllergenRepository;

	@Override
	@Transactional(readOnly = true)
	public List<UserAllergen> getUserAllergenByUserIdAndTenantId(UserId userId, TenantId tenantId) {
		return userAllergenRepository.getListUserAllergenByUserIdAndTenantId(userId, tenantId);
	}
}
