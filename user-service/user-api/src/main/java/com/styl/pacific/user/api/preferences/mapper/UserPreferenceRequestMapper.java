/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.preferences.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.user.service.core.features.preferences.entities.DateTimeFormatPreferenceData;
import com.styl.pacific.user.service.core.features.preferences.entities.NotificationPreferenceData;
import com.styl.pacific.user.service.core.features.preferences.entities.SecurityMfaPreferenceData;
import com.styl.pacific.user.service.core.features.preferences.entities.UserPreferenceData;
import com.styl.pacific.user.service.core.features.preferences.request.CreateUserPreferenceCommand;
import com.styl.pacific.user.service.core.features.preferences.request.FilterUserPreferenceQuery;
import com.styl.pacific.user.service.core.features.preferences.request.UpdateUserPreferenceCommand;
import com.styl.pacific.user.shared.http.preferences.request.CreateAccountDateTimeFormatRequest;
import com.styl.pacific.user.shared.http.preferences.request.CreateNotificationPreferenceRequest;
import com.styl.pacific.user.shared.http.preferences.request.CreateSecurityMfaRequest;
import com.styl.pacific.user.shared.http.preferences.request.CreateUserPreferenceRequest;
import com.styl.pacific.user.shared.http.preferences.request.FilterUserPreferenceRequest;
import com.styl.pacific.user.shared.http.preferences.request.UpdateAccountDateTimeFormatRequest;
import com.styl.pacific.user.shared.http.preferences.request.UpdateNotificationPreferenceRequest;
import com.styl.pacific.user.shared.http.preferences.request.UpdateSecurityMfaRequest;
import com.styl.pacific.user.shared.http.preferences.request.UpdateUserPreferenceRequest;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface UserPreferenceRequestMapper {
	UserPreferenceRequestMapper INSTANCE = Mappers.getMapper(UserPreferenceRequestMapper.class);

	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "userId", source = "userId", qualifiedByName = "longToUserId")
	@Mapping(target = "key", source = "request.key")
	@Mapping(target = "group", source = "request.key.group")
	@Mapping(target = "data", ignore = true)
	@Mapping(target = "user", ignore = true)
	CreateUserPreferenceCommand toCreateUserPreferenceCommand(Long tenantId, Long userId,
			CreateUserPreferenceRequest request);

	@AfterMapping
	default void afterMappingCreateUserPreference(
			@MappingTarget CreateUserPreferenceCommand.CreateUserPreferenceCommandBuilder builder,
			CreateUserPreferenceRequest source) {
		final UserPreferenceData data = switch (source.getKey()) {
		case SECURITY_MFA -> {
			final var security = (CreateSecurityMfaRequest) source;
			yield SecurityMfaPreferenceData.builder()
					.isEnabledEmailOtp(security.getIsEnabledEmailOtp())
					.build();
		}
		case NOTIFICATION_PAYMENT_TRANSACTIONS_SUCCEEDED, NOTIFICATION_WALLET_BALANCE_CHANGED,
				NOTIFICATION_ORDER_STATUS_UPDATED, NOTIFICATION_PRE_ORDER_CONFIRMED -> NotificationPreferenceData
						.builder()
						.key(source.getKey())
						.channels(((CreateNotificationPreferenceRequest) source).getOptions())
						.build();
		case ACCOUNT_DATE_TIME_FORMAT -> {
			final var dateTime = (CreateAccountDateTimeFormatRequest) source;
			yield DateTimeFormatPreferenceData.builder()
					.timeFormat(dateTime.getTimeFormat())
					.dateFormat(dateTime.getDateFormat())
					.timeZone(MapstructCommonMapper.INSTANCE.stringToTimeZone(dateTime.getTimeZone()))
					.build();
		}
		};

		builder.data(data);
	}

	@Mapping(target = "id", source = "id", qualifiedByName = "longToUserPreferenceId")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "userId", source = "userId", qualifiedByName = "longToUserId")
	@Mapping(target = "key", source = "request.key")
	@Mapping(target = "group", source = "request.key.group")
	@Mapping(target = "data", ignore = true)
	@Mapping(target = "user", ignore = true)
	UpdateUserPreferenceCommand toUpdateUserPreferenceCommand(Long tenantId, Long userId, Long id,
			UpdateUserPreferenceRequest request);

	@AfterMapping
	default void afterMappingUpdateUserPreference(
			@MappingTarget UpdateUserPreferenceCommand.UpdateUserPreferenceCommandBuilder builder,
			UpdateUserPreferenceRequest source) {
		final UserPreferenceData data = switch (source.getKey()) {
		case SECURITY_MFA -> {
			final var security = (UpdateSecurityMfaRequest) source;
			yield SecurityMfaPreferenceData.builder()
					.isEnabledEmailOtp(security.getIsEnabledEmailOtp())
					.build();
		}
		case NOTIFICATION_PAYMENT_TRANSACTIONS_SUCCEEDED, NOTIFICATION_WALLET_BALANCE_CHANGED,
				NOTIFICATION_ORDER_STATUS_UPDATED, NOTIFICATION_PRE_ORDER_CONFIRMED -> NotificationPreferenceData
						.builder()
						.key(source.getKey())
						.channels(((UpdateNotificationPreferenceRequest) source).getOptions())
						.build();
		case ACCOUNT_DATE_TIME_FORMAT -> {
			final var dateTime = (UpdateAccountDateTimeFormatRequest) source;
			yield DateTimeFormatPreferenceData.builder()
					.timeFormat(dateTime.getTimeFormat())
					.dateFormat(dateTime.getDateFormat())
					.timeZone(MapstructCommonMapper.INSTANCE.stringToTimeZone(dateTime.getTimeZone()))
					.build();
		}
		};

		builder.data(data);
	}

	@Mapping(target = "byTenantId", ignore = true)
	@Mapping(target = "byUserId", ignore = true)
	FilterUserPreferenceQuery toFilter(FilterUserPreferenceRequest request);
}