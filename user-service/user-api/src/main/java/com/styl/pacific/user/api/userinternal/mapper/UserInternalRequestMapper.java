/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.userinternal.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.user.service.core.features.userinternal.request.ActivateUserInternalCommand;
import com.styl.pacific.user.shared.http.users.request.ActivateUserInternalRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface UserInternalRequestMapper {
	UserInternalRequestMapper INSTANCE = Mappers.getMapper(UserInternalRequestMapper.class);

	@Mapping(target = "email", source = "email", qualifiedByName = "stringToLowercase")
	@Mapping(target = "firstName", source = "firstName", qualifiedByName = "stringToClearance")
	@Mapping(target = "lastName", source = "lastName", qualifiedByName = "stringToClearance")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "isAllowedUserCreation", source = "allowedUserCreation")
	ActivateUserInternalCommand toActivateCommand(ActivateUserInternalRequest request);
}