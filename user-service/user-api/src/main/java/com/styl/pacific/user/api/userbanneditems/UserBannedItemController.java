/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.userbanneditems;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.api.userbanneditems.mapper.UserBannedItemControllerMapper;
import com.styl.pacific.user.service.core.features.subaccounts.UserSubAccountQueryService;
import com.styl.pacific.user.service.core.features.userbanneditems.UserBannedItemService;
import com.styl.pacific.user.service.core.features.userbanneditems.request.SaveUserBannedItemCommand;
import com.styl.pacific.user.shared.http.apis.UserBannedItemApi;
import com.styl.pacific.user.shared.http.userbanneditems.request.SaveUserBannedItemRequest;
import com.styl.pacific.user.shared.http.userbanneditems.response.UserBannedItemResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class UserBannedItemController implements UserBannedItemApi {
	private final UserBannedItemService userBannedItemService;
	private final RequestContext requestContext;
	private final UserSubAccountQueryService userSubAccountQueryService;

	@Override
	public List<UserBannedItemResponse> saveUserBannedItems(Long userId, SaveUserBannedItemRequest request) {
		userSubAccountQueryService.checkSubAccountResourceAccess(MapstructCommonDomainMapper.INSTANCE.longToTenantId(
				requestContext.getTenantId()), requestContext.getTokenClaim(), MapstructCommonDomainMapper.INSTANCE
						.longToUserId(userId));
		SaveUserBannedItemCommand command = UserBannedItemControllerMapper.INSTANCE.toSaveCommand(userId, requestContext
				.getTenantId(), request.getItems());
		return userBannedItemService.saveUserBannedItems(command)
				.stream()
				.map(UserBannedItemControllerMapper.INSTANCE::toResponse)
				.toList();
	}

	@Override
	public List<UserBannedItemResponse> getUserBannedItems(Long userId) {
		userSubAccountQueryService.checkSubAccountResourceAccess(MapstructCommonDomainMapper.INSTANCE.longToTenantId(
				requestContext.getTenantId()), requestContext.getTokenClaim(), MapstructCommonDomainMapper.INSTANCE
						.longToUserId(userId));
		return userBannedItemService.getUserBannedItemByUserIdAndTenantId(new UserId(userId), new TenantId(
				requestContext.getTenantId()))
				.stream()
				.map(UserBannedItemControllerMapper.INSTANCE::toResponse)
				.toList();
	}
}
