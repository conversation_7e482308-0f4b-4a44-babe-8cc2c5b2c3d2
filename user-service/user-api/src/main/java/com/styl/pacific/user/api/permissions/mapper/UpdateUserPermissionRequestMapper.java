/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.permissions.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.user.service.core.features.users.request.UpdateUserPermissionCommand;
import com.styl.pacific.user.shared.http.users.request.UpdateUserPermissionRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, })
public interface UpdateUserPermissionRequestMapper {

	@Mapping(source = "tenantId", target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(source = "userRoleId", target = "userRoleId", qualifiedByName = "longToUserRoleId")
	UpdateUserPermissionCommand toCommand(UpdateUserPermissionRequest source);
}
