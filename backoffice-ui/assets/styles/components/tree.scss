@use './typography';

.fuse-tree-invisible {
  @apply hidden;
}

.fuse-tree {
  ul,
  li {
    @apply mt-0 mb-0;
    list-style-type: none;
  }

  .mat-nested-tree-node {
    div[optgroup='group'] {
      @apply ml-[14.5px] border-l-[1px] border-solid border-[#e4e7ec];
    }
  }

  div[optgroup='group'] > .mat-tree-node {
    @apply pl-0;
  }

  .mat-tree-node {
    @apply flex gap-1 h-fit min-h-7 px-2;

    .btn-toggle {
      @apply flex justify-center items-center w-3.5 h-3.5 border-[1px] border-solid border-[#212b36] rounded-1;

      mat-icon {
        @apply w-3 h-3 text-black;
      }
    }
  }

  .tree-line {
    @apply relative w-2 h-6;

    div {
      @apply absolute top-1/2 left-0 w-full -translate-y-1/2 border-t-[1px] border-solid border-[#e4e7ec];
    }
  }

  .tree-label {
    @extend .web__body2;
    @apply flex-1 text-[#475467] whitespace-nowrap overflow-hidden text-ellipsis cursor-pointer;

    &__selected {
      @apply rounded-2 bg-[#c6daff] px-1;
    }
  }

  .tree-leaf-root {
    @apply w-3.5 h-3.5 rounded-1 border-[1px] border-solid border-grey-400;
  }

  .mat-mdc-checkbox {
    .mdc-form-field {
      @apply pr-0;
    }

    .mdc-checkbox {
      @apply p-0;

      .mdc-checkbox__background {
        @apply top-0 left-0;
      }
    }

    .mat-mdc-checkbox-touch-target {
      @apply hidden;
    }

    .mdc-checkbox__native-control {
      @apply w-[18px] h-[18px];
    }
  }
}
