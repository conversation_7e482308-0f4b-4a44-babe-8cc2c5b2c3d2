@use './typography';

.select-field {
  @apply w-full;

  .mat-mdc-text-field-wrapper {
    @apply h-[42px] rounded-[8px] #{!important};
  }

  .mat-mdc-form-field-flex {
    @apply px-3 #{!important};
  }

  .mat-mdc-form-field-infix {
    @apply min-h-[42px] #{!important};

    .mdc-floating-label {
      mat-label {
        @apply font-onest text-base font-normal leading-[22px] text-grey-800;
      }

      span {
        @apply font-onest text-xl font-semibold leading-6 text-error-main;
      }
    }
  }

  .mat-mdc-select {
    .mat-mdc-select-value {
      .mat-mdc-select-placeholder {
        @apply font-onest text-base font-normal leading-[22px] text-grey-500/48 #{!important};
      }

      .mat-mdc-select-value-text {
        @apply font-onest text-base font-normal leading-[22px] text-grey-700 #{!important};
      }
    }
  }

  &.mat-mdc-form-field.mat-form-field-appearance-fill.mat-form-field-invalid {
    .mat-mdc-text-field-wrapper {
      @apply border-error-main #{!important};
    }

    .mat-mdc-select {
      .mat-mdc-select-placeholder {
        @apply text-grey-500/48 #{!important};
      }
    }
  }

  .mat-mdc-select-arrow-wrapper {
    @apply hidden;
  }
}

.select-dropdown-panel,
.mat-mdc-autocomplete-panel {
  @apply pt-2 pb-3 px-2 rounded-[8px] #{!important};
  @apply border-solid border-[1px] border-[#dcdcdc] shadow-xl bg-[#fdfdfd] #{!important};

  &.mat-mdc-select-panel {
    @apply max-h-72 #{!important};
  }

  .mat-mdc-option {
    @apply w-full py-3 px-2.5 flex justify-start items-center gap-1.5 #{!important};

    .mdc-list-item__primary-text {
      @apply font-onest text-base font-normal leading-[22px] text-[#525252] #{!important};
    }
  }

  .mat-mdc-option.mat-mdc-option-active.mdc-list-item {
    @apply bg-transparent hover:bg-grey-500/16 #{!important};

    .mdc-list-item__primary-text {
      @apply text-grey-700 #{!important};
    }
  }

  .mdc-list-item--selected.mat-mdc-option-active.mdc-list-item {
    @apply bg-grey-500/16 hover:bg-grey-500/16 #{!important};

    .mdc-list-item__primary-text {
      @apply text-grey-700 #{!important};
    }
  }

  .mat-mdc-option-multiple.mdc-list-item--selected.mdc-list-item {
    @apply bg-transparent hover:bg-grey-500/16 #{!important};
  }

  .mat-mdc-option-pseudo-checkbox {
    @apply m-0 rounded-3 #{!important};
  }

  .mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked {
    &::after {
      @apply w-[5px] h-0.5 #{!important};
    }
  }

  .select-search {
    @apply w-full h-11 flex items-center gap-1.5 px-3 py-2.5 mb-1;
    @apply rounded-[8px] border-[1px] border-solid border-grey-500/48 bg-white shadow-xs;

    input {
      @apply w-full font-onest text-base leading-[22px] text-left text-grey-700 #{!important};

      &::placeholder {
        @apply font-onest text-base leading-[22px] text-grey-500/48 #{!important};
      }
    }
  }
}

.select-tenant {
  @apply w-full rounded-[12px] border-[1px] border-solid border-grey-500/12 bg-grey-500/12;

  .mat-mdc-select-arrow-wrapper {
    @apply hidden;
  }
}

.select-tenant-dropdown-panel {
  @apply border-[1px] border-solid border-primary bg-[#162133] #{!important};
  box-shadow: -5px -5px 250px 0px rgba(118, 165, 255, 0.02) inset;

  &.mat-mdc-select-panel {
    @apply max-h-80 p-3 rounded-[8px] #{!important};
  }

  .mat-mdc-option {
    @apply flex py-1.5 justify-start items-start #{!important};

    .mdc-list-item__primary-text {
      @extend .web__body2;
      @apply font-normal text-white #{!important};
    }
  }
}

.date-of-week {
  @apply flex items-center gap-2.5 flex-wrap sm:justify-normal;

  &__item {
    @extend .web__body2;
    @apply w-10 h-10 flex justify-center items-center text-grey-700 cursor-pointer;
    @apply rounded-full border-[1px] border-solid border-grey-500/48;
  }

  &__range {
    @extend .web__body2;
    @apply w-fit h-8 px-3 flex justify-center items-center text-grey-700 cursor-pointer;
    @apply rounded-2 border-[1px] border-solid border-grey-500/48;
  }

  &__active {
    @apply text-white bg-primary #{!important};
  }
}

.no-select-label {
  .mat-mdc-form-field.mat-form-field-appearance-fill
    .mat-mdc-text-field-wrapper:not(.mdc-text-field--no-label) {
    margin-top: 0px;
  }
}

.mat-mdc-select-value-text,
.mdc-list-item__primary-text {
  @apply font-onest #{!important};
}


.device-config-set {
  .mat-mdc-menu-content {
    @apply flex flex-col gap-1 p-3 rounded-md;

    .mat-mdc-menu-item {
      @apply p-2 rounded min-h-[38px];

      .mat-mdc-menu-item-text {
        @extend .web__body2;
        @apply font-normal text-grey-900;
      }
    }
  }
}