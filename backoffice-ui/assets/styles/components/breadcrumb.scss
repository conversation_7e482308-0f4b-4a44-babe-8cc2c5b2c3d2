@use './typography';

.parent-page {
  @apply flex flex-col flex-auto min-w-0;

  &__header {
    @apply px-4 py-3 rounded-tr-4 rounded-tl-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3;
    @apply text-3xl text-[#212B36] font-bold leading-10 bg-white;

    &__actions {
      @apply flex justify-end flex-wrap sm:gap-4 gap-2 w-full sm:w-fit sm:h-10;
    }
  }

  &__body {
    @apply flex-auto p-4 h-[calc(100vh_-_176px)];

    &__table {
      @apply w-full h-full overflow-auto bg-white rounded-4;
    }
  }
}

.page-header {
  @apply flex justify-between w-full px-5 py-4 bg-white flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-1;

  &__wrapper {
    @apply flex flex-col gap-1;
  }

  &__breadcrumb {
    @apply flex items-center gap-4;

    &__parent {
      @extend .web__body2;
    }

    &__dot {
      @apply w-1 h-1 rounded-full bg-grey-500;
    }

    &__child {
      @extend .web__body2;
      @apply leading-5 text-grey-500;
    }
  }

  &__title {
    @apply flex gap-2.5;

    button {
      @apply flex items-center justify-center h-fit px-3 py-2 rounded-3 shadow-3xl;

      mat-icon {
        @apply w-4 h-4;
      }
    }

    div {
      @extend .web__h4;
      @apply leading-8;
    }
  }

  &__action {
    @apply flex justify-end flex-wrap w-full sm:w-auto gap-2 sm:gap-4;
  }
}
