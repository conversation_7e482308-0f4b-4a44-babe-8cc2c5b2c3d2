.dialog {
  @apply relative flex flex-col w-full h-full gap-6;

  &__header {
    // @apply p-4 flex items-center justify-between;

    @apply flex flex-col sm:flex-row gap-2.5 justify-between px-[20px];

    &__wrapper {
      @apply flex flex-col gap-1;
    }

    &__title {
      @apply font-onest text-2xl font-bold leading-7 text-grey-900;
    }

    &__description {
      @apply font-onest text-base font-normal leading-[22px] text-black;
    }
  }

  &__body {
    @apply flex flex-col w-full gap-4 px-[20px] overflow-x-scroll h-3/4 pb-4;
  }

  &__action {
    @apply sticky sm:absolute flex items-center justify-end space-x-3 mt-auto bg-white w-full bottom-0 mr-4;
  }
}

.fuse-modal-dialog-panel {
  @screen md {
    @apply w-[928px] h-[568px];
  }

  .mat-mdc-dialog-container {
    .mat-mdc-dialog-surface {
      padding: 16px !important;
    }
  }
}

.mat-mdc-dialog-container {
  max-height: 100vh !important;
}

.cdk-overlay-pane.mat-mdc-dialog-panel {
  max-width: 100% !important;
}

@media (max-width: 599px) {
  .cdk-overlay-pane.mat-mdc-dialog-panel {
    max-width: 100% !important;
  }
}
