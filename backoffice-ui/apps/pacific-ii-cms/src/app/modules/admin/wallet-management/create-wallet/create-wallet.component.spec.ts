import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CreateWalletComponent } from './create-wallet.component';
import { provideIcons } from 'apps/pacific-ii-cms/src/app/core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { MatDialogRef } from '@angular/material/dialog';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';
import { CustomerManagementService } from '../../customer-management/customer-management.service';
import { of, throwError } from 'rxjs';
import { SourceOfFundService } from '../../source-of-fund/source-of-fund.service';
import { Utils } from '../../../../core/utils/utils';
import { WalletManagementService } from '../wallet-management.service';

describe('CreateWalletComponent', () => {
  let component: CreateWalletComponent;
  let fixture: ComponentFixture<CreateWalletComponent>;

  let toast: ToastrService;
  let dialogRef: MatDialogRef<any>;
  let walletService: WalletManagementService;
  let sourceOfFundService: SourceOfFundService;
  let customerService: CustomerManagementService;

  const mockCustomerListResponse = {
    content: [
      {
        id: '123',
        firstName: 'John',
        lastName: 'Doe',
        fullName: 'John Doe',
        email: '',
      },
      {
        id: '456',
        firstName: 'Jane',
        lastName: 'Smith',
        fullName: 'Jane Smith',
        email: '',
      },
    ],
  };
  const mockFundSourceListResponse = {
    content: [
      {
        fundSourceId: 'fs001',
        name: 'Fund1',
      },
      {
        fundSourceId: 'fs002',
        name: 'Fund2',
      },
    ],
  };
  const mockWalletData = {
    customerId: '123',
    type: 'FUNDED',
    description: 'desc',
    fundSourceId: 'fs001',
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CreateWalletComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: MatDialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CreateWalletComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    toast = TestBed.inject(ToastrService);
    dialogRef = TestBed.inject(MatDialogRef);
    walletService = TestBed.inject(WalletManagementService);
    sourceOfFundService = TestBed.inject(SourceOfFundService);
    customerService = TestBed.inject(CustomerManagementService);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle hooks', () => {
    it('should initialize the form correctly on constructor', () => {
      expect(component.walletForm).toBeDefined();
      expect(component.walletForm.get('customerId')).toBeDefined();
      expect(component.walletForm.get('customerId')?.value).toEqual('');
      expect(component.walletForm.get('type')).toBeDefined();
      expect(component.walletForm.get('type')?.value).toEqual('');
      expect(component.walletForm.get('description')).toBeDefined();
      expect(component.walletForm.get('description')?.value).toEqual('');
      expect(component.walletForm.get('fundSourceId')).toBeDefined();
      expect(component.walletForm.get('fundSourceId')?.value).toEqual('');

      component.walletForm.patchValue({ type: 'FUNDED' });
      expect(component.walletForm.get('fundSourceId')?.enabled).toBeTruthy();

      component.walletForm.patchValue({ type: 'DEPOSIT' });
      expect(component.walletForm.get('fundSourceId')?.enabled).toBeFalsy();
    });

    it('should call handleGetCustomer and handleGetListSourceOfFund on init', () => {
      const handleGetCustomerSpy = jest.spyOn(component, 'handleGetCustomer');
      const handleGetListSourceOfFundSpy = jest.spyOn(
        component,
        'handleGetListSourceOfFund',
      );
      component.ngOnInit();

      expect(handleGetCustomerSpy).toHaveBeenCalled();
      expect(handleGetListSourceOfFundSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from all subscriptions on destroy', () => {
      fixture.detectChanges();
      const nextSpy = jest.spyOn(component['_unsubscribeAll'], 'next');
      const completeSpy = jest.spyOn(component['_unsubscribeAll'], 'complete');

      component.ngOnDestroy();

      expect(nextSpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    let handleGetCustomerSpy: any;
    let getCustomersSpy: any;
    let handleGetListSourceOfFundSpy: any;
    let getSourceOfFundListSpy: any;

    const mockCustomerList = [
      {
        label: 'Alice Smith',
        value: '001',
      },
    ];
    const mockFundSourceList = [
      {
        fundSourceId: 'fs000',
        name: 'Fund0',
      },
    ];

    beforeEach(() => {
      handleGetCustomerSpy = jest.spyOn(component, 'handleGetCustomer');
      getCustomersSpy = jest.spyOn(customerService, 'getCustomers');
      handleGetListSourceOfFundSpy = jest.spyOn(
        component,
        'handleGetListSourceOfFund',
      );
      getSourceOfFundListSpy = jest.spyOn(
        sourceOfFundService,
        'getSourceOfFundList',
      );

      component.optionListCustomer = mockCustomerList;
      component.optionListSOF = mockFundSourceList;
    });

    it('should update customerAppend/customerSearchConfig and fetch customer list when handleLazyLoadSelectCustomer called', () => {
      const event = {
        page: 1,
        search: 'test',
      };
      component.handleLazyLoadSelectCustomer(event);

      expect(component.customerAppend).toBeTruthy();
      expect(component.customerSearchConfig).toEqual({
        sortDirection: 'asc',
        sortFields: 'firstName',
        page: 1,
        size: 10,
        filter: {
          byName: 'test',
        },
      });
      expect(handleGetCustomerSpy).toHaveBeenCalled();
    });

    it('should update optionListCustomer after fetching customer list successfully with customerAppend is true', () => {
      getCustomersSpy.mockReturnValue(of(mockCustomerListResponse));
      component.customerAppend = true;
      component.handleGetCustomer();

      expect(getCustomersSpy).toHaveBeenCalledWith(
        'sortDirection=asc&sortFields=firstName&page=0&size=10',
      );
      expect(component.optionListCustomer).toEqual([
        ...mockCustomerList,
        {
          label: `${mockCustomerListResponse.content[0].firstName} ${mockCustomerListResponse.content[0].lastName}`,
          value: mockCustomerListResponse.content[0].id,
        },
        {
          label: `${mockCustomerListResponse.content[1].firstName} ${mockCustomerListResponse.content[1].lastName}`,
          value: mockCustomerListResponse.content[1].id,
        },
      ]);
    });

    it('should update optionListCustomer after fetching customer list successfully with customerAppend is false', () => {
      getCustomersSpy.mockReturnValue(of(mockCustomerListResponse));
      component.customerAppend = false;
      component.handleGetCustomer();

      expect(getCustomersSpy).toHaveBeenCalledWith(
        'sortDirection=asc&sortFields=firstName&page=0&size=10',
      );
      expect(component.optionListCustomer).toEqual([
        {
          label: `${mockCustomerListResponse.content[0].firstName} ${mockCustomerListResponse.content[0].lastName}`,
          value: mockCustomerListResponse.content[0].id,
        },
        {
          label: `${mockCustomerListResponse.content[1].firstName} ${mockCustomerListResponse.content[1].lastName}`,
          value: mockCustomerListResponse.content[1].id,
        },
      ]);
    });

    it('should not update optionListCustomer after fetching customer list unsuccessfully', () => {
      getCustomersSpy.mockReturnValue(throwError(() => new Error(undefined)));
      component.handleGetCustomer();

      expect(getCustomersSpy).toHaveBeenCalledWith(
        'sortDirection=asc&sortFields=firstName&page=0&size=10',
      );
      expect(component.optionListCustomer).toEqual(mockCustomerList);
    });

    it('should update sofAppend/sofSearchConfig and fetch source of fund list when handleLazyLoadSelectSOF called', () => {
      const event = {
        page: 1,
        search: 'test',
      };
      component.handleLazyLoadSelectSOF(event);

      expect(component.sofAppend).toBeTruthy();
      expect(component.sofSearchConfig).toEqual({
        sortDirection: 'asc',
        sortFields: 'name',
        page: 1,
        size: 10,
        filter: {
          name: 'test',
        },
      });
      expect(handleGetListSourceOfFundSpy).toHaveBeenCalled();
    });

    it('should update optionListSOF after fetching source of fund list successfully with sofAppend is true', () => {
      getSourceOfFundListSpy.mockReturnValue(of(mockFundSourceListResponse));
      component.sofAppend = true;
      component.handleGetListSourceOfFund();

      expect(getSourceOfFundListSpy).toHaveBeenCalledWith(
        'sortDirection=asc&sortFields=name&page=0&size=10',
      );

      expect(component.optionListSOF).toEqual([
        ...mockFundSourceList,
        ...mockFundSourceListResponse.content,
      ]);
    });

    it('should update optionListSOF after fetching source of fund list successfully with sofAppend is false', () => {
      getSourceOfFundListSpy.mockReturnValue(of(mockFundSourceListResponse));
      component.sofAppend = false;
      component.handleGetListSourceOfFund();

      expect(getSourceOfFundListSpy).toHaveBeenCalledWith(
        'sortDirection=asc&sortFields=name&page=0&size=10',
      );
      expect(component.optionListSOF).toEqual([
        ...mockFundSourceListResponse.content,
      ]);
    });

    it('should not update optionListSOF after fetching source of fund list unsuccessfully', () => {
      getSourceOfFundListSpy.mockReturnValue(
        throwError(() => new Error(undefined)),
      );
      component.handleGetListSourceOfFund();

      expect(getSourceOfFundListSpy).toHaveBeenCalledWith(
        'sortDirection=asc&sortFields=name&page=0&size=10',
      );
      expect(component.optionListSOF).toEqual(mockFundSourceList);
    });
  });

  describe('Form Validation', () => {
    let scrollToInvalidSpy: any;
    let handleCreateWalletSpy: any;

    beforeEach(() => {
      scrollToInvalidSpy = jest.spyOn(Utils, 'scrollToInvalid');
      handleCreateWalletSpy = jest.spyOn(component, 'handleCreateWallet');
      component.loading = false;
    });

    it('should be invalid when form is empty', () => {
      component.walletForm.reset({
        customerId: '',
        type: '',
        description: '',
        fundSourceId: '',
      });
      component.submitForm();

      expect(component.walletForm.valid).toBeFalsy();
      expect(scrollToInvalidSpy).toHaveBeenCalled();
      expect(handleCreateWalletSpy).not.toHaveBeenCalled();
      expect(component.loading).toBeFalsy();
    });

    it('should be valid when all fields are inputted', () => {
      component.walletForm.setValue(mockWalletData);
      component.submitForm();

      expect(component.walletForm.valid).toBeTruthy();
      expect(scrollToInvalidSpy).not.toHaveBeenCalled();
      expect(handleCreateWalletSpy).toHaveBeenCalledWith(mockWalletData);
      expect(component.loading).toBeTruthy();
    });

    it('should be set fundSourceId to null if empty string', () => {
      component.walletForm.setValue({
        customerId: '123',
        type: 'DEPOSIT',
        description: 'desc',
        fundSourceId: '',
      });
      component.submitForm();

      expect(component.walletForm.valid).toBeTruthy();
      expect(scrollToInvalidSpy).not.toHaveBeenCalled();
      expect(handleCreateWalletSpy).toHaveBeenCalledWith({
        customerId: '123',
        type: 'DEPOSIT',
        description: 'desc',
        fundSourceId: null,
      });
      expect(component.loading).toBeTruthy();
    });

    it('should mark fields as touched and update value and validity on submit', () => {
      for (const key in component.walletForm.controls) {
        jest.spyOn(component.walletForm.controls[key], 'markAsTouched');
        jest.spyOn(
          component.walletForm.controls[key],
          'updateValueAndValidity',
        );
      }
      component.walletForm.reset({
        customerId: '',
        type: '',
        description: '',
        fundSourceId: '',
      });
      component.submitForm();

      for (const key in component.walletForm.controls) {
        expect(
          component.walletForm.controls[key].markAsTouched,
        ).toHaveBeenCalled();
        expect(
          component.walletForm.controls[key].updateValueAndValidity,
        ).toHaveBeenCalled();
      }
    });
  });

  describe('Form Submission', () => {
    let handleCreateWalletSpy: any;
    let createWalletSpy: any;
    let toastSuccessSpy: any;
    let closeDialogSpy: any;

    beforeEach(() => {
      handleCreateWalletSpy = jest.spyOn(component, 'handleCreateWallet');
      createWalletSpy = jest.spyOn(walletService, 'createWallet');
      toastSuccessSpy = jest.spyOn(toast, 'success');
      closeDialogSpy = jest.spyOn(dialogRef, 'close');
      component.loading = false;
    });

    it('should not call handleCreateWallet if form is invalid', () => {
      component.walletForm.reset({
        customerId: '',
        type: '',
        description: '',
        fundSourceId: '',
      });
      component.submitForm();

      expect(handleCreateWalletSpy).not.toHaveBeenCalled();
      expect(component.loading).toBeFalsy();
    });

    it('should call handleCreateWallet if form is valid', () => {
      component.walletForm.setValue(mockWalletData);
      component.submitForm();

      expect(handleCreateWalletSpy).toHaveBeenCalled();
      expect(component.loading).toBeTruthy();
    });

    it('should close dialog and show toast message after adding wallet successfully', () => {
      createWalletSpy.mockReturnValue(of(null));
      component.handleCreateWallet(mockWalletData);

      expect(createWalletSpy).toHaveBeenCalledWith(mockWalletData);
      expect(component.loading).toBeFalsy();
      expect(toastSuccessSpy).toHaveBeenCalledWith(
        'wallet.message.create-success',
      );
      expect(closeDialogSpy).toHaveBeenCalledWith('save');
    });

    it('should not close dialog and show toast message after adding wallet unsuccessfully', () => {
      createWalletSpy.mockReturnValue(throwError(() => new Error(undefined)));
      component.handleCreateWallet(mockWalletData);

      expect(createWalletSpy).toHaveBeenCalledWith(mockWalletData);
      expect(component.loading).toBeFalsy();
      expect(toastSuccessSpy).not.toHaveBeenCalled();
      expect(closeDialogSpy).not.toHaveBeenCalled();
    });
  });

  describe('Dialog Closing', () => {
    let dialogRefCloseSpy: any;

    beforeEach(() => {
      dialogRefCloseSpy = jest.spyOn(dialogRef, 'close');
    });

    it('should close dialog with undefined', () => {
      component.onClose();
      expect(dialogRefCloseSpy).toHaveBeenCalledWith(undefined);
    });

    it('should close dialog with save value', () => {
      component.onClose('save');
      expect(dialogRefCloseSpy).toHaveBeenCalledWith('save');
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
