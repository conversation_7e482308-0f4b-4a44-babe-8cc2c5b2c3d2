import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SessionRevokeComponent } from './session-revoke.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';
import { ToastrModule } from 'ngx-toastr';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { IMenuBoard, IMenuBoardSessionDetail } from '../menu-board.types';
import {
  API,
  MENU_BOARD_DISPLAY_MODE,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { FuseUltils } from '@fuse/ultils';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { of } from 'rxjs';
import { provideIcons } from 'apps/pacific-ii-cms/src/app/core/icons/icons.provider';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('SessionRevokeComponent', () => {
  let component: SessionRevokeComponent;
  let fixture: ComponentFixture<SessionRevokeComponent>;
  let httpTestingController: HttpTestingController;

  const mockMenuBoard: IMenuBoard = {
    id: 'test1',
    name: 'Menu Board 1',
    displayMode: MENU_BOARD_DISPLAY_MODE.SINGLE,
    status: 'CREATED',
    createdAt: 1729340726348,
    activatedAt: 1729340726348,
    updatedAt: 1729340726348,
    onboard: 'test1',
    tenantId: '1234',
    images: [],
    delayBetweenImages: 60,
  };

  const mockSessionList = [
    {
      id: 'test1',
      tenantId: 'string',
      name: 'Test 1',
      menuBoardId: mockMenuBoard.id,
      createdAt: 0,
      updatedAt: 0,
    },
    {
      id: 'test2',
      tenantId: 'string',
      name: 'Test 2',
      menuBoardId: mockMenuBoard.id,
      createdAt: 0,
      updatedAt: 0,
    },
    {
      id: 'test3',
      tenantId: 'string',
      name: 'Test 3',
      menuBoardId: mockMenuBoard.id,
      createdAt: 0,
      updatedAt: 0,
    },
  ];

  const mockSessionListResponse = {
    content: mockSessionList,
    page: 0,
    sort: ['id: ASC'],
    totalElements: 3,
    totalPages: 1,
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SessionRevokeComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideAnimationsAsync(),
        {
          provide: MatDialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        {
          provide: MAT_DIALOG_DATA,
          useValue: {
            menuBoard: mockMenuBoard,
          },
        },
        {
          provide: FuseConfirmationService,
          useValue: {
            open: jest.fn().mockReturnValue({
              afterClosed: () => of('confirmed'),
            }),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(SessionRevokeComponent);
    component = fixture.componentInstance;

    httpTestingController = TestBed.inject(HttpTestingController);

    jest.spyOn(component['_toast'], 'success');

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should get the session list of menu board when run the ngOnInit', () => {
    component.ngOnInit();

    const params = {
      filter: {
        menuBoardIds: [mockMenuBoard.id],
      },
      page: 0,
      size: 500,
    };
    const req = httpTestingController.match(
      `${API.MENU_BOARD.SESSIONS}?${FuseUltils.objectToQueryString(params)}`,
    );
    req[1].flush(mockSessionListResponse);

    expect(component.sessionList).toEqual(mockSessionList);
  });

  it('should add the session to the selectedSession when run onChange with checked is true', () => {
    component.onChange({ checked: true }, mockSessionList[0]);

    expect(component.selectedSessions).toEqual([mockSessionList[0]]);
  });

  it('should remove the session out of the selectedSession when run onChange with checked is false', () => {
    component.selectedSessions = [...mockSessionList];

    component.onChange({ checked: false }, mockSessionList[0]);

    expect(component.selectedSessions).toEqual([
      mockSessionList[1],
      mockSessionList[2],
    ]);
  });

  it('should close the dialog when run the onClose method', () => {
    component.onClose();

    expect(component['_dialogRef'].close).toHaveBeenCalled();
  });

  it('should open the delete dialog and call the revoke selected session if user confirm when run the openConfirmationDialog with multiple sessions selected', () => {
    jest.spyOn(component, 'revoke');

    component.selectedSessions = [...mockSessionList];
    component.openConfirmationDialog();

    expect(component['_confirmationService'].open).toHaveBeenCalledWith({
      title: 'Revoke Menu Board Sessions',
      message: `Please confirm to revoke the selected menu board sessions.`,
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Revoke',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    expect(component.revoke).toHaveBeenCalledWith();
  });

  it('should open the delete dialog and call the revoke selected session if user confirm when run the openConfirmationDialog with a session selected', () => {
    jest.spyOn(component, 'revoke');

    component.selectedSessions = [mockSessionList[0]];
    component.openConfirmationDialog();

    expect(component['_confirmationService'].open).toHaveBeenCalledWith({
      title: 'Revoke Menu Board Session',
      message: `Please confirm to revoke the selected menu board session.`,
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Revoke',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    expect(component.revoke).toHaveBeenCalledWith();
  });

  it('should revoke the multiple sessions when run the revoke method', () => {
    jest.spyOn(component, 'onClose');

    component.selectedSessions = [...mockSessionList];
    component.revoke();

    for (const session of component.selectedSessions) {
      const req = httpTestingController.expectOne(
        API.MENU_BOARD.REVOKE.replace('{sessionId}', session.id),
      );
      req.flush(null);
    }

    expect(component['_toast'].success).toHaveBeenCalledWith(
      'You have revoked the selected sessions successfully!',
    );
    expect(component.onClose).toHaveBeenCalledWith(mockMenuBoard.id);
  });

  it('should revoke a session when run the revoke method', () => {
    jest.spyOn(component, 'onClose');

    component.selectedSessions = [mockSessionList[0]];
    component.revoke();

    for (const session of component.selectedSessions) {
      const req = httpTestingController.expectOne(
        API.MENU_BOARD.REVOKE.replace('{sessionId}', session.id),
      );
      req.flush(null);
    }

    expect(component['_toast'].success).toHaveBeenCalledWith(
      'You have revoked the selected session successfully!',
    );
    expect(component.onClose).toHaveBeenCalledWith(mockMenuBoard.id);
  });

  describe('Session Selection Functions', () => {
    const mockSessions = [
      { id: '1', name: 'Session 1' },
      { id: '2', name: 'Session 2' },
      { id: '3', name: 'Session 3' },
    ] as IMenuBoardSessionDetail[];

    beforeEach(() => {
      component.sessionList = [...mockSessions];
      component.selectedSessions = [];
    });

    describe('toggleAllSessions', () => {
      it('should select all sessions when none are selected', () => {
        expect(component.selectedSessions.length).toBe(0);

        component.toggleAllSessions();

        expect(component.selectedSessions.length).toBe(mockSessions.length);
        expect(component.selectedSessions).toEqual(mockSessions);
      });

      it('should deselect all sessions when all are selected', () => {
        component.selectedSessions = [...mockSessions];
        expect(component.selectedSessions.length).toBe(mockSessions.length);

        component.toggleAllSessions();

        expect(component.selectedSessions.length).toBe(0);
      });

      it('should create a deep copy of sessions when selecting all', () => {
        component.toggleAllSessions();

        component.sessionList[0].name = 'Modified Session';

        expect(component.selectedSessions[0].name).toBe('Session 1');
      });
    });

    describe('isAllSessionsSelected', () => {
      it('should return true when all sessions are selected', () => {
        component.selectedSessions = [...mockSessions];
        expect(component.isAllSessionsSelected()).toBe(true);
      });

      it('should return false when no sessions are selected', () => {
        expect(component.isAllSessionsSelected()).toBe(false);
      });

      it('should return false when only some sessions are selected', () => {
        component.selectedSessions = [mockSessions[0]];
        expect(component.isAllSessionsSelected()).toBe(false);
      });

      it('should handle empty sessionList correctly', () => {
        component.sessionList = [];
        component.selectedSessions = [];
        expect(component.isAllSessionsSelected()).toBe(true);
      });
    });

    describe('isSessionSelected', () => {
      it('should return true for a selected session', () => {
        component.selectedSessions = [mockSessions[0]];
        expect(component.isSessionSelected(mockSessions[0])).toBe(true);
      });

      it('should return false for an unselected session', () => {
        component.selectedSessions = [mockSessions[0]];
        expect(component.isSessionSelected(mockSessions[1])).toBe(false);
      });

      it('should handle session with same id but different properties', () => {
        component.selectedSessions = [mockSessions[0]];
        const modifiedSession = { ...mockSessions[0], name: 'Modified Name' };
        expect(component.isSessionSelected(modifiedSession)).toBe(true);
      });
    });
  });
});
