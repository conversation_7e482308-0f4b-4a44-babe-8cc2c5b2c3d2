<div class="w-full box !gap-4">
  <div
    class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between"
  >
    <div class="box__header__title">Stripe Connected Account</div>

    @if (paymentMethodDetail && permissionList.update) {
      <div class="flex justify-end w-full sm:w-auto gap-2.5">
        <button
          type="button"
          class="btn-contained__primary__medium"
          [disabled]="loading"
          (click)="onOpenLinkExistingAccountDialog()"
        >
          {{
            paymentMethodDetail && paymentMethodDetail.connectedAccount
              ? 'Change Connect Account'
              : 'Use Existing Connected Account'
          }}
        </button>

        @if (!paymentMethodDetail.connectedAccount) {
          <button
            type="button"
            class="btn-contained__primary__medium"
            [disabled]="loading"
            (click)="handleConnectStripeAccount()"
          >
            @if (loading) {
              <mat-spinner diameter="18" class="mr-0"></mat-spinner>
            }
            Connect
          </button>
        }
      </div>
    }
  </div>

  <div>
    <div
      class="flex flex-col items-start gap-4 sm:flex-row sm:justify-between card"
    >
      <div class="flex-1">
        <div class="card__label">Account</div>

        <div class="flex items-center gap-2">
          <div>
            {{
              paymentMethodDetail && paymentMethodDetail.connectedAccount
                ? getPaymentAccountName(paymentMethodDetail.connectedAccount)
                : 'N/A'
            }}
          </div>

          @if (paymentMethodDetail && paymentMethodDetail.connectedAccount) {
            <div
              class="px-2 py-1 rounded-3 w-fit"
              [ngClass]="
                paymentMethodDetail.connectedAccount.isActive
                  ? 'bg-[#36B37E] text-white'
                  : 'bg-[#DFE3E8] text-black'
              "
            >
              {{
                paymentMethodDetail.connectedAccount.isActive
                  ? 'ACTIVE'
                  : 'PENDING'
              }}
            </div>
          }
        </div>
      </div>

      @if (paymentMethodDetail && permissionList.update) {
        <div class="flex justify-end w-full gap-2 sm:w-auto">
          @if (
            paymentMethodDetail &&
            paymentMethodDetail.connectedAccount &&
            !paymentMethodDetail.connectedAccount.isActive
          ) {
            <button
              type="button"
              class="btn-contained__primary__medium"
              [disabled]="loading"
              (click)="
                handleConnectStripeAccount(
                  paymentMethodDetail.connectedAccount.id
                )
              "
            >
              @if (loading) {
                <mat-spinner diameter="18" class="mr-0"></mat-spinner>
              }
              Continue
            </button>
          }

          @if (paymentMethodDetail && paymentMethodDetail.connectedAccount) {
            <button
              type="button"
              class="btn-outlined__error__medium"
              [disabled]="loading"
              (click)="onOpenRemoveStripeAccountDialog()"
            >
              @if (loading) {
                <mat-spinner diameter="18" class="mr-0"></mat-spinner>
              }
              Remove
            </button>
          }
        </div>
      }
    </div>
  </div>
</div>
