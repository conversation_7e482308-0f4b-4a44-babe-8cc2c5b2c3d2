import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { API } from '../../../core/const';
import { TermsConditions, TermsConditionsInfo } from './terms-conditions.types';

@Injectable({ providedIn: 'root' })
export class TermsConditionsService {
  private _httpClient = inject(HttpClient);

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Get terms and conditions
   */
  getTermsConditions(): Observable<TermsConditionsInfo> {
    return this._httpClient.get<TermsConditionsInfo>(
      API.TERMS_CONDITIONS.DETAIL,
    );
  }

  /**
   * Update terms and conditions
   */
  updateTermsConditions(
    data: TermsConditions,
  ): Observable<TermsConditionsInfo> {
    return this._httpClient.post<TermsConditionsInfo>(
      API.TERMS_CONDITIONS.UPDATE,
      data,
    );
  }
}
