import { Component, Inject } from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FuseInputComponent } from '@fuse/components/input';
import { FuseUltils } from '@fuse/ultils';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { ToastrService } from 'ngx-toastr';
import { StoreManagementService } from '../../store-management/store-management.service';
import { DeviceManagementService } from '../device-management.service';

@Component({
  selector: 'app-make-broken-device',
  standalone: true,
  imports: [ReactiveFormsModule, MatProgressSpinnerModule, FuseInputComponent],
  templateUrl: './device-make-as-broken.component.html',
})
export class DeviceMakeBrokenComponent {
  dataForm!: UntypedFormGroup;
  loading = false;

  optionListTenant: Array<any> = [];
  optionListStore: Array<any> = [];

  errorMessages = {
    note: {
      required: 'Please input note!',
    },
  };

  constructor(
    private _toast: ToastrService,
    private _dialogRef: MatDialogRef<any>,
    private _formBuilder: UntypedFormBuilder,
    private _deviceService: DeviceManagementService,
    private _storeService: StoreManagementService,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.initForm();
  }

  handleGetStoreDetail(tenantId: any): void {
    const params = {};
    this._storeService
      .getStores(FuseUltils.objectToQueryString(params), tenantId)
      .subscribe((stores: any) => {
        this.optionListStore = stores.content;
      });
  }

  submitForm(): void {
    for (const i in this.dataForm.controls) {
      this.dataForm.controls[i].markAsTouched();
      this.dataForm.controls[i].updateValueAndValidity();
    }

    if (this.dataForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    this.loading = true;
    const deviceData = {
      ...this.dataForm.value,
    };
    this.handleMakeBrokenDevice(deviceData);
  }

  handleMakeBrokenDevice(deviceData: any): void {
    this._deviceService.makeAsBroken(this.data.deviceId, deviceData).subscribe({
      next: () => {
        this.loading = false;
        this._toast.success('Mark as broken success.');
        this.onClose('save');
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  onClose(message?: string): void {
    this._dialogRef.close(message);
  }

  private initForm(): void {
    this.dataForm = this._formBuilder.group({
      note: [null, [Validators.required]],
    });
  }
}
