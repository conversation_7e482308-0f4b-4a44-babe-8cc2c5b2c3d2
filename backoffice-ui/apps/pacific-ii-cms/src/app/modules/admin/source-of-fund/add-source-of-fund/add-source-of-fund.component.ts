import { NgClass } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  ReactiveFormsModule,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { Mat<PERSON>abe<PERSON> } from '@angular/material/form-field';
import { MatSpinner } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { FuseDateTimePickerComponent } from '@fuse/components/date-time-picker';
import { FuseInputComponent } from '@fuse/components/input';
import { FuseSelectComponent } from '@fuse/components/select';
import { FuseTextareaComponent } from '@fuse/components/textarea';
import { FuseUltils } from '@fuse/ultils';
import {
  FUND_SOURCE_EXPIRED_IN_OPTIONS,
  FUND_SOURCE_SCHEDULE_TYPE,
  FUND_SOURCE_SCHEDULE_TYPE_OPTIONS,
  FUND_SOURCE_STATUS,
  FUND_SOURCE_STATUS_OPTIONS,
  REGEX,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { StorageService } from 'apps/pacific-ii-cms/src/app/core/services/storage.service';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { DateTime } from 'luxon';
import { ToastrService } from 'ngx-toastr';
import { switchMap } from 'rxjs';
import { Currency } from '../../tenant-management/tenant.types';
import { SourceOfFundService } from '../source-of-fund.service';
import { ISourceOfFundAdded } from '../source-of-fund.types';

@Component({
  selector: 'app-add-source-of-fund',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FuseInputComponent,
    FuseTextareaComponent,
    FuseSelectComponent,
    FuseDateTimePickerComponent,
    MatSpinner,
    NgClass,
    MatLabel,
    MatRadioModule,
  ],
  templateUrl: './add-source-of-fund.component.html',
})
export class AddSourceOfFundComponent implements OnInit {
  sourceForm!: UntypedFormGroup;
  expiredForm!: UntypedFormGroup;
  tenantCurrency!: Currency;
  errorMessages = {
    name: {
      required: "Please enter fund's name!",
    },
    status: {
      required: "Please choose fund's status!",
    },
    topupFrequency: {
      required: "Please choose fund's schedule type!",
    },
    description: {
      required: "Please enter fund's description!",
      maxlength: 'Description cannot exceed 255 characters!',
    },
    topupDate: {
      required: "Please choose fund's top-up time!",
    },
    topupAmount: {
      required: "Please enter fund's top-up amount!",
      pattern:
        'Invalid top-up amount value. Please ensure the top-up amount is a numeric value and has no more than {n} digits in decimal!',
      min: 'Top-up amount must be greater than or equal to 0!',
    },
    fundExpiresOn: {
      required: 'Please choose the expired time!',
    },
    fundExpiresIn: {
      required: 'Please enter the amount of time top-up would be expired!',
      pattern: 'The amount of time must be a non-negative integer!',
      min: 'The amount of time must be a non-negative integer!',
    },
  };

  maxTimeStamp: number | null = null;
  minTimeStampExpire = DateTime.now().startOf('day').toMillis();
  minTimeStampEnd = DateTime.now().toMillis();

  statusOptions = [...FUND_SOURCE_STATUS_OPTIONS];
  fundSourceScheduleOptions = [...FUND_SOURCE_SCHEDULE_TYPE_OPTIONS];
  expiredInOptions = [...FUND_SOURCE_EXPIRED_IN_OPTIONS];

  expiredMode = null;

  loading = false;

  constructor(
    private _formBuilder: FormBuilder,
    private _dialogRef: MatDialogRef<any>,
    private _sourceOfFundService: SourceOfFundService,
    private _toast: ToastrService,
    private _storageService: StorageService,
  ) {
    this.tenantCurrency = this._storageService.getTenantCurrency();
    this.updateErrorMessages();
    this.initSourceForm();
  }

  ngOnInit(): void {
    this.sourceForm.controls['expiredMode'].valueChanges.subscribe((value) => {
      if (value !== 'time') {
        this.maxTimeStamp = this.sourceForm.value['endTopupDate'];
      } else {
        if (this.expiredForm.value.fundExpiresOn) {
          const fundExpiresOn = DateTime.fromMillis(
            this.expiredForm.value.fundExpiresOn,
          )
            .endOf('day')
            .toMillis();

          this.maxTimeStamp =
            (fundExpiresOn || Infinity) >
            (this.sourceForm.value.endTopupDate || Infinity)
              ? this.sourceForm.value.endTopupDate
              : fundExpiresOn;
        }
      }
    });

    this.expiredForm.controls['fundExpiresOn'].valueChanges.subscribe(
      (value) => {
        if (value) {
          const fundExpiresOn = DateTime.fromMillis(value)
            .endOf('day')
            .toMillis();
          this.maxTimeStamp =
            (fundExpiresOn || Infinity) >
            (this.sourceForm.value.endTopupDate || Infinity)
              ? this.sourceForm.value.endTopupDate
              : fundExpiresOn;
        } else {
          this.maxTimeStamp = this.sourceForm.value.endTopupDate;
        }
      },
    );

    this.sourceForm.controls['endTopupDate'].valueChanges.subscribe((value) => {
      if (
        this.sourceForm.value.expiredMode !== 'time' ||
        !this.expiredForm.value.fundExpiresOn
      ) {
        this.maxTimeStamp = value;
        return;
      }

      if (this.expiredForm.value.fundExpiresOn) {
        const fundExpiresOn = DateTime.fromMillis(
          this.expiredForm.value.fundExpiresOn,
        )
          .endOf('day')
          .toMillis();

        this.maxTimeStamp =
          (value || Infinity) > (fundExpiresOn || Infinity)
            ? fundExpiresOn
            : value;
      }
    });

    this.sourceForm.controls['topupDate'].valueChanges.subscribe((value) => {
      this.minTimeStampExpire = DateTime.fromMillis(
        DateTime.fromMillis(value).get('day') > DateTime.now().get('day')
          ? value
          : new Date().getTime(),
      )
        .startOf('day')
        .toMillis();

      this.minTimeStampEnd =
        value > new Date().getTime() ? value : new Date().getTime();
    });
  }

  onClose(message?: string) {
    this._dialogRef.close(message);
  }

  // Refactor to use shared utility methods
  submitForm() {
    for (const key in this.sourceForm.controls) {
      this.sourceForm.controls[key].markAsTouched();
      this.sourceForm.controls[key].markAsDirty();
      this.sourceForm.controls[key].updateValueAndValidity();
    }

    for (const key in this.expiredForm.controls) {
      this.expiredForm.controls[key].markAsTouched();
      this.expiredForm.controls[key].markAsDirty();
      this.expiredForm.controls[key].updateValueAndValidity();
    }

    if (this.sourceForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    if (
      this.sourceForm.value.expiredMode === 'amount' &&
      this.expiredForm.controls['fundExpiresIn'].invalid
    ) {
      Utils.scrollToInvalid();
      return;
    }

    if (
      this.sourceForm.value.expiredMode === 'time' &&
      this.expiredForm.controls['fundExpiresOn'].invalid
    ) {
      Utils.scrollToInvalid();
      return;
    }

    this.loading = true;
    const data = this._sourceOfFundService.formatFundData(
      this.sourceForm.getRawValue(),
      this.expiredForm.getRawValue(),
      this.tenantCurrency,
    );

    this.handleCreateSourceOfFund(data);
  }

  handleCreateSourceOfFund(data: Partial<ISourceOfFundAdded>) {
    this._sourceOfFundService
      .createSourceOfFund(data)
      .pipe(
        switchMap((returnData: any) => {
          const topUpData = {
            topupAmount: data.topupAmount,
            currency: data.currency,
            fundSourceId: returnData.fundSourceId,
            startTopupDate: data.topupDate,
            endTopupDate: data.endTopupDate,
            topupFrequency: data.topupFrequency,
          };
          return this.handleAddTopUpScheduler(topUpData);
        }),
      )
      .subscribe({
        next: () => {
          this.loading = false;
          this._toast.success(
            'You have created the source of fund successfully!',
          );
          this.onClose('Success');
        },
        error: () => {
          this.loading = false;
        },
      });
  }

  handleAddTopUpScheduler(data: any) {
    return this._sourceOfFundService.addTopUpScheduler(data);
  }

  updateErrorMessages(): void {
    this.errorMessages = {
      ...this.errorMessages,
      topupAmount: {
        ...this.errorMessages.topupAmount,
        pattern: this.tenantCurrency.fractionDigits
          ? this.errorMessages.topupAmount.pattern.replace(
              '{n}',
              `${this.tenantCurrency.fractionDigits}`,
            )
          : this.errorMessages.topupAmount.pattern
              .replace('numerical', 'integer')
              .replace(' and has no more than {n} digits in decimal', ''),
      },
    };
  }

  private initSourceForm() {
    this.sourceForm = this._formBuilder.group({
      name: [null, [Validators.required]],
      status: [FUND_SOURCE_STATUS.ACTIVE, [Validators.required]],
      topupFrequency: [FUND_SOURCE_SCHEDULE_TYPE.NONE, [Validators.required]],
      description: [null, [Validators.maxLength(255)]],
      topupDate: [null, [Validators.required]],
      endTopupDate: [null, [Validators.required]],
      topupAmount: [
        null,
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern(
            this.tenantCurrency.fractionDigits
              ? REGEX.PRICE.replace(
                  'n',
                  `${this.tenantCurrency.fractionDigits}`,
                )
              : REGEX.INTEGER,
          ),
        ],
      ],
      expiredMode: ['none'],
    });

    this.expiredForm = this._formBuilder.group({
      fundExpiresOn: [null, [Validators.required]],
      fundExpiresIn: [
        null,
        [
          Validators.pattern(REGEX.NONNEGATIVE_INTEGER),
          Validators.required,
          Validators.min(0),
        ],
      ],
    });

    this.sourceForm.controls['expiredMode'].valueChanges.subscribe((value) => {
      this.expiredMode = value;
    });
  }
}
