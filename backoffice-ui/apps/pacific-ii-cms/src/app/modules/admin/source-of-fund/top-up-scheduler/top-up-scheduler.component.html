<div class="box">
  <div class="box__header">
    <div class="box__header__title rounded-tr-4 rounded-tl-4">
      Top-up Scheduler Configuration
    </div>

    <div class="flex gap-2 justify-end w-full sm:w-auto">
      @if (permissionList.update) {
        @if (selectedData.length) {
          <button
            type="button"
            class="btn-outlined__error__medium"
            (click)="openMultiDeleteModal()"
          >
            <mat-icon [svgIcon]="'heroicons_outline:trash'"></mat-icon>
            Delete
          </button>
        }
        <button
          type="button"
          class="btn-outlined__primary__medium"
          [disabled]="dataSource.length === 10"
          [matTooltip]="'Maximum 10 schedulers'"
          [matTooltipPosition]="'above'"
          (click)="openAddSchedulerModal()"
        >
          <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
          Add Scheduler
        </button>
      }
    </div>
  </div>

  <div class="flex flex-col w-full gap-4">
    <fuse-table-component
      class="max-h-120"
      [dataSource]="dataSource"
      [displayedColumns]="displayedColumns"
      [actions]="actions"
      [hideColBtn]="true"
      [pagination]="false"
      [selectElement]="true"
      (selectedElementChanged)="onSelectData($event)"
    ></fuse-table-component>
  </div>
</div>
