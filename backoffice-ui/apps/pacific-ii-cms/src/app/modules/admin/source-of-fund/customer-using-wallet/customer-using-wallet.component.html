<div class="box">
  <div class="box__header__title rounded-tr-4 rounded-tl-4">
    Customer Using Wallet
  </div>

  <div class="flex-auto">
    <!-- CONTENT GOES HERE -->
    <div class="overflow-auto bg-white rounded-4">
      <fuse-advanced-search
        [searchFormData]="searchForm"
        (searchSelect)="handleLazyLoad($event)"
      >
        <ng-container>
          @if (permissionList.delete && selectedData.length) {
            <button
              type="button"
              class="btn-icon"
              [matTooltip]="'Bulk Actions'"
              [matMenuTriggerFor]="bulkAction"
              aria-label="Bulk Actions"
            >
              <mat-icon>more_vert</mat-icon>
            </button>

            <mat-menu #bulkAction="matMenu">
              <button
                type="button"
                mat-menu-item
                (click)="openRemoveMultiBeneficiariesDialog()"
              >
                Delete
              </button>
            </mat-menu>
          }
        </ng-container>
      </fuse-advanced-search>

      <div
        class="flex justify-end flex-col sm:flex-row w-full gap-2 py-4 pl-4 sm:h-18"
      >
        @if (permissionList.create) {
          <button
            type="button"
            class="btn-contained__primary__medium rounded-3"
            (click)="openAddGroupDialog()"
          >
            <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
            <span>Add Customer Group</span>
          </button>

          <button
            type="button"
            class="btn-contained__primary__medium rounded-3"
            (click)="openAddCustomerDialog()"
          >
            <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
            <span>Add Customer Name</span>
          </button>
        }
      </div>

      <fuse-table-component
        class="max-h-120"
        [dataSource]="dataSource"
        [displayedColumns]="displayedColumns"
        [actions]="actions"
        [total]="total"
        [sortDefault]="sortDefault"
        [hideColBtn]="true"
        [selectElement]="true"
        [listSelectedElement]="selectedData"
        (searchSelectChanged)="handleLazyLoad($event)"
        (selectedElementChanged)="onSelectData($event)"
      ></fuse-table-component>
    </div>
  </div>
</div>
