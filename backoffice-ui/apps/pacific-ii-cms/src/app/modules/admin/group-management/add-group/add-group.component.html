<div class="flex flex-col gap-6">
  <div class="flex flex-col gap-8 px-3 pt-2 pb-4">
    <div class="web__h6 text-grey-900 flex items-center gap-2">
      {{
        data?.groupName
          ? 'Edit Group'
          : data.parentId
            ? 'Add Group'
            : 'Add Main Group'
      }}
      <fuse-help-link
        [url]="
          data.parentId
            ? '/customer-management/group-manage/#add-a-sub-group'
            : '/customer-management/group-manage/#add-a-main-group'
        "
      ></fuse-help-link>
    </div>

    <form class="flex flex-col gap-4" [formGroup]="groupForm">
      <fuse-input
        class="w-full"
        [form]="groupForm"
        [label]="'Name'"
        [name]="'groupName'"
        [placeholder]="'Enter name'"
        [errorMessages]="errorMessages.groupName"
      />
      <fuse-textarea
        class="w-full"
        [form]="groupForm"
        [label]="'Description'"
        [name]="'description'"
        [placeholder]="'Enter description'"
        [errorMessages]="errorMessages.description"
      />
      @if (data.parentId) {
        <fuse-select
          class="w-full"
          [form]="groupForm"
          [label]="'Main Group'"
          [name]="'parentId'"
          [options]="optionListGroup"
          [placeholder]="'Select parent group'"
          [optionLabel]="'groupName'"
          [optionValue]="'id'"
        />
      }
    </form>
  </div>

  <div class="flex items-start justify-end gap-2">
    <button
      type="button"
      class="btn-outlined__primary__medium"
      (click)="onClose()"
    >
      Cancel
    </button>
    <button
      type="button"
      class="btn-contained__primary__medium"
      [disabled]="loading || groupForm.invalid || !groupForm.dirty"
      (click)="submitForm()"
    >
      @if (loading) {
        <mat-spinner diameter="18" class="mr-0"></mat-spinner>
      }
      {{ data?.groupName ? 'Save' : 'Add' }}
    </button>
  </div>
</div>
