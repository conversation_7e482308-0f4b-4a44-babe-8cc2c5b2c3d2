<div class="parent-page">
  <!--  Breadcrumb -->
  <div class="parent-page__header">
    <div class="flex gap-2 items-center">
      Group Management
      <fuse-help-link
        [url]="'/customer-management/group-manage/#view-groups-and-sub-groups'"
      ></fuse-help-link>
    </div>
  </div>

  <!-- Main -->
  <div class="parent-page__body">
    <!-- CONTENT GOES HERE -->
    <div class="flex w-full h-full bg-white rounded-4 relative">
      <div
        class="w-64 flex flex-col items-start border-r-[1px] border-solid border-grey-500/16 max-[640px]:hidden"
      >
        <div class="flex items-center justify-between w-full px-4 py-3 h-fit">
          <div class="web__subtitle1">Groups</div>

          @if (permissionList.update) {
            <button
              type="button"
              class="flex items-center justify-center px-2.5 py-1 rounded-2 border-[1px] border-solid border-primary"
              (click)="openAddGroupDialog()"
            >
              <mat-icon
                class="w-5 h-auto text-primary"
                [svgIcon]="'heroicons_outline:folder-plus'"
              ></mat-icon>
            </button>
          }
        </div>

        <div
          class="flex flex-col items-start flex-1 w-full gap-1 p-2 overflow-auto"
        >
          @if (groupsTree.length > 0) {
            <fuse-tree
              class="w-full"
              [treeData]="groupsTree"
              [selectedNodeId]="selectedGroupId"
              (selectedNodeChange)="onChangeSelectedNode($event)"
            ></fuse-tree>
          } @else {
            <div class="m-4 italic text-center web__caption">
              No groups are present in the tenant
            </div>
          }
        </div>
      </div>

      <fuse-drawer
        class="sm:hidden rounded-4"
        [mode]="'over'"
        [position]="'left'"
        [opened]="drawerOpen"
        (openedChanged)="setStateToggleDrawer($event)"
      >
        <div
          class="w-full flex flex-col items-start border-r-[1px] border-solid border-grey-500/16"
        >
          <div class="flex items-center justify-between w-full px-4 py-3 h-fit">
            <div class="web__subtitle1">Groups</div>

            @if (permissionList.update) {
              <button
                type="button"
                class="flex items-center justify-center px-2.5 py-1 rounded-2 border-[1px] border-solid border-primary"
                (click)="openAddGroupDialog()"
              >
                <mat-icon
                  class="w-5 h-auto text-primary"
                  [svgIcon]="'heroicons_outline:folder-plus'"
                ></mat-icon>
              </button>
            }
          </div>

          <div
            class="flex flex-col items-start flex-1 w-full gap-1 p-2 overflow-auto"
          >
            @if (groupsTree.length > 0) {
              <fuse-tree
                class="w-full"
                [treeData]="groupsTree"
                [selectedNodeId]="selectedGroupId"
                (selectedNodeChange)="onChangeSelectedNode($event)"
              ></fuse-tree>
            } @else {
              <div class="m-4 italic text-center web__caption">
                No groups are present in the tenant
              </div>
            }
          </div>
        </div>
      </fuse-drawer>

      <div class="flex flex-col flex-1 overflow-auto">
        <div class="w-full p-2 sm:hidden">
          <button class="inline-flex" (click)="toggleDrawer()">
            <mat-icon [svgIcon]="'heroicons_outline:bars-3'"></mat-icon>
          </button>
        </div>

        <mat-tab-group
          class="flex-1"
          mat-stretch-tabs="false"
          mat-align-tabs="start"
          [selectedIndex]="selectedTab"
          (selectedTabChange)="OnChangeSelectedTab($event)"
        >
          <mat-tab [label]="'Group Information'" class="relative">
            @if (groupsTree.length > 0) {
              <app-group-information
                [groupId]="selectedGroupId"
                [leafNode]="isLeafNode"
              ></app-group-information>
            } @else {
              <div class="w-full h-[400px] relative">
                <fuse-error-no-data />
              </div>
            }
          </mat-tab>

          <mat-tab [label]="'Customer'" class="relative">
            @if (groupsTree.length > 0) {
              <app-group-customer
                [groupId]="selectedGroupId"
              ></app-group-customer>
            } @else {
              <div class="w-full h-[400px] relative">
                <fuse-error-no-data />
              </div>
            }
          </mat-tab>
        </mat-tab-group>
      </div>
    </div>
  </div>
</div>
