<div class="flex flex-col h-full gap-4">
  @if (permissionList.update) {
    <div class="flex justify-end gap-4 mt-4">
      @if (selectedCards.length) {
        <button
          type="button"
          class="btn-outlined__error__medium"
          (click)="onOpenDeleteSelectedCardsDialog()"
        >
          <mat-icon [svgIcon]="'heroicons_outline:trash'"></mat-icon>
          <span>Delete</span>
        </button>
      }

      <button
        type="button"
        class="btn-outlined__primary__medium"
        (click)="onOpenAddCardDialog()"
      >
        <mat-icon
          class="w-5 h-5"
          [svgIcon]="'heroicons_outline:plus'"
        ></mat-icon>
        <span>Add Card</span>
      </button>
    </div>
  }

  <div class="flex-auto">
    <fuse-table-component
      [dataSource]="dataSource"
      [displayedColumns]="displayedColumns"
      [actions]="permissionList.update ? actions : []"
      [total]="total"
      [template]="template"
      [hideColBtn]="true"
      [rowClick]="permissionList.update ? onRowClick : undefined"
      [selectElement]="permissionList.update ? true : false"
      [listSelectedElement]="selectedCards"
      (selectedElementChanged)="onChangeSelectedElement($event)"
    ></fuse-table-component>

    <ng-template #template let-column="header" let-element="data">
      @if (column.name === 'Status') {
        <ng-container>
          <mat-slide-toggle
            (click)="stopPropagation($event)"
            [checked]="element.status === 'ACTIVE' ? true : false"
            (change)="detectToggleChange($event, element)"
          >
          </mat-slide-toggle>
        </ng-container>
      }
    </ng-template>
  </div>
</div>
