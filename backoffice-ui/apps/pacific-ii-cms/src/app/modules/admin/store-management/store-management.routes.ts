import { Routes } from '@angular/router';
import { ROUTE } from '../../../core/const';
import { StoreManagementComponent } from './store-management.component';
import { AddStoreComponent } from './add-store/add-store.component';
import { StoreDetailComponent } from './store-detail/store-detail.component';

export default [
  {
    path: ROUTE.STORE.LIST,
    component: StoreManagementComponent,
  },
  {
    path: ROUTE.STORE.ADD,
    component: AddStoreComponent,
  },
  {
    path: ROUTE.STORE.EDIT + '/:id',
    component: AddStoreComponent,
  },
  {
    path: ROUTE.STORE.DETAIL + '/:id',
    component: StoreDetailComponent,
  },
  { path: '', pathMatch: 'full', redirectTo: ROUTE.STORE.LIST },
] as Routes;
