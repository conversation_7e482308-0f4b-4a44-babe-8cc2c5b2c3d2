<div class="flex flex-col gap-6">
  <div class="flex flex-col gap-8 px-3 pt-2 pb-4">
    <div class="web__h6 text-grey-900 flex gap-2 items-center">
      Temporary Closure
      <fuse-help-link
        [url]="'/store-operation/storemange/#view-store-list-and-change-status'"
      ></fuse-help-link>
    </div>

    <form class="flex flex-col gap-4" [formGroup]="closureForm">
      <fuse-select
        class="w-full"
        [form]="closureForm"
        [label]="'Reason'"
        [name]="'suspendType'"
        [options]="optionListReason"
        [placeholder]="'Select reason'"
        [errorMessages]="errorMessages.suspendType"
      />
      <fuse-textarea
        class="w-full"
        [form]="closureForm"
        [label]="'Note'"
        [name]="'note'"
        [placeholder]="'Enter note'"
        [errorMessages]="errorMessages.note"
      />
    </form>
  </div>

  <div class="flex items-start justify-end gap-2">
    <button
      type="button"
      class="btn-outlined__primary__medium"
      (click)="onClose()"
    >
      Cancel
    </button>
    <button
      type="button"
      class="btn-contained__primary__medium"
      [disabled]="loading || closureForm.invalid"
      (click)="submitForm()"
    >
      @if (loading) {
        <mat-spinner diameter="18" class="mr-0"></mat-spinner>
      }
      OK
    </button>
  </div>
</div>
