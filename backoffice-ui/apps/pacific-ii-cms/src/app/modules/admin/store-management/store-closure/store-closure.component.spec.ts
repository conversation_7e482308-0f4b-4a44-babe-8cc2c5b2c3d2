import { provideHttpClient } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { SUSPEND_TYPE } from '../../../../core/const';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';
import { StoreManagementService } from '../store-management.service';
import { IStore } from '../store.types';
import { StoreClosureComponent } from './store-closure.component';
import { ToastrModule } from 'ngx-toastr';
import { of, throwError } from 'rxjs';

describe('StoreClosureComponent', () => {
  let component: StoreClosureComponent;
  let fixture: ComponentFixture<StoreClosureComponent>;

  const mockStore: IStore = {
    storeId: '117511553290924032',
    tenantId: '116024918514279424',
    name: 'Blue Bean',
    email: '<EMAIL>',
    phoneNumber: '+**********',
    addressLine1: '123 Main Street',
    addressLine2: 'Suite 100',
    city: 'New York',
    country: 'USA',
    status: 'ACTIVE',
    postalCode: '10001',
    workingHour: 'Mon-Fri 9:00 AM - 6:00 PM',
    createdAt: 1728016937565,
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [StoreClosureComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: StoreManagementService,
          useValue: {
            suspendStore: jest.fn().mockReturnValue(of(null)),
          },
        },
        {
          provide: MatDialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        {
          provide: MAT_DIALOG_DATA,
          useValue: {
            ...mockStore,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(StoreClosureComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    jest.spyOn(component['_toast'], 'success');
    jest.spyOn(component, 'onClose');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should close the dialog when run onClose', () => {
    component.onClose('test');

    expect(component['_dialogRef'].close).toHaveBeenCalledWith('test');
  });

  it('should call handleSuspendStore method when run submitForm with valid value', () => {
    jest.spyOn(component, 'handleSuspendStore');
    const testValue = {
      suspendType: SUSPEND_TYPE.RENOVATION_OR_RELOCATION,
      note: 'test',
    };
    component.closureForm.patchValue(testValue);

    component.submitForm();

    expect(component.handleSuspendStore).toHaveBeenCalledWith(testValue);
  });

  it('should not call handleSuspendStore method when run submitForm with invalid value', () => {
    jest.spyOn(component, 'handleSuspendStore');
    const testValue = {
      suspendType: null,
      note: 'test',
    };
    component.closureForm.patchValue(testValue);

    component.submitForm();

    expect(component.handleSuspendStore).not.toHaveBeenCalled();
  });

  it('should use service to suspend the store and show toast when success when run handleSuspendStore', () => {
    const testValue = {
      suspendType: null,
      note: 'test',
    };
    component.handleSuspendStore(testValue);

    expect(component['_storesService'].suspendStore).toHaveBeenCalledWith(
      mockStore.storeId,
      testValue,
    );

    expect(component.loading).toEqual(false);
    expect(component['_toast'].success).toHaveBeenCalledWith(
      'Store is temporarily closed.',
    );
    expect(component.onClose).toHaveBeenCalledWith('save');
  });

  it('should use service to suspend the store and change the loading state to false when error when run handleSuspendStore', () => {
    const testValue = {
      suspendType: null,
      note: 'test',
    };
    jest
      .spyOn(component['_storesService'], 'suspendStore')
      .mockReturnValue(throwError(() => null));

    component.handleSuspendStore(testValue);

    expect(component['_storesService'].suspendStore).toHaveBeenCalledWith(
      mockStore.storeId,
      testValue,
    );

    expect(component.loading).toEqual(false);
    expect(component['_toast'].success).not.toHaveBeenCalled();
    expect(component.onClose).not.toHaveBeenCalled();
  });
});
