import { Component, Inject } from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { StoreManagementService } from '../store-management.service';
import { FuseSelectComponent } from '@fuse/components/select';
import { FuseTextareaComponent } from '@fuse/components/textarea';
import { SuspendTypeList } from '../../../../core/const';
import { MatIconModule } from '@angular/material/icon';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-store-closure',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    FuseSelectComponent,
    FuseTextareaComponent,
    MatIconModule,
    FuseHelpLinkComponent,
  ],
  templateUrl: './store-closure.component.html',
})
export class StoreClosureComponent {
  closureForm!: UntypedFormGroup;
  loading = false;

  optionListReason: Array<any> = SuspendTypeList;

  errorMessages = {
    suspendType: {
      required: 'Please select reason!',
    },
    note: {
      maxlength: 'Note cannot exceed 250 characters!',
    },
  };

  constructor(
    private _toast: ToastrService,
    private _dialogRef: MatDialogRef<any>,
    private _formBuilder: UntypedFormBuilder,
    private _storesService: StoreManagementService,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.initForm();
  }

  submitForm(): void {
    for (const i in this.closureForm.controls) {
      this.closureForm.controls[i].markAsTouched();
      this.closureForm.controls[i].updateValueAndValidity();
    }

    if (this.closureForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    this.loading = true;
    const closureData = {
      ...this.closureForm.value,
    };
    this.handleSuspendStore(closureData);
  }

  handleSuspendStore(closureData: any): void {
    this._storesService.suspendStore(this.data.storeId, closureData).subscribe({
      next: () => {
        this.loading = false;
        this._toast.success('Store is temporarily closed.');
        this.onClose('save');
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  onClose(message?: string): void {
    this._dialogRef.close(message);
  }

  private initForm(): void {
    this.closureForm = this._formBuilder.group({
      suspendType: [null, [Validators.required]],
      note: [null, [Validators.maxLength(250)]],
    });
  }
}
