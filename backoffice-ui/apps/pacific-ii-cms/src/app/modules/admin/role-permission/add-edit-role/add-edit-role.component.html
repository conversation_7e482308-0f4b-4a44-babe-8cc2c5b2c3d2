<div class="flex flex-col w-full">
  <div class="page-header">
    <div class="page-header__wrapper">
      <div class="page-header__breadcrumb">
        <div class="page-header__breadcrumb__parent">Role & Permissions</div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">
          {{
            action === 'add'
              ? 'Add New Role'
              : action === 'edit'
                ? 'Edit Role'
                : 'Role Information'
          }}
        </div>
      </div>

      <div class="page-header__title">
        <button type="button" (click)="goBack()">
          <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
        </button>
        <div>
          {{
            action === 'add'
              ? 'Add New Role'
              : action === 'edit'
                ? 'Edit Role'
                : 'Role Information'
          }}
        </div>
        <fuse-help-link
          [url]="
            action === 'add'
              ? '/user-management/rolepermission/#create-a-new-role'
              : action === 'edit'
                ? '/user-management/rolepermission/#edit-a-role'
                : '/user-management/rolepermission/#view-and-delete-a-role'
          "
        ></fuse-help-link>
      </div>
    </div>
    <div class="page-header__action">
      @if (action === 'view' && userPermissionList.update) {
        <button
          type="button"
          class="btn-soft__primary__medium"
          (click)="gotoEditRole()"
        >
          <mat-icon class="w-5 h-5" [svgIcon]="'styl:Pencil'"></mat-icon>
          <span>Edit</span>
        </button>
      }
      @if (action !== 'view') {
        <button
          type="button"
          class="btn-outlined__primary__medium"
          (click)="goBack()"
        >
          <mat-icon
            class="w-5 h-5"
            [svgIcon]="'heroicons_outline:x-mark'"
          ></mat-icon>
          <span>Cancel</span>
        </button>
        <button
          type="button"
          class="btn-contained__success__medium"
          (click)="handleSaveRole()"
          [disabled]="
            loading ||
            roleForm.invalid ||
            (!roleForm.dirty && !permissionsChanged)
          "
        >
          @if (loading) {
            <mat-spinner diameter="18" class="mr-0"></mat-spinner>
          } @else {
            <mat-icon
              class="w-5 h-5"
              [svgIcon]="'heroicons_outline:check'"
            ></mat-icon>
          }
          <span>Save</span>
        </button>
      }
    </div>
  </div>

  <div class="w-full">
    <form [formGroup]="roleForm">
      <div class="flex-auto w-full p-4">
        <!-- GENERAL INFORMATION -->
        <div class="box">
          <div class="box__header__title rounded-tr-4 rounded-tl-4">
            General Information
          </div>
          @if (action !== 'view') {
            <div
              class="grid sm:grid-cols-2 grid-cols-1 sm:gap-4 gap-2 general-info"
            >
              <fuse-input
                class="w-full"
                [form]="roleForm"
                [label]="'Role Name'"
                [name]="'name'"
                [placeholder]="'Enter role name'"
                [errorMessages]="errorMessages.name"
              />
            </div>
          }
          @if (action === 'view') {
            <div class="flex flex-col gap-4 mt-4">
              <div class="grid sm:grid-cols-2 grid-cols-1 gap-4">
                <div class="card">
                  <span class="card__label"> Name </span>
                  <div>
                    {{
                      roleDetail && roleDetail.roleTitle
                        ? roleDetail.roleTitle
                        : 'N/A'
                    }}
                  </div>
                </div>

                <div class="card">
                  <span class="card__label"> Role ID </span>
                  <div>
                    {{ roleDetail && roleDetail.id ? roleDetail.id : 'N/A' }}
                  </div>
                </div>
              </div>
            </div>
          }
        </div>
      </div>
    </form>
    <div class="dashed-border"></div>
    <div class="flex-auto w-full p-4">
      <!-- PERMISSION -->
      <div class="box">
        <div class="box__header__title rounded-tr-4 rounded-tl-4">
          Permission
        </div>
        <form [formGroup]="searchForm">
          <div class="">
            <fuse-input
              class="w-full"
              [form]="searchForm"
              [label]="''"
              [name]="'permission'"
              [placeholder]="'Search...'"
              [prefixIcon]="'heroicons_outline:magnifying-glass'"
              [prefixIconColor]="'#343330'"
            />
          </div>
        </form>
        <div class="flex flex-col gap-4 mt-4">
          <mat-checkbox
            class="permission"
            color="primary"
            [checked]="allSelected()"
            (change)="toggleAll($event.checked)"
            [disabled]="action === 'view'"
          >
            <div class="checkbox-label">
              <span> Full Access </span>
            </div>
          </mat-checkbox>
          <div class="flex flex-col gap-4">
            @if (filteredPermissions && filteredPermissions.length) {
              @for (
                permission of filteredPermissions;
                track trackByFn($index, permission)
              ) {
                <div>
                  <div class="flex items-center mb-2">
                    <mat-checkbox
                      class="permission"
                      color="primary"
                      [checked]="isAllChecked(permission)"
                      [indeterminate]="partiallyComplete(permission)"
                      (change)="update(permission, $event.checked)"
                      [disabled]="action === 'view'"
                    >
                      {{ permission.nodeTitle }}
                    </mat-checkbox>
                    <div class="line"></div>
                  </div>

                  @if (permission.children?.length > 0) {
                    <div class="grid sm:grid-cols-3 grid-cols-1 gap-2 ml-6">
                      @for (
                        child of permission.children;
                        track child;
                        let i = $index
                      ) {
                        <mat-checkbox
                          class="permission"
                          color="primary"
                          [checked]="child.checked"
                          (change)="update(child, $event.checked, permission)"
                          [disabled]="action === 'view'"
                        >
                          {{ child.nodeTitle }}
                        </mat-checkbox>
                      }
                    </div>
                  }
                </div>
              }
            }
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
