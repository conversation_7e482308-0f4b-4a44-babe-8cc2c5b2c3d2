<div class="flex flex-col w-full">
  <div class="page-header">
    <div class="page-header__wrapper">
      <div class="page-header__breadcrumb">
        <div class="page-header__breadcrumb__parent">Tenant Management</div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">
          {{ tenantDetail && tenantDetail.name ? tenantDetail.name : '' }}
        </div>
      </div>

      <div class="page-header__title">
        <button type="button" (click)="goBack()">
          <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
        </button>
        <div>Tenant Overview</div>
      </div>
    </div>

    <div class="flex gap-2.5 justify-end w-full sm:w-auto">
      @if (tenantDetail && tenantDetail.status === TENANT_STATUS[1].value) {
        <button
          type="button"
          class="btn-outlined__primary__medium"
          (click)="handleLoginAsTenant()"
        >
          <mat-icon class="w-5 h-5" [svgIcon]="'styl:SignInBold'"></mat-icon>
          <span>Login as Tenant</span>
        </button>
      }

      @if (permissionList.update) {
        <button
          type="button"
          class="btn-soft__primary__medium"
          (click)="gotoEditTenant()"
        >
          <mat-icon class="w-5 h-5" [svgIcon]="'styl:Pencil'"></mat-icon>
          <span>Edit</span>
        </button>
      }
    </div>
  </div>

  <div class="w-full">
    @if (tenantDetail && tenantDetail.status === 'UNDER_REVIEW') {
      <div class="mx-5 mt-6 mb-8">
        <app-tenant-checklist [tenantId]="id"></app-tenant-checklist>
      </div>

      <div class="dashed-border"></div>
    }

    <div>
      <app-tenant-information
        [tenantDetail]="tenantDetail"
      ></app-tenant-information>
    </div>
  </div>
</div>
