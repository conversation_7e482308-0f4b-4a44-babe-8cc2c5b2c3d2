import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AddTenantConfigurationsComponent } from './add-tenant-configurations.component';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { ToastrModule } from 'ngx-toastr';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import {
  API,
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { Currency, TenantSetting } from '../tenant.types';
import { FuseUltils } from '@fuse/ultils';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('AddTenantConfigurationsComponent', () => {
  let component: AddTenantConfigurationsComponent;
  let fixture: ComponentFixture<AddTenantConfigurationsComponent>;
  let httpTestingController: HttpTestingController;

  // Spy Instances
  let handleGetTenantSettingSpy: jest.SpyInstance;

  // Mock data
  const mockTenantId = '135261622300581888';

  const mockDateFormats = [
    'dd/MM/yyyy',
    'MM/dd/yyyy',
    'MMM dd yyyy',
    'dd-MM-yyyy',
    'MM-dd-yyyy',
    'yyyy-MM-dd',
    'dd MMM yyyy',
  ];

  const mockDateFormatOptions = mockDateFormats.map((value) => ({
    value: value,
  }));

  const mockTimeFormats = ['HH:mm:ss', 'HH:mm', 'hh:mm:ss a', 'hh:mm a'];

  const mockTimeFormatOptions = mockTimeFormats.map((value) => ({
    value: value,
  }));

  const mockTimeZones = [
    {
      zoneId: 'Etc/GMT+12',
      gtmOffset: 'GMT-12:00',
      displayName: '(GMT-12:00) Etc/GMT+12',
    },
    {
      zoneId: 'Etc/GMT+11',
      gtmOffset: 'GMT-11:00',
      displayName: '(GMT-11:00) Etc/GMT+11',
    },
    {
      zoneId: 'Pacific/Midway',
      gtmOffset: 'GMT-11:00',
      displayName: '(GMT-11:00) Pacific/Midway',
    },
    {
      zoneId: 'Pacific/Niue',
      gtmOffset: 'GMT-11:00',
      displayName: '(GMT-11:00) Pacific/Niue',
    },
  ];

  const mockCurrencies: Array<Currency> = [
    {
      displayName: 'US Dollar',
      numericCode: 840,
      currencyCode: 'USD',
      symbol: '$',
      fractionDigits: 2,
    },
    {
      displayName: 'Singapore Dollar',
      numericCode: 702,
      currencyCode: 'SGD',
      symbol: 'SGD',
      fractionDigits: 2,
    },
    {
      displayName: 'Vietnamese Dong',
      numericCode: 704,
      currencyCode: 'VND',
      symbol: '₫',
      fractionDigits: 0,
    },
    {
      displayName: 'Japanese Yen',
      numericCode: 392,
      currencyCode: 'JPY',
      symbol: '¥',
      fractionDigits: 0,
    },
  ];

  const mockPermissionList = [
    {
      externalId: `${ROLE_MODULES.TENANT_MGMT_SETTING}_${ROLE_SCOPES.UPDATE}`,
    },
  ];

  const mockConfigDomains = { baseDomain: 'pacific-ii-sit.styl.solutions' };

  const mockTenantSettings: TenantSetting = {
    defaultDomain: `tenantx.${mockConfigDomains.baseDomain}`,
    domainName: `tenantx.${mockConfigDomains.baseDomain}`,
    timeZone: mockTimeZones[0],
    currency: mockCurrencies[0],
    dateFormat: mockDateFormats[0],
    timeFormat: mockTimeFormats[0],
  };

  const mockFormValue = {
    defaultDomain: `tenantx`,
    timeZone: mockTimeZones[0].zoneId,
    currency: mockCurrencies[0].currencyCode,
    dateFormat: mockDateFormats[0],
    timeFormat: mockTimeFormats[0],
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AddTenantConfigurationsComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        provideHttpClientTesting(),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({
              id: mockTenantId,
            }),
            paramMap: of({
              params: { id: mockTenantId },
            }),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AddTenantConfigurationsComponent);
    component = fixture.componentInstance;
    httpTestingController = TestBed.inject(HttpTestingController);

    component['_userPermissionService']['_permissions'].next(
      mockPermissionList,
    );
    component['_userPermissionService']['permissionList'] = mockPermissionList;

    component.data = {
      isFinalStep: false,
    };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle Hook', () => {
    it('should call the handleGetData method when run ngOnInit', () => {
      const handleGetDataSpy = jest.spyOn(component, 'handleGetData');

      component.ngOnInit();

      expect(handleGetDataSpy).toHaveBeenCalled();
    });

    it('should unsubscribe the all the observable when run the ngOnDestroy ', () => {
      const unsubscribeNextSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'next',
      );
      const unsubscribeCompleteSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'complete',
      );

      component.ngOnDestroy();

      expect(unsubscribeNextSpy).toHaveBeenCalled();
      expect(unsubscribeCompleteSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    beforeEach(() => {
      handleGetTenantSettingSpy = jest.spyOn(
        component,
        'handleGetTenantSetting',
      );
    });

    it('should send request to server to get the date format, time formats, timezones, domain, currencies and call the handleGetTenantSetting when run handleGetData', () => {
      component.id = mockTenantId;
      component.handleGetData();

      const dateFormatReq = httpTestingController.match(
        API.UTILITY.DATE_FORMAT,
      );
      const timeFormatReq = httpTestingController.match(
        API.UTILITY.TIME_FORMAT,
      );
      const timezoneReq = httpTestingController.match(API.UTILITY.TIMEZONES);
      const currencyReq = httpTestingController.match(API.UTILITY.CURRENCY);
      const configDomainReq = httpTestingController.match(
        API.UTILITY.CONFIG.DOMAIN,
      );

      dateFormatReq[0].flush(mockDateFormats);
      timeFormatReq[0].flush(mockTimeFormats);
      timezoneReq[0].flush(mockTimeZones);
      currencyReq[0].flush(mockCurrencies);
      configDomainReq[0].flush(mockConfigDomains);

      expect(component.optionListDateFormat).toEqual(mockDateFormatOptions);
      expect(component.optionListTimeFormat).toEqual(mockTimeFormatOptions);
      expect(component.optionListTimezone).toEqual(mockTimeZones);
      expect(component.optionListCurrency).toEqual(mockCurrencies);
      expect(component.suffixDomain).toBe(`.${mockConfigDomains.baseDomain}`);

      expect(handleGetTenantSettingSpy).toHaveBeenCalled();
    });

    it('should send request to server to get the tenant setting when run handleGetTenantSetting and disable controls when there is setting data', () => {
      component.id = mockTenantId;

      component.handleGetTenantSetting().subscribe();

      const req = httpTestingController.expectOne(
        API.TENANT.GET_SETTING.replace('{id}', `${mockTenantId}`),
      );
      req.flush(mockTenantSettings);

      expect(component.configForm.getRawValue()).toEqual({
        ...mockTenantSettings,
        domainName: mockTenantSettings.defaultDomain,
        currency: mockTenantSettings.currency.currencyCode,
        timeZone: mockTenantSettings.timeZone.zoneId,
      });

      expect(component.configForm.controls['currency'].disabled).toBe(true);
      expect(component.configForm.controls['defaultDomain'].disabled).toBe(
        true,
      );
      expect(component.subdomainStatus).toBe(1);
      expect(component.showPopup).toBe(false);
    });

    it('should send request to server to get the tenant setting when run handleGetTenantSetting', () => {
      component.id = mockTenantId;

      component['getPermission']();
      component.handleGetTenantSetting().subscribe();

      const req = httpTestingController.expectOne(
        API.TENANT.GET_SETTING.replace('{id}', `${mockTenantId}`),
      );
      req.flush({});

      expect(component.configForm.getRawValue()).toEqual({
        defaultDomain: null,
        domainName: null,
        currency: null,
        timeZone: null,
        dateFormat: null,
        timeFormat: null,
      });

      expect(component.configForm.controls['currency'].disabled).toBe(false);
      expect(component.configForm.controls['defaultDomain'].disabled).toBe(
        false,
      );
      expect(component.showPopup).toBe(true);
      expect(component.subdomainStatus).not.toBe(1);
    });

    it('should get the permission to authorize the action when run the getPermission method', () => {
      expect(component.permissionList).toEqual({
        update: false,
      });

      component['getPermission']();

      expect(component.permissionList).toEqual({
        update: true,
      });
    });

    it('should get the tenant id in queryParams when run the getTenantId method with route does not contain the Onboarding Route', () => {
      jest
        .spyOn(component['_router'], 'url', 'get')
        .mockReturnValue(`${ROUTE.TENANT.ONBOARDING}/${mockTenantId}`);

      component['getTenantId']();

      expect(component.id).toBe(mockTenantId);
    });

    it('should get the tenant id in paramsMap when run the getTenantId method with route contains the Onboarding Route', () => {
      jest
        .spyOn(component['_router'], 'url', 'get')
        .mockReturnValue(`${ROUTE.TENANT.EDIT}/${mockTenantId}`);

      component['getTenantId']();

      expect(component.id).toBe(mockTenantId);
    });
  });

  describe('Utility Function', () => {
    it('should call the handleUpdateSetting method when run the submit form with valid data', () => {
      component['getPermission']();

      const handleUpdateSettingSpy = jest.spyOn(
        component,
        'handleUpdateTenantSetting',
      );
      component.configForm.patchValue(mockFormValue);
      component.suffixDomain = `.${mockConfigDomains.baseDomain}`;
      component.subdomainStatus = 1;

      component.submitForm();

      expect(component.loading).toBe(true);
      expect(component.configForm.invalid).toBe(false);
      expect(handleUpdateSettingSpy).toHaveBeenCalledWith({
        ...mockFormValue,
        defaultDomain: mockFormValue.defaultDomain + component.suffixDomain,
      });
    });

    it('should not call the handleUpdateSetting method when run the submit form with invalid data', () => {
      component['getPermission']();

      const handleUpdateSettingSpy = jest.spyOn(
        component,
        'handleUpdateTenantSetting',
      );
      component.configForm.patchValue({}, { emitEvent: true });

      component.submitForm();

      expect(component.loading).toBe(false);
      expect(component.configForm.invalid).toBe(true);
      expect(handleUpdateSettingSpy).not.toHaveBeenCalled();
    });

    it('should call the update method and run the toast success when run the handleUpdateTenantSetting method successfully', () => {
      const mockData = {
        ...mockFormValue,
        defaultDomain: mockFormValue.defaultDomain + component.suffixDomain,
      };

      component.goStepperNext = jest.fn();
      component.id = mockTenantId;
      const toastSuccessSpy = jest.spyOn(component['_toast'], 'success');

      component.handleUpdateTenantSetting(mockData);

      const req = httpTestingController.expectOne(
        API.TENANT.UPDATE_SETTING.replace('{id}', mockTenantId),
      );
      req.flush('Success');

      expect(component.loading).toBe(false);
      expect(component.goStepperNext).toHaveBeenCalled();
      expect(toastSuccessSpy).toHaveBeenCalledWith(
        'tenant.message.update-success',
      );
    });

    it('should call the update method but not run the toast success when run the handleUpdateTenantSetting method unsuccessfully', () => {
      const mockData = {
        ...mockFormValue,
        defaultDomain: mockFormValue.defaultDomain + component.suffixDomain,
      };

      component.goStepperNext = jest.fn();
      component.id = mockTenantId;
      const toastSuccessSpy = jest.spyOn(component['_toast'], 'success');

      component.handleUpdateTenantSetting(mockData);

      const req = httpTestingController.expectOne(
        API.TENANT.UPDATE_SETTING.replace('{id}', mockTenantId),
      );
      req.error(new ProgressEvent('Error'));

      expect(component.loading).toBe(false);
      expect(component.goStepperNext).not.toHaveBeenCalled();
      expect(toastSuccessSpy).not.toHaveBeenCalled();
    });

    it('should check the availability of the domain name and change subdomainStatus to 1 if return value is available when run onCheckAvailabilitySubDomain', () => {
      const defaultDomainControl =
        component.configForm.controls['defaultDomain'];
      component.suffixDomain = `.${mockConfigDomains.baseDomain}`;
      defaultDomainControl.patchValue(mockFormValue.defaultDomain);

      component.onCheckAvailabilitySubDomain();

      const req = httpTestingController.expectOne(
        `${API.TENANT.CHECK_DOMAIN}?${FuseUltils.objectToQueryString({ domain: mockTenantSettings.defaultDomain })}`,
      );
      req.flush({ available: true });

      expect(component.subdomainStatus).toBe(1);
      expect(defaultDomainControl.errors).toBe(null);
    });

    it('should check the availability of the domain name and change subdomainStatus to 2 if return value is unavailable when run onCheckAvailabilitySubDomain', () => {
      const defaultDomainControl =
        component.configForm.controls['defaultDomain'];
      component.suffixDomain = `.${mockConfigDomains.baseDomain}`;
      defaultDomainControl.patchValue(mockFormValue.defaultDomain);

      component.onCheckAvailabilitySubDomain();

      const req = httpTestingController.expectOne(
        `${API.TENANT.CHECK_DOMAIN}?${FuseUltils.objectToQueryString({ domain: mockTenantSettings.defaultDomain })}`,
      );
      req.flush({ available: false });

      expect(component.subdomainStatus).toBe(2);
      expect(defaultDomainControl.errors).toEqual({
        unavailable: true,
      });
    });
  });
});
