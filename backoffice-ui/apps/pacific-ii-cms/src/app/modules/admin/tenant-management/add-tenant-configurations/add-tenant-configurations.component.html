<div class="flex flex-col justify-between h-full">
  <form class="flex flex-col gap-4 m-4" [formGroup]="configForm">
    <!-- DOMAIN -->
    <div class="box">
      <div>
        <div class="box__header__title">
          {{ 'domain.main' | transloco }}
        </div>
      </div>

      @if (isActivateTenant() && optionListDomain.length > 1) {
        <div>
          <fuse-select
            class="w-full"
            [form]="configForm"
            [label]="'domain.default.main'"
            [name]="'domainName'"
            [options]="optionListDomain"
            [placeholder]="'domain.default.select-placeholder'"
            [errorMessages]="errorMessages.domainName"
          />
        </div>
      } @else {
        <div class="grid grid-cols-1 gap-2 sm:grid-cols-2 sm:gap-4">
          <fuse-input
            class="w-full"
            [form]="configForm"
            [label]="
              configForm.controls['defaultDomain'].disabled
                ? 'Default Domain'
                : 'Sub-domain'
            "
            [name]="'defaultDomain'"
            [placeholder]="
              configForm.controls['defaultDomain'].disabled
                ? 'Enter default domain'
                : 'Enter sub-domain'
            "
            [errorMessages]="errorMessages.defaultDomain"
            [suffixIcon]="subdomainStatusList[subdomainStatus].icon"
            [suffixIconColor]="subdomainStatusList[subdomainStatus].color"
          />

          @if (!configForm.controls['defaultDomain'].disabled) {
            <div class="flex items-center gap-2.5 sm:mt-6 mt-0">
              <div
                class="h-fit font-onest font-normal text-base leading-[22px] text-grey-700"
              >
                {{ suffixDomain }}
              </div>

              <button
                type="button"
                class="text-base font-bold leading-6 cursor-pointer h-fit font-onest text-primary"
                [ngClass]="{
                  '!text-grey-500 hover:!cursor-default':
                    configForm.controls['defaultDomain'].invalid,
                }"
                [disabled]="configForm.controls['defaultDomain'].invalid"
                (click)="onCheckAvailabilitySubDomain()"
              >
                {{ 'domain.check-availability' | transloco }}
              </button>
            </div>
          }
        </div>
      }
    </div>

    <!-- SETTING -->
    <div class="box">
      <div>
        <div class="box__header__title">{{ 'common.setting' | transloco }}</div>
      </div>

      <div class="grid grid-cols-1 gap-2 sm:grid-cols-2 sm:gap-4">
        <fuse-select
          class="w-full"
          [form]="configForm"
          [label]="'tenant.date-format.main'"
          [name]="'dateFormat'"
          [options]="optionListDateFormat"
          [placeholder]="'tenant.date-format.placeholder'"
          [optionLabel]="'value'"
          [optionValue]="'value'"
          [errorMessages]="errorMessages.dateFormat"
        />
        <fuse-select
          class="w-full"
          [form]="configForm"
          [label]="'tenant.time-format.main'"
          [name]="'timeFormat'"
          [options]="optionListTimeFormat"
          [placeholder]="'tenant.time-format.placeholder'"
          [optionLabel]="'value'"
          [optionValue]="'value'"
          [errorMessages]="errorMessages.timeFormat"
        />
        <fuse-select
          class="w-full"
          [form]="configForm"
          [label]="'tenant.timezone.main'"
          [name]="'timeZone'"
          [options]="optionListTimezone"
          [placeholder]="'tenant.timezone.placeholder'"
          [optionLabel]="'displayName'"
          [optionValue]="'zoneId'"
          [errorMessages]="errorMessages.timeZone"
          [showSearch]="true"
          [offlineSearch]="true"
        />
        <fuse-select
          class="w-full"
          [form]="configForm"
          [label]="'tenant.currency-code.main'"
          [name]="'currency'"
          [options]="optionListCurrency"
          [placeholder]="'tenant.currency-code.placeholder'"
          [optionLabel]="'currencyCode'"
          [optionValue]="'currencyCode'"
          [errorMessages]="errorMessages.currency"
        />
      </div>
    </div>
  </form>

  <div class="flex justify-between gap-2 p-6 bg-white">
    <button
      type="button"
      class="btn-outlined__primary__medium"
      (click)="goStepperPrevious()"
    >
      <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
      {{ 'common.action.back' | transloco }}
    </button>

    <div class="flex gap-4">
      <!-- The skip button is shown when it's in edit mode or it has been saved once before -->
      @if (
        !data.isFinalStep &&
        ((isShowSkip() && permissionList.update) ||
          (editMode && permissionList.update))
      ) {
        <button
          type="button"
          class="btn-outlined__primary__medium"
          (click)="goStepperNext()"
          [disabled]="loading"
        >
          Skip
        </button>
      }

      @if (permissionList.update) {
        <button
          type="button"
          class="btn-contained__primary__medium"
          (click)="showPopup ? openPopup() : submitForm()"
          [disabled]="
            loading ||
            configForm.invalid ||
            subdomainStatus !== 1 ||
            configForm.pristine
          "
        >
          @if (loading) {
            <mat-spinner diameter="18" class="mr-0"></mat-spinner>
          }
          {{
            (editMode && data.isFinalStep
              ? 'common.action.save'
              : 'common.action.save-continue'
            ) | transloco
          }}
        </button>
      } @else {
        <button
          type="button"
          class="btn-contained__primary__medium"
          (click)="data.isFinalStep ? navigateNextPage() : goStepperNext()"
        >
          {{
            (editMode && data.isFinalStep
              ? 'common.action.finish'
              : 'common.action.next'
            ) | transloco
          }}
        </button>
      }
    </div>
  </div>
</div>
