<div class="flex flex-col justify-between h-full">
  <form class="flex flex-col gap-4 m-4" [formGroup]="inviteUserForm">
    <div class="box max-[640px]:gap-4">
      <div class="box__header">
        <div class="box__header__wrapper">
          <div class="box__header__title">Invite Users to The Manager Role</div>
          <div class="box__header__description">
            An email will be sent to the addresses containing a link to activate
            the account.
            <br />
            After activation, the accounts will access the portal to set up the
            business according to the access rights
          </div>
        </div>

        @if (invitedUsers.length > 0 && permissionList.create) {
          <button
            type="button"
            class="btn-contained__primary__medium max-[640px]:hidden"
            (click)="addUser()"
          >
            <mat-icon
              class="w-5 h-5"
              [svgIcon]="'heroicons_outline:plus'"
            ></mat-icon>
            <span class="whitespace-nowrap">Add New</span>
          </button>
        }
      </div>

      @if (invitedUsers.length > 0) {
        <div class="overflow-auto list-invited-user-card max-[640px]:hidden">
          <div class="list-invited-user-card__item border-right">
            <div class="list-invited-user-card__label">Email</div>
            <div class="flex flex-col flex-1 gap-1">
              @for (user of invitedUsers; track user) {
                <div class="flex list-invited-user-card__text gap-0.5">
                  <div>
                    {{ user.email }}
                  </div>
                  @if (
                    user.userStatus === 'ARCHIVED' ||
                    user.userStatus === 'BLOCKED'
                  ) {
                    <mat-icon
                      class="w-4 h-4"
                      [svgIcon]="'heroicons_outline:information-circle'"
                      [class.text-error-main]="
                        user.userStatus === 'ARCHIVED' &&
                        user.schedulingDeletedAt
                      "
                      [class.text-warning-main]="
                        (user.userStatus === 'ARCHIVED' ||
                          user.userStatus === 'BLOCKED') &&
                        !user.schedulingDeletedAt
                      "
                      [matTooltip]="
                        user.userStatus === 'ARCHIVED' &&
                        user.schedulingDeletedAt
                          ? 'User has been scheduled for deletion.'
                          : user.userStatus === 'BLOCKED'
                            ? 'User has been blocked.'
                            : 'User has been archived.'
                      "
                    ></mat-icon>
                  }
                </div>
              }
            </div>
          </div>

          <div class="list-invited-user-card__item border-right">
            <div class="list-invited-user-card__label">User Role</div>
            @for (user of invitedUsers; track user) {
              <div class="list-invited-user-card__text">
                {{ user.userRole }}
              </div>
            }
          </div>

          <div
            class="list-invited-user-card__item"
            [ngClass]="{
              'border-right': permissionList.update || permissionList.delete,
            }"
          >
            <div class="list-invited-user-card__label">Permission Status</div>
            @for (user of invitedUsers; track user) {
              <div class="flex gap-2.5">
                <div class="list-invited-user-card__status">
                  {{ user.permissionStatus }}
                </div>
                @if (user.userStatus === 'CREATED') {
                  <button
                    type="button"
                    class="list-invited-user-card__btn text-primary whitespace-nowrap"
                    (click)="onSendInvitation(user)"
                  >
                    Resend Invitation
                  </button>
                }
              </div>
            }
          </div>

          @if (permissionList.update || permissionList.delete) {
            <div class="list-invited-user-card__item">
              <div class="list-invited-user-card__label">Action</div>
              @for (user of invitedUsers; track user) {
                <div>
                  @if (
                    permissionList.delete &&
                    !getPermissionExcludeCurrentTenant(user).length &&
                    !user.schedulingDeletedAt
                  ) {
                    <button
                      type="button"
                      class="list-invited-user-card__btn text-error-main whitespace-nowrap"
                      (click)="onOpenDeleteUserDialog(user)"
                    >
                      Delete
                    </button>
                  } @else if (
                    permissionList.update &&
                    getPermissionExcludeCurrentTenant(user).length
                  ) {
                    <button
                      type="button"
                      class="list-invited-user-card__btn text-error-main whitespace-nowrap"
                      (click)="onOpenRemoveUserPermissionDialog(user)"
                    >
                      Remove Permission
                    </button>
                  } @else {
                    <div class="h-[22px]"></div>
                  }
                </div>
              }
            </div>
          }
        </div>
      }

      @if (invitedUsers.length > 0) {
        <div class="dashed-border sm:hidden"></div>

        <div class="overflow-auto list-invited-user-card sm:hidden flex-col">
          @for (user of invitedUsers; track user) {
            <div class="flex flex-col gap-2">
              <div class="list-invited-user-card__item border-none">
                <div class="list-invited-user-card__label">Email</div>
                <div class="flex flex-col flex-1 gap-1">
                  <div class="list-invited-user-card__text">
                    {{ user.email }}
                  </div>
                </div>
              </div>

              <div class="list-invited-user-card__item border-none">
                <div class="list-invited-user-card__label">User Role</div>
                <div class="list-invited-user-card__text">
                  {{ user.userRole }}
                </div>
              </div>

              <div class="list-invited-user-card__item">
                <div class="list-invited-user-card__label">
                  Permission Status
                </div>
                <div class="flex gap-2.5">
                  <div class="list-invited-user-card__status">
                    {{ user.permissionStatus }}
                  </div>
                  @if (user.userStatus === 'CREATED') {
                    <button
                      class="list-invited-user-card__btn text-primary whitespace-nowrap"
                      (click)="onSendInvitation(user)"
                    >
                      Resend Invitation
                    </button>
                  }
                </div>
              </div>

              @if (permissionList.update || permissionList.delete) {
                <div class="list-invited-user-card__item">
                  <div class="list-invited-user-card__label">Action</div>
                  <div>
                    @if (
                      permissionList.delete &&
                      !getPermissionExcludeCurrentTenant(user).length
                    ) {
                      <button
                        class="list-invited-user-card__btn text-error-main whitespace-nowrap"
                        (click)="onOpenDeleteUserDialog(user)"
                      >
                        Delete
                      </button>
                    } @else if (
                      permissionList.update &&
                      getPermissionExcludeCurrentTenant(user).length
                    ) {
                      <button
                        class="list-invited-user-card__btn text-error-main whitespace-nowrap"
                        (click)="onOpenRemoveUserPermissionDialog(user)"
                      >
                        Remove Permission
                      </button>
                    } @else {
                      <div class="h-[22px]"></div>
                    }
                  </div>
                </div>

                @if ($index !== invitedUsers.length - 1) {
                  <mat-divider></mat-divider>
                }
              }
            </div>
          }
        </div>

        <div class="dashed-border sm:hidden"></div>
      }

      @if (invitedUsers.length > 0 && permissionList.create) {
        <button
          class="btn-contained__primary__medium sm:hidden"
          (click)="addUser()"
        >
          <mat-icon
            class="w-5 h-5"
            [svgIcon]="'heroicons_outline:plus'"
          ></mat-icon>
          <span class="whitespace-nowrap">Add New</span>
        </button>
      }

      <div class="flex flex-col w-full gap-4" formArrayName="users">
        @for (user of userArray.controls; track user; let i = $index) {
          <div
            class="flex w-full gap-4 max-[640px]:flex-col"
            [formGroupName]="i"
          >
            <div class="flex flex-1 gap-4 max-[640px]:flex-col">
              <fuse-input
                class="w-full"
                [form]="getUserFormGroup(user)"
                [label]="'Email'"
                [name]="'email'"
                [placeholder]="'Enter email'"
                [prefixIcon]="'heroicons_outline:envelope'"
                [prefixIconColor]="'#343330'"
                [errorMessages]="errorMessages.email"
              />
              <fuse-select
                class="w-full"
                [form]="getUserFormGroup(user)"
                [label]="'User Role'"
                [name]="'userRoleId'"
                [options]="optionListUserRole"
                [placeholder]="'Select user role'"
                [optionLabel]="'roleTitle'"
                [optionValue]="'id'"
                [errorMessages]="errorMessages.userRoleId"
              />
            </div>

            @if (invitedUsers.length > 0) {
              <div
                class="sm:w-10 w-full sm:mt-6 mt-0 max-[640px]:flex max-[640px]:justify-end"
              >
                <button
                  class="btn-outlined__error__medium"
                  type="button"
                  (click)="removeUser(i)"
                >
                  <mat-icon
                    class="w-4 h-4 text-error-main"
                    [svgIcon]="'heroicons_outline:trash'"
                  ></mat-icon>
                  <p class="sm:hidden">Delete</p>
                </button>
              </div>
            }
            @if (i !== userArray.controls.length - 1) {
              <mat-divider class="sm:hidden"></mat-divider>
            }
          </div>
        }
      </div>
    </div>
  </form>

  <div class="flex justify-between gap-2 p-6 bg-white">
    <button
      type="button"
      class="btn-outlined__primary__medium"
      (click)="goStepperPrevious()"
    >
      <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
      Back
    </button>

    <button
      type="button"
      class="btn-contained__primary__medium"
      (click)="submitForm()"
      [disabled]="loading || inviteUserForm.invalid"
    >
      @if (loading) {
        <mat-spinner diameter="18" class="mr-0"></mat-spinner>
      }
      Finish
    </button>
  </div>
</div>
