export interface TenantList {
  content: Array<TenantInfo>;
  totalElements: number;
  totalPages: number;
  page: number;
  sort: Array<string>;
}

export interface TenantInfo {
  tenantId?: number;
  name: string;
  businessRegNo: string;
  businessType: string;
  status?: string;
  email: string;
  phoneNumber: string;
  contactRemarks: string;
  email2?: string | null;
  phoneNumber2?: string | null;
  contactRemarks2?: string | null;
  email3?: string | null;
  phoneNumber3?: string | null;
  contactRemarks3?: string | null;
  website: string | null;
  logo?: Logo | null;
  logoPath?: string;
  realmId?: string;
  addressLine1: string;
  addressLine2: string | null;
  city: string;
  country: {
    countryCode: string;
    countryName: string;
  };
  postalCode: string | null;
  timeZone?: string;
  createdAt?: number;
  updatedAt?: number;
  activatedAt?: number;
  currency?: Currency;
  settings?: any;
}

export interface Logo {
  path: string;
  url: string;
}

export interface Currency {
  displayName: string;
  numericCode: number;
  currencyCode: string;
  symbol: string;
  fractionDigits: number;
}

export interface TenantSetting {
  defaultDomain: string;
  timeZone: {
    zoneId: string;
    gtmOffset: string;
    displayName: string;
  };
  currency: {
    displayName: string;
    numericCode: number;
    currencyCode: string;
    symbol: string;
    fractionDigits: number;
  };
  dateFormat: string;
  timeFormat: string;
  domainName: string;
}

export interface TenantChecklist {
  itemId: string;
  tenantId: string;
  name: string;
  description: string;
  allowedManualUpdate: boolean;
  completed: boolean;
  updatedAt: number;
}

export interface BusinessFeature {
  code: string;
  name: string;
  icon: string;
  groupName: string;
  description: string;
}

export interface SubmitBusinessFeature {
  code: string;
  businessFeatureId?: string;
  enabled: boolean;
}

export interface TenantBusinessFeature {
  id: string;
  tenantId: string;
  name: string;
  code: string;
  description: string;
  enabled: boolean;
}
export interface TenantDomainRequest {
  domain: string;
}

export interface TenantDomainResponse {
  available: boolean;
}
