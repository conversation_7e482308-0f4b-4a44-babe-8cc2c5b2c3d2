<div class="flex flex-col w-full">
  <div class="page-header">
    <div class="page-header__wrapper">
      <div class="page-header__breadcrumb">
        <div class="page-header__breadcrumb__parent">
          {{ 'service-charge.management' | transloco }}
        </div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">
          {{ 'service-charge.information' | transloco }}
        </div>
      </div>

      <div class="page-header__title">
        <button type="button" (click)="goBack()">
          <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
        </button>
        <div>
          {{ 'service-charge.information' | transloco }}
        </div>
        <fuse-help-link [url]="''"></fuse-help-link>
      </div>
    </div>

    <div class="page-header__action">
      @if (permissionList.update) {
        <button
          type="button"
          class="btn-soft__primary__medium"
          (click)="gotoUpdateServiceCharge()"
        >
          <mat-icon class="w-5 h-5" [svgIcon]="'styl:Pencil'"></mat-icon>
          <span>{{ 'common.action.edit' | transloco }}</span>
        </button>
      }
    </div>
  </div>

  <div class="flex flex-col w-full gap-4 p-4">
    <!-- GENERAL INFORMATION -->
    <div class="box">
      <div class="box__header__title">
        {{ 'common.general-information' | transloco }}
      </div>

      <div class="grid sm:grid-cols-2 grid-cols-1 sm:gap-4 gap-2">
        <div class="card">
          <span class="card__label">
            {{ 'service-charge.name' | transloco }}
          </span>
          <p>
            {{ (serviceChargeDetail && serviceChargeDetail.name) || 'N/A' }}
          </p>
        </div>

        <div class="card">
          <span class="card__label">
            {{ 'service-charge.id' | transloco }}
          </span>
          <p>
            {{ (serviceChargeDetail && serviceChargeDetail.id) || 'N/A' }}
          </p>
        </div>

        <div class="card">
          <span class="card__label">
            {{ 'common.status.main' | transloco }}
          </span>
          <div
            class="px-2 py-1 rounded-3 w-fit"
            [ngClass]="
              serviceChargeDetail && serviceChargeDetail.isActive
                ? 'bg-[#36B37E] text-white'
                : 'bg-[#DFE3E8] text-black'
            "
          >
            {{
              serviceChargeDetail && serviceChargeDetail.isActive
                ? 'Active'
                : 'Inactive'
            }}
          </div>
        </div>

        <div class="card">
          <span class="card__label">
            {{ 'service-charge.charge-amount' | transloco }}
          </span>
          <p>
            {{
              serviceChargeDetail &&
              !utils.isNullOrEmpty(serviceChargeDetail.chargeFixedAmount)
                ? utils.formatPrice(
                    serviceChargeDetail.chargeFixedAmount,
                    tenantCurrency
                  )
                : 'N/A'
            }}
          </p>
        </div>

        <div class="card">
          <span class="card__label">
            {{ 'service-charge.charge-rate' | transloco }}</span
          >
          <p>
            {{
              serviceChargeDetail &&
              !utils.isNullOrEmpty(serviceChargeDetail.chargeRate)
                ? (serviceChargeDetail.chargeRate * 100).toFixed(2) + '%'
                : 'N/A'
            }}
          </p>
        </div>

        <div class="card">
          <span class="card__label">
            {{ 'common.creation-date' | transloco }}
          </span>
          <p>
            {{
              (serviceChargeDetail && serviceChargeDetail.createdAt
                | date: 'dd/MM/yyyy') || 'N/A'
            }}
          </p>
        </div>

        <div class="card">
          <span class="card__label">
            {{ 'common.last-updated-date' | transloco }}
          </span>
          <p>
            {{
              (serviceChargeDetail && serviceChargeDetail.updatedAt
                | date: 'dd/MM/yyyy') || 'N/A'
            }}
          </p>
        </div>
      </div>

      <div class="grid w-full grid-cols-1 gap-4">
        <div class="card">
          <span class="card__label">
            {{ 'service-charge.platform-order-type' | transloco }}
          </span>

          @if (serviceChargeDetail && serviceChargeDetail.platformOrderTypes) {
            <div class="flex flex-wrap sm:gap-1 gap-2 mt-2">
              @for (
                type of serviceChargeDetail.platformOrderTypes;
                track type
              ) {
                <div class="primary-card">
                  {{ type }}
                </div>
              }
            </div>
          } @else {
            <div>N/A</div>
          }
        </div>

        <div class="card">
          <span class="card__label">
            {{ 'common.description' | transloco }}
          </span>
          <div class="break-words">
            {{
              (serviceChargeDetail && serviceChargeDetail.description) || 'N/A'
            }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
