import { Routes } from '@angular/router';
import { ROUTE } from '../../../core/const';
import { ServiceChargeComponent } from './service-charge.component';
import { AddServiceChargeComponent } from './add-service-charge/add-service-charge.component';
import { ServiceChargeDetailComponent } from './service-charge-detail/service-charge-detail.component';
export default [
  {
    path: ROUTE.SERVICE_CHARGE.LIST,
    component: ServiceChargeComponent,
  },
  {
    path: ROUTE.SERVICE_CHARGE.ADD,
    component: AddServiceChargeComponent,
  },
  {
    path: `${ROUTE.SERVICE_CHARGE.EDIT}/:id`,
    component: AddServiceChargeComponent,
  },
  {
    path: `${ROUTE.SERVICE_CHARGE.DETAIL}/:id`,
    component: ServiceChargeDetailComponent,
  },
  { path: '', pathMatch: 'full', redirectTo: ROUTE.SERVICE_CHARGE.LIST },
] as Routes;
