import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PreOrderMenuItemCalendarComponent } from './pre-order-menu-item-calendar.component';
import { provideIcons } from '../../../../../core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import { provideTranslate } from '../../../../../core/transloco/transloco.provider';

describe('PreOrderMenuItemCalendarComponent', () => {
  let component: PreOrderMenuItemCalendarComponent;
  let fixture: ComponentFixture<PreOrderMenuItemCalendarComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PreOrderMenuItemCalendarComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({}),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(PreOrderMenuItemCalendarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
