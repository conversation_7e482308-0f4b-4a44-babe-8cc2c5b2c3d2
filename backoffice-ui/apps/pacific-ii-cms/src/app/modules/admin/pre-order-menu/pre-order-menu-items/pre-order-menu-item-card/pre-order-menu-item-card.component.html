<div class="w-full">
  <div
    class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-2.5 px-5 py-2 bg-[#c6daff]"
  >
    <p
      class="flex-grow-0 flex-shrink-0 text-2xl font-bold text-left text-black"
    >
      {{ menuItems.title }}
    </p>
    <div
      class="flex flex-col justify-center items-center flex-grow-0 flex-shrink-0 h-[22px] w-[30px] relative gap-2.5 p-2.5 rounded-[30px] border border-white"
    >
      <p
        class="flex-grow-0 flex-shrink-0 text-xs font-bold text-left text-[#161c24]"
      >
        {{ menuItems?.items?.length }}
      </p>
    </div>
  </div>

  <div
    class="grid w-full gap-4 px-5 py-4 grid-cols-[repeat(auto-fill,_minmax(262px,_1fr))]"
  >
    @for (item of menuItems.items; track item) {
      <div
        class="flex flex-col justify-between items-start flex-grow-0 flex-shrink-0 gap-2 p-4 rounded-[10px] bg-white border-2 border-[#f4f6f8]"
        [ngClass]="{ 'bg-[#dfe3e8]': !item.isEditable }"
        style="box-shadow: 0px 0px 20px 0 rgba(0, 0, 0, 0.06)"
      >
        <div
          class="flex flex-col items-start justify-start flex-grow-0 flex-shrink-0 gap-2"
        >
          <div
            class="flex items-start self-stretch justify-start flex-grow-0 flex-shrink-0 gap-2"
          >
            <div
              class="relative flex flex-col items-start justify-start flex-grow gap-1"
            >
              <p
                class="self-stretch flex-grow-0 flex-shrink-0 text-base font-semibold text-left text-[#212b36]"
              >
                {{ item?.product?.name ? item.product.name : '' }}
              </p>

              @if (item?.product?.category?.name) {
                <div class="menu-item-card__product__category">
                  {{ item.product.category.name }}
                </div>
              }

              <p
                class="self-stretch flex-grow-0 flex-shrink-0 text-sm text-left text-[#454f5b]"
              >
                {{ item?.product?.description ? item.product.description : '' }}
              </p>
            </div>
          </div>

          <div
            class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-2.5"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              class="relative flex-grow-0 flex-shrink-0 w-6 h-6"
              preserveAspectRatio="xMidYMid meet"
            >
              <path
                opacity="0.2"
                d="M21.7504 6.75L19.3476 15.4012C19.2601 15.7165 19.0719 15.9944 18.8118 16.1928C18.5517 16.3912 18.2338 16.499 17.9067 16.5H8.64043C8.31265 16.4999 7.99394 16.3924 7.73304 16.1939C7.47214 15.9955 7.28342 15.7171 7.19574 15.4012L4.79199 6.75H21.7504Z"
                fill="#637381"
              ></path>
              <path
                d="M9.75 20.25C9.75 20.5467 9.66203 20.8367 9.4972 21.0834C9.33238 21.33 9.09811 21.5223 8.82403 21.6358C8.54994 21.7494 8.24834 21.7791 7.95736 21.7212C7.66639 21.6633 7.39912 21.5204 7.18934 21.3107C6.97956 21.1009 6.8367 20.8336 6.77882 20.5426C6.72094 20.2517 6.75065 19.9501 6.86418 19.676C6.97771 19.4019 7.16997 19.1676 7.41665 19.0028C7.66332 18.838 7.95333 18.75 8.25 18.75C8.64782 18.75 9.02936 18.908 9.31066 19.1893C9.59196 19.4706 9.75 19.8522 9.75 20.25ZM18 18.75C17.7033 18.75 17.4133 18.838 17.1666 19.0028C16.92 19.1676 16.7277 19.4019 16.6142 19.676C16.5006 19.9501 16.4709 20.2517 16.5288 20.5426C16.5867 20.8336 16.7296 21.1009 16.9393 21.3107C17.1491 21.5204 17.4164 21.6633 17.7074 21.7212C17.9983 21.7791 18.2999 21.7494 18.574 21.6358C18.8481 21.5223 19.0824 21.33 19.2472 21.0834C19.412 20.8367 19.5 20.5467 19.5 20.25C19.5 19.8522 19.342 19.4706 19.0607 19.1893C18.7794 18.908 18.3978 18.75 18 18.75ZM22.4728 6.95062L20.0691 15.6019C19.9369 16.0745 19.6542 16.4911 19.2639 16.7885C18.8736 17.0859 18.397 17.2479 17.9062 17.25H8.64C8.14784 17.2498 7.66926 17.0886 7.27725 16.791C6.88523 16.4935 6.6013 16.0758 6.46875 15.6019L3.18 3.75H1.5C1.30109 3.75 1.11032 3.67098 0.96967 3.53033C0.829018 3.38968 0.75 3.19891 0.75 3C0.75 2.80109 0.829018 2.61032 0.96967 2.46967C1.11032 2.32902 1.30109 2.25 1.5 2.25H3.75C3.91397 2.24997 4.07343 2.30367 4.20398 2.40289C4.33452 2.50211 4.42895 2.64138 4.47281 2.79938L5.36156 6H21.75C21.8656 5.99998 21.9797 6.02669 22.0833 6.07805C22.1869 6.1294 22.2772 6.20401 22.3472 6.29605C22.4171 6.38809 22.4649 6.49506 22.4867 6.60861C22.5085 6.72216 22.5037 6.83922 22.4728 6.95062ZM20.7628 7.5H5.77875L7.91719 15.2006C7.96105 15.3586 8.05548 15.4979 8.18602 15.5971C8.31657 15.6963 8.47603 15.75 8.64 15.75H17.9062C18.0702 15.75 18.2297 15.6963 18.3602 15.5971C18.4908 15.4979 18.5852 15.3586 18.6291 15.2006L20.7628 7.5Z"
                fill="#637381"
              ></path>
            </svg>
            <p
              class="flex-grow-0 flex-shrink-0 text-base text-left text-[#637381]"
            >
              {{
                item.ordered +
                  (item && item.capacity ? '/' + item?.capacity : '') +
                  ' ordered'
              }}
            </p>
          </div>
        </div>

        <div
          class="relative flex items-end self-stretch justify-between flex-grow-0 flex-shrink-0"
        >
          <div
            class="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 w-[101px] h-[52px] absolute left-0 top-0 gap-1"
          ></div>
          <div
            class="relative flex items-center justify-start flex-grow-0 flex-shrink-0 gap-2"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-2xl font-bold text-left text-[#165adc]"
            >
              {{
                formatPriceValue(item.product.unitPrice, item.product.currency)
              }}
            </p>
            @if (item?.product?.listingPrice) {
              <p
                class="flex-grow-0 flex-shrink-0 text-xl font-bold text-left text-[#919eab] line-through"
              >
                {{
                  formatPriceValue(
                    item.product.listingPrice,
                    item.product.currency
                  )
                }}
              </p>
            }
          </div>
          <div class="flex items-center justify-center">
            @if (
              item.product &&
              item.product.status &&
              item.product.status === productStatus.ARCHIVED
            ) {
              <mat-icon
                class="cursor-pointer text-warning-main"
                [svgIcon]="'styl:WarningBold'"
                [matTooltip]="productWarning"
              ></mat-icon>
            }

            @if (actions && actions.length > 0) {
              <button
                type="button"
                class="flex items-center justify-center w-6 h-6"
                mat-icon-button
                [matMenuTriggerFor]="menu"
                aria-label="Actions menu"
              >
                <mat-icon>more_vert</mat-icon>
              </button>
            }
            <mat-menu #menu="matMenu">
              @for (action of actions; track action) {
                @if (action.type === 'event' && action.callback) {
                  <button
                    type="button"
                    mat-menu-item
                    (click)="action.callback(item)"
                    [ngClass]="{
                      hidden: action?.hidden && action?.hidden(item),
                    }"
                  >
                    {{ action.name }}
                  </button>
                }
              }
            </mat-menu>
          </div>
        </div>
      </div>
    }
  </div>
</div>
