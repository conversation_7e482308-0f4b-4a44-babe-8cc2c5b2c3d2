import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AddPreOrderMenuItemComponent } from './add-pre-order-menu-item.component';
import { ToastrModule } from 'ngx-toastr';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { ReactiveFormsModule } from '@angular/forms';
import { provideIcons } from '../../../../../core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideTranslate } from '../../../../../core/transloco/transloco.provider';

describe('AddPreOrderMenuItemComponent', () => {
  let component: AddPreOrderMenuItemComponent;
  let fixture: ComponentFixture<AddPreOrderMenuItemComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        AddPreOrderMenuItemComponent,
        MatDialogModule,
        ReactiveFormsModule,
        ToastrModule.forRoot(),
      ],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: MatDialogRef,
          useValue: {},
        },
        { provide: MAT_DIALOG_DATA, useValue: { data: {} } },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AddPreOrderMenuItemComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
