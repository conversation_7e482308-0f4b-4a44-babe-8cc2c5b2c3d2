<div class="flex flex-col w-full">
  <div class="page-header">
    <div class="page-header__wrapper">
      <div class="page-header__breadcrumb">
        <div class="page-header__breadcrumb__parent">Pre-order Menu</div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">
          {{ preOrderMenuDetail ? preOrderMenuDetail.name : '' }}
        </div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">Pre-order Menu Items</div>
      </div>

      <div class="page-header__title">
        <button type="button" (click)="goBack()">
          <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
        </button>
        <div>Pre-order Menu Items</div>
        <fuse-help-link
          [url]="
            '/pre-order-menu/pre-order-management/#view-and-edit-pre-order-menu'
          "
        ></fuse-help-link>
      </div>
    </div>

    <div class="page-header__action">
      <button
        type="button"
        class="flex items-center justify-center gap-1 px-3 py-2 h-fit rounded-3 shadow-3xl bg-[#C6DAFF] border-[1px] border-solid border-primary"
      >
        <mat-icon
          class="w-3 h-3"
          [svgIcon]="'heroicons_outline:calendar'"
        ></mat-icon>
        <span class="web__body2">Calendar</span>
      </button>

      <button
        type="button"
        class="flex items-center justify-center gap-1 px-3 py-2 h-fit rounded-3 shadow-3xl"
        (click)="gotoPreOrderMenuItemList()"
      >
        <mat-icon
          class="w-3 h-3"
          [svgIcon]="'heroicons_outline:queue-list'"
        ></mat-icon>
        <span class="web__body2 text-nowrap">List Items</span>
      </button>

      @if (permissionList.update) {
        <button
          type="button"
          class="btn-contained__primary__medium"
          (click)="onOpenAddPreOrderMenuItemDialog()"
        >
          Add Item
        </button>
      }
    </div>
  </div>

  <div class="box !gap-0 p-0 m-4 box flex-auto w-auto">
    <div class="w-full">
      <fuse-calendar
        (selectedDateChange)="onSelectedDateChange($event)"
      ></fuse-calendar>
    </div>

    @if (mealTimeItems && mealTimeItems.length > 0) {
      @for (mealTimeItem of mealTimeItems; track mealTimeItem) {
        <app-pre-order-menu-item-card
          [menuItems]="mealTimeItem"
          [actions]="actionShow ? actions : []"
        ></app-pre-order-menu-item-card>
      }
    } @else {
      <div class="w-full h-[400px] relative">
        <fuse-error-no-data
          subText="No data have been created yet. Click on add button to create new data"
        />
      </div>
    }
  </div>
</div>
