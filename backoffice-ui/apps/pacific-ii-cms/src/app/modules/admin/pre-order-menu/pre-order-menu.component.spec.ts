import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PreOrderMenuComponent } from './pre-order-menu.component';
import { provideHttpClient } from '@angular/common/http';
import { MatDialogModule } from '@angular/material/dialog';
import { ReactiveFormsModule } from '@angular/forms';
import { ToastrModule } from 'ngx-toastr';
import { provideIcons } from '../../../core/icons/icons.provider';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';
import { of } from 'rxjs';
import { provideTranslate } from '../../../core/transloco/transloco.provider';

describe('PreOrderMenuComponent', () => {
  let component: PreOrderMenuComponent;
  let fixture: ComponentFixture<PreOrderMenuComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        PreOrderMenuComponent,
        MatDialogModule,
        ReactiveFormsModule,
        ToastrModule.forRoot(),
      ],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({
              params: {},
            }),
          },
        },
        {
          provide: KeycloakService,
          useValue: {},
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(PreOrderMenuComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
