import { IProduct } from '../../product-management/product.types';

export interface IPreOrderMenuItemResponse {
  content: IPreOrderMenuItem[];
  totalElements: number;
  totalPages: number;
  page: number;
  sort: string[];
}

export interface IPreOrderMenuItem {
  id: string;
  preOrderMenuId: string;
  chainId: string;
  mealTimeId: string;
  product: IProduct;
  date: string;
  status: string;
  capacity: number;
  ordered: number;
  createdAt: number;
  updatedAt: number;
}

export interface IPreOrderMenuItemFilter {
  filter?: {
    name?: string;
    mealTimeId?: string;
    categoryId?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
  };
  size?: number;
  page?: number;
  sortDirection?: string;
  sortFields?: string;
}
