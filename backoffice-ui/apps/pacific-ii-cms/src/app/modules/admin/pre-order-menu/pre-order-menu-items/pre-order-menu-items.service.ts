import { inject, Injectable } from '@angular/core';
import { EMPTY, expand, Observable, reduce, ReplaySubject, tap } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { API } from '../../../../core/const';
import {
  IPreOrderMenuItem,
  IPreOrderMenuItemFilter,
  IPreOrderMenuItemResponse,
} from './pre-order-menu-items.types';
import { FuseUltils } from '../../../../../../../../libs/fuse/src/lib/ultils';

@Injectable({ providedIn: 'root' })
export class PreOrderMenuItemsService {
  private _httpClient = inject(HttpClient);

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Get order item data by pagination
   */
  getPreOrderMenuItems(menuId: string, queryParams: any = {}): Observable<any> {
    return this._httpClient.get<any>(
      `${API.PRE_ORDER_MENU.ITEMS.LIST.replace('{menuId}', menuId)}?${queryParams}`,
    );
  }

  /**
   * Get all order items data
   */
  getAllPreOrderMenuItems(
    menuId: string,
    queryParams: IPreOrderMenuItemFilter,
  ): Observable<any> {
    let initialPage = 0;
    queryParams.page = initialPage;

    return this.getPreOrderMenuItems(
      menuId,
      FuseUltils.objectToQueryString(queryParams),
    ).pipe(
      expand((response) => {
        const nextPage = initialPage + 1;

        if (nextPage >= response.totalPages) {
          return EMPTY;
        }

        initialPage = nextPage;
        queryParams.page = initialPage;
        return this.getPreOrderMenuItems(
          menuId,
          FuseUltils.objectToQueryString(queryParams),
        );
      }),
      reduce((acc: IPreOrderMenuItem[], current: IPreOrderMenuItemResponse) => {
        return [...acc, ...current.content];
      }, [] as IPreOrderMenuItem[]),
    );
  }

  /**
   * Get order item detail
   */
  getPreOrderMenuItemDetail(menuId: string, id: string): Observable<any> {
    return this._httpClient.get<any>(
      API.PRE_ORDER_MENU.ITEMS.DETAIL.replace('{menuId}', menuId).replace(
        '{id}',
        id,
      ),
    );
  }

  /**
   * Add new order item
   */
  addPreOrderMenuItem(menuId: string, data: any): Observable<any> {
    return this._httpClient.post<any>(
      API.PRE_ORDER_MENU.ITEMS.ADD.replace('{menuId}', menuId),
      data,
    );
  }

  /**
   * Update order item
   */
  updatePreOrderMenuItem(
    menuId: string,
    id: string,
    data: any,
  ): Observable<any> {
    return this._httpClient.put<any>(
      API.PRE_ORDER_MENU.ITEMS.UPDATE.replace('{menuId}', menuId).replace(
        '{id}',
        id,
      ),
      data,
    );
  }

  /**
   * Delete order item
   */
  deletePreOrderMenuItem(
    menuId: string,
    id: string,
    following: boolean,
  ): Observable<any> {
    return this._httpClient.delete<any>(
      API.PRE_ORDER_MENU.ITEMS.DELETE.replace('{menuId}', menuId).replace(
        '{id}',
        id,
      ) + `?following=${following}`,
    );
  }
}
