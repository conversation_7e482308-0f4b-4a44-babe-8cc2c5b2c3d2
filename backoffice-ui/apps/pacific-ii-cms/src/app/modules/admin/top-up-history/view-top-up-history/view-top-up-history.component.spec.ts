import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ViewTopUpHistoryComponent } from './view-top-up-history.component';
import { IDataResponse } from '../../source-of-fund/source-of-fund.types';
import { ITopUpHistoryRecord } from '../top-up-history.types';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideHttpClient } from '@angular/common/http';
import { ICustomer } from '../../customer-management/customer.types';
import { CustomerManagementService } from '../../customer-management/customer-management.service';
import { of } from 'rxjs';
import { TopUpHistoryService } from '../top-up-history.service';
import { ActivatedRoute } from '@angular/router';
import { FuseUltils } from '../../../../../../../../libs/fuse/src/lib/ultils';
import { ROUTE } from '../../../../core/const';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';
import { NavigationHistoryService } from '../../../../core/services/navigation-history.service';
import { ToastrModule } from 'ngx-toastr';

describe('ViewTopUpHistoryComponent', () => {
  let component: ViewTopUpHistoryComponent;
  let fixture: ComponentFixture<ViewTopUpHistoryComponent>;

  const mockHistoryId = 'test';

  const mockHistoryRecord = {
    id: 'string',
    tenantId: 'string',
    customerId: 'string',
    currency: {
      displayName: 'US Dollar',
      numericCode: 840,
      currencyCode: 'USD',
      symbol: '$',
      fractionDigits: 2,
    },
    amount: 0,
    fundSourceTopupHistoryId: 'string',
    fundSourceTopupSchedulerId: 'string',
    walletTransactionId: 'string',
    createdAt: 123,
  };

  const mockHistoryRecordResponse: IDataResponse<ITopUpHistoryRecord> = {
    content: [mockHistoryRecord],
    totalElements: 1,
    totalPages: 1,
    page: 0,
    sort: [],
  };

  const mockQueryParams = {
    id: mockHistoryRecord.id,
    customerId: mockHistoryRecord.customerId,
    fundSourceTopupSchedulerId: mockHistoryRecord.fundSourceTopupSchedulerId,
    fundSourceTopupHistoryId: mockHistoryId,
    walletTransactionId: mockHistoryRecord.walletTransactionId,
    fromTime: mockHistoryRecord.createdAt,
    toTime: mockHistoryRecord.createdAt,
    size: 10,
    page: 0,
    sortDirection: 'desc',
    sortFields: ['createdAt'],
  };

  const mockFormatQueryParams = {
    filter: {
      id: mockHistoryRecord.id,
      customerId: mockHistoryRecord.customerId,
      fundSourceTopupSchedulerId: mockHistoryRecord.fundSourceTopupSchedulerId,
      fundSourceTopupHistoryId: mockHistoryId,
      walletTransactionId: mockHistoryRecord.walletTransactionId,
      fromTime: mockHistoryRecord.createdAt,
      toTime: mockHistoryRecord.createdAt,
    },
    size: 10,
    page: 0,
    sortDirection: 'desc',
    sortFields: ['createdAt'],
  };

  const mockCustomer: any = {
    id: '125157569389419520',
    ssoId: null,
    externalId: null,
    uniqueExternalId: null,
    firstName: 'aa',
    lastName: 'aaa',
    fullName: 'aaa',
    email: '',
    userType: 'CUSTOMER',
    realmId: '********-45f9-4cc2-9946-bc00abed6203',
    avatar: null,
    userStatus: 'ACTIVE',
    phoneNumber: null,
    totalSubAccounts: null,
    totalSponsors: null,
    userGroup: {
      id: '121520479628366848',
      tenantId: '116024918514279424',
      path: '121520479628366848',
      groupName: 'Default',
      groupKey: 'DEFAULT_GROUP',
      parent: null,
      isDefaultGroup: true,
      description:
        'By default, all customer of tenant would be added into this group. ',
      createdAt: null,
      updatedAt: null,
    },
    userNonCompletedActions: ['KEYCLOAK_REGISTRATION'],
    permissions: [
      {
        tenantId: '116024918514279424',
        userId: '125157569389419520',
        userRoleId: '125157222424000514',
        permissionStatus: 'ACTIVE',
      },
    ],
    createdAt: *************,
    updatedAt: *************,
  };

  const mockCustomerResponse: IDataResponse<ICustomer> = {
    content: [mockCustomer],
    totalElements: 1,
    totalPages: 1,
    page: 0,
    sort: [],
  };

  const mockTableDisplayedColumns = [
    {
      key: 'id',
      name: 'ID',
      selected: true,
      sort: true,
    },
    {
      key: 'walletTransactionId',
      name: 'Wallet Transaction ID',
      selected: true,
      sort: true,
    },
    {
      key: 'customerId',
      name: 'Customer',
      selected: true,
      sort: true,
      render: expect.any(Function),
    },
    {
      key: 'fundSourceTopupSchedulerId',
      name: 'Scheduler ID',
      selected: true,
      sort: true,
    },
    {
      key: 'amount',
      name: 'Amount',
      selected: true,
      sort: true,
      headerAlign: 'right',
      renderHtml: expect.any(Function),
    },
    {
      key: 'createdAt',
      name: 'Created Date',
      selected: true,
      sort: true,
      renderHtml: expect.any(Function),
    },
  ];

  const mockHistoryService = {
    goBack: jest.fn(),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ViewTopUpHistoryComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideAnimationsAsync(),
        provideHttpClient(),
        {
          provide: CustomerManagementService,
          useValue: {
            getCustomers: jest.fn().mockReturnValue(of(mockCustomerResponse)),
          },
        },
        {
          provide: TopUpHistoryService,
          useValue: {
            getTopUpRecord: jest
              .fn()
              .mockReturnValue(of(mockHistoryRecordResponse)),
          },
        },
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of(mockQueryParams),
            paramMap: of({
              params: {
                id: mockHistoryId,
              },
            }),
          },
        },
        {
          provide: NavigationHistoryService,
          useValue: mockHistoryService,
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ViewTopUpHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    jest.spyOn(component['_changeDetectorRef'], 'detectChanges');
    jest.spyOn(component['_router'], 'navigate');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle Hook', () => {
    it('should get the queryParams and call getTopUpHistoryRecord and getCustomerLazyLoad when run ngOnInit', () => {
      jest.spyOn(component, 'getTopUpHistoryRecord');
      jest.spyOn(component, 'getCustomerLazyLoad');
      component.queryParams = null;

      component.ngOnInit();

      expect(component.queryParams).toEqual(mockQueryParams);
      expect(component.getTopUpHistoryRecord).toHaveBeenCalled();
      expect(component.getCustomerLazyLoad).toHaveBeenCalled();
    });

    it('should init the table displayed columns and table action when run ngAfterViewInit', () => {
      jest.spyOn(component['_changeDetectorRef'], 'detectChanges');
      component.displayedColumns = [];

      component.ngAfterViewInit();

      expect(component.displayedColumns).toEqual(mockTableDisplayedColumns);
      expect(component['_changeDetectorRef'].detectChanges).toHaveBeenCalled();
    });

    it('should unsubscribe the all the observable when run the ngOnDestroy ', () => {
      const unsubscribeNextSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'next',
      );
      const unsubscribeCompleteSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'complete',
      );

      component.ngOnDestroy();

      expect(unsubscribeNextSpy).toHaveBeenCalled();
      expect(unsubscribeCompleteSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    it('should use service to get the top-up history and call the getCustomer method when run getTopUpHistoryRecord', () => {
      jest.spyOn(component, 'getCustomer');

      component.historyId = mockHistoryId;
      component.queryParams = {
        id: mockFormatQueryParams.filter.id,
        customerId: mockFormatQueryParams.filter.customerId,
        fundSourceTopupSchedulerId:
          mockFormatQueryParams.filter.fundSourceTopupSchedulerId,
        fundSourceTopupHistoryId:
          mockFormatQueryParams.filter.fundSourceTopupHistoryId,
        walletTransactionId: mockFormatQueryParams.filter.walletTransactionId,
        createdAtStart: mockFormatQueryParams.filter.fromTime,
        createdAtEnd: mockFormatQueryParams.filter.toTime,
        size: mockFormatQueryParams.size,
        page: mockFormatQueryParams.page,
        sortDirection: mockFormatQueryParams.sortDirection,
        sortFields: mockFormatQueryParams.sortFields,
      };
      component.getTopUpHistoryRecord();

      expect(
        component['_topUpHistoryService'].getTopUpRecord,
      ).toHaveBeenCalledWith(
        FuseUltils.objectToQueryString(mockFormatQueryParams),
      );
      expect(component.datasource).toEqual(mockHistoryRecordResponse.content);
      expect(component.total).toBe(mockHistoryRecordResponse.totalElements);
      expect(component.getCustomer).toHaveBeenCalledWith([
        mockHistoryRecord.id,
      ]);
    });

    it('should use service to get the source of fund to update and update the render function of displayedColumn when run getCustomer', () => {
      const params = {
        filter: {
          byUserIds: [mockHistoryRecord.id],
        },
        page: 0,
        size: mockFormatQueryParams.size,
      };

      component.getCustomer([mockHistoryRecord.id]).subscribe();

      expect(component['_customerService'].getCustomers).toHaveBeenCalledWith(
        FuseUltils.objectToQueryString(params),
      );
      expect(component.customerDisplayed).toEqual(mockCustomerResponse.content);

      const columnUpdate = component.displayedColumns.find(
        (value) => value.key === 'customerId',
      );
      expect(columnUpdate).not.toEqual(null);
      expect(columnUpdate.render(mockCustomer.id)).toEqual(
        mockCustomer.fullName,
      );
      expect(columnUpdate.render('12341234')).toEqual(
        'Unknown Customer (12341234)',
      );
      expect(component['_changeDetectorRef'].detectChanges).toHaveBeenCalled();
    });

    it('should use service to get source of fund and replace it to search form field when run getCustomerLazyLoad with customerAppend is false', () => {
      component.customerAppend = false;
      const formatCustomer = {
        ...mockCustomer,
        value: mockCustomer.id,
        label: mockCustomer.fullName,
      };

      component.getCustomerLazyLoad();

      expect(component['_customerService'].getCustomers).toHaveBeenCalledWith(
        FuseUltils.objectToQueryString(component.customerParams),
      );

      expect(component.customerOptions).toEqual([formatCustomer]);
      expect(component.searchForm.basic[1].options).toEqual(
        component.customerOptions,
      );
      expect(component['_changeDetectorRef'].detectChanges).toHaveBeenCalled();
    });

    it('should use service to get source of fund and append it to search form field when run getCustomerLazyLoad with customerAppend is true', () => {
      component.customerAppend = true;
      const formatSourceOfFund = {
        ...mockCustomer,
        value: mockCustomer.id,
        label: mockCustomer.fullName,
      };
      component.customerOptions = [mockCustomer];

      component.getCustomerLazyLoad();

      expect(component['_customerService'].getCustomers).toHaveBeenCalledWith(
        FuseUltils.objectToQueryString(component.customerParams),
      );

      expect(component.customerOptions).toEqual([
        mockCustomer,
        formatSourceOfFund,
      ]);
      expect(component.searchForm.basic[1].options).toEqual(
        component.customerOptions,
      );
      expect(component['_changeDetectorRef'].detectChanges).toHaveBeenCalled();
    });
  });

  describe('Utility Function', () => {
    it('should navigate the record page when run goBack', () => {
      component.goBack();

      expect(mockHistoryService.goBack).toHaveBeenCalledWith(
        `${ROUTE.TOP_UP_HISTORY.MAIN}`,
      );
    });

    it('should update the lazy-load params for Source of Fund when run handleLazyLoadSelect', () => {
      jest.spyOn(component, 'getCustomerLazyLoad');
      component.handleLazyLoadSelect({
        value: {
          page: 1,
          search: 'test',
        },
      });

      expect(component.customerAppend).toBe(true);
      expect(component.customerParams.page).toBe(1);
      expect(component.customerParams.filter.name).toBe('test');
      expect(component.getCustomerLazyLoad).toHaveBeenCalled();
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
