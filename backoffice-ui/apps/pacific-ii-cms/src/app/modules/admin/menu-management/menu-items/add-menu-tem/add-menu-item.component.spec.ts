/* eslint-disable @typescript-eslint/no-empty-function */
import { provideHttpClient } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { FuseUltils } from '../../../../../../../../../libs/fuse/src/lib/ultils';
import { DateOfWeekList } from '../../../../../core/const';
import { provideIcons } from '../../../../../core/icons/icons.provider';
import { provideTranslate } from '../../../../../core/transloco/transloco.provider';
import { Utils } from '../../../../../core/utils/utils';
import { ProductManagementService } from '../../../product-management/product-management.service';
import { IProduct } from '../../../product-management/product.types';
import { IDataResponse } from '../../../source-of-fund/source-of-fund.types';
import { MenuItemsService } from '../menu-items.service';
import { IMenuItem, IMenuProduct } from '../menu-items.types';
import { AddMenuItemComponent } from './add-menu-item.component';

// Mock data
const mockMenuId = 'test-menu-id';

const mockProductResponse: IDataResponse<IProduct> = {
  content: [
    {
      id: '117149506383483904',
      tenantId: '116024918514279424',
      migrationId: null,
      category: {
        id: '117141350118953984',
        tenantId: '116024918514279424',
        migrationId: null,
        name: 'Fruit Juices',
        icon: {
          path: 'pacific:image/2024/10/3/JUICES.jpg',
          url: 'https://pacific.s3.ap-southeast-1.amazonaws.com/image/2024/10/3/JUICES.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250618T042250Z&X-Amz-SignedHeaders=host&X-Amz-Credential=AKIAV5QLPTQ3JPXI3LZR%2F20250618%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Expires=86400&X-Amz-Signature=f1157ab1ef1413502f7fa61b06f4d3f5608799ab8c043b812c96fe03a42e0f93',
        },
        description: 'ttt',
        parentId: null,
        createdAt: 1727928674315,
        updatedAt: 1745400004002,
      },
      productType: 'STORE_PRODUCT',
      storeId: '116037144468648960',
      healthierChoice: {
        id: '117147172366554112',
        tenantId: '116024918514279424',
        name: 'Healthier Choice',
        symbol: {
          path: 'pacific:image/2024/10/3/HC Logo-01-1.png',
          url: 'https://pacific.s3.ap-southeast-1.amazonaws.com/image/2024/10/3/HC%20Logo-01-1.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250618T042250Z&X-Amz-SignedHeaders=host&X-Amz-Credential=AKIAV5QLPTQ3JPXI3LZR%2F20250618%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Expires=86400&X-Amz-Signature=0017e1460c0ea7b6369a3f687b71ddc952f8d3d2a87bf01f02e64685e0d317b7',
        },
        description: null,
        createdAt: 1727930062384,
        updatedAt: 1727930062384,
      },
      name: 'Kiwi juice',
      sku: 'KIWIJUIC4133',
      briefInformation: null,
      description: null,
      ingredients: 'Kiwi, Honey',
      barcode: null,
      status: 'ACTIVE',
      unitPrice: 700,
      listingPrice: null,
      currency: {
        displayName: 'Singapore Dollar',
        numericCode: 702,
        currencyCode: 'SGD',
        symbol: 'SGD',
        fractionDigits: 2,
      },
      preparationTime: 10,
      images: [
        {
          id: '120100012248627200',
          position: 0,
          image: {
            path: 'pacific:image/2024/10/3/Kiwi-Juice-recipe-6.jpg',
            url: 'https://pacific.s3.ap-southeast-1.amazonaws.com/image/2024/10/3/Kiwi-Juice-recipe-6.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250618T042250Z&X-Amz-SignedHeaders=host&X-Amz-Credential=AKIAV5QLPTQ3JPXI3LZR%2F20250618%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Expires=86400&X-Amz-Signature=dd29b44bd84fbf572deb16f055ee672e33d539b9b7aa3e0083d78dad3e7f8952',
          },
          createdAt: 1728634074275,
          updatedAt: 1728634074275,
        },
      ],
      options: [],
      nutrition: [],
      allergens: [],
      keywords: null,
      createdAt: 1727930618872,
      updatedAt: 1728634074276,
      openPrice: false,
    },
    {
      id: '117501403689391104',
      tenantId: '116024918514279424',
      migrationId: null,
      category: {
        id: '117542367745283072',
        tenantId: '116024918514279424',
        migrationId: null,
        name: 'Italian Coffee',
        icon: {
          path: 'pacific:image/2024/10/4/coffee-brain-caffeine-neuroscincces.jpg',
          url: 'https://pacific.s3.ap-southeast-1.amazonaws.com/image/2024/10/4/coffee-brain-caffeine-neuroscincces.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250618T042250Z&X-Amz-SignedHeaders=host&X-Amz-Credential=AKIAV5QLPTQ3JPXI3LZR%2F20250618%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Expires=86400&X-Amz-Signature=2781e30a1663685eef7cc4d00a19c409c70a514dc79ad75921da0a59bf73e822',
        },
        description: 'ttt',
        parentId: '117144473499702272',
        createdAt: 1728024284307,
        updatedAt: 1745399882317,
      },
      productType: 'STORE_PRODUCT',
      storeId: '116037144468648960',
      healthierChoice: null,
      name: 'Coffee Vietnam',
      sku: 'COFFEEVI1487',
      briefInformation: null,
      description: null,
      ingredients: null,
      barcode: null,
      status: 'ACTIVE',
      unitPrice: 300,
      listingPrice: null,
      currency: {
        displayName: 'Singapore Dollar',
        numericCode: 702,
        currencyCode: 'SGD',
        symbol: 'SGD',
        fractionDigits: 2,
      },
      preparationTime: 15,
      images: [
        {
          id: '168218973128637440',
          position: 0,
          image: {
            path: 'pacific:image/2024/10/4/Ca phe phin viet nam.jpg',
            url: 'https://pacific.s3.ap-southeast-1.amazonaws.com/image/2024/10/4/Ca%20phe%20phin%20viet%20nam.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250618T042250Z&X-Amz-SignedHeaders=host&X-Amz-Credential=AKIAV5QLPTQ3JPXI3LZR%2F20250618%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Expires=86400&X-Amz-Signature=3b98e6ae171a572f24434410b275b1a6aa2421b6eaadfd7eaeb8a830633a2307',
          },
          createdAt: 1740106528561,
          updatedAt: 1740106528561,
        },
      ],
      options: [
        {
          id: '117502170101978112',
          title: 'Sweetie',
          productId: '117501403689391104',
          minimum: 1,
          maximum: 1,
          items: [
            {
              id: '117502561292129280',
              optionId: '117502170101978112',
              name: 'Without Sugar',
              additionPrice: 0,
              active: true,
              createdAt: 1728014793710,
              updatedAt: 1728014793710,
            },
            {
              id: '117502561292129281',
              optionId: '117502170101978112',
              name: 'Less Sugar',
              additionPrice: 0,
              active: true,
              createdAt: 1728014793713,
              updatedAt: 1728014793713,
            },
            {
              id: '117502561292129282',
              optionId: '117502170101978112',
              name: 'Normal Sugar',
              additionPrice: 0,
              active: true,
              createdAt: 1728014793715,
              updatedAt: 1728014793715,
            },
            {
              id: '117502561292129283',
              optionId: '117502170101978112',
              name: 'More Sugar',
              additionPrice: 1,
              active: true,
              createdAt: 1728014793716,
              updatedAt: 1728014793716,
            },
          ],
          createdAt: 1728014700448,
          updatedAt: 1728014793717,
        },
      ],
      nutrition: [],
      allergens: [],
      keywords: null,
      createdAt: 1728014517718,
      updatedAt: 1740106528631,
      openPrice: false,
    },
    {
      id: '117504727008416768',
      tenantId: '116024918514279424',
      migrationId: null,
      category: {
        id: '117141350118953984',
        tenantId: '116024918514279424',
        migrationId: null,
        name: 'Fruit Juices',
        icon: {
          path: 'pacific:image/2024/10/3/JUICES.jpg',
          url: 'https://pacific.s3.ap-southeast-1.amazonaws.com/image/2024/10/3/JUICES.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250618T042250Z&X-Amz-SignedHeaders=host&X-Amz-Credential=AKIAV5QLPTQ3JPXI3LZR%2F20250618%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Expires=86400&X-Amz-Signature=f1157ab1ef1413502f7fa61b06f4d3f5608799ab8c043b812c96fe03a42e0f93',
        },
        description: 'ttt',
        parentId: null,
        createdAt: 1727928674315,
        updatedAt: 1745400004002,
      },
      productType: 'STORE_PRODUCT',
      storeId: '116037144468648960',
      healthierChoice: {
        id: '117147172366554112',
        tenantId: '116024918514279424',
        name: 'Healthier Choice',
        symbol: {
          path: 'pacific:image/2024/10/3/HC Logo-01-1.png',
          url: 'https://pacific.s3.ap-southeast-1.amazonaws.com/image/2024/10/3/HC%20Logo-01-1.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250618T042250Z&X-Amz-SignedHeaders=host&X-Amz-Credential=AKIAV5QLPTQ3JPXI3LZR%2F20250618%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Expires=86400&X-Amz-Signature=0017e1460c0ea7b6369a3f687b71ddc952f8d3d2a87bf01f02e64685e0d317b7',
        },
        description: null,
        createdAt: 1727930062384,
        updatedAt: 1727930062384,
      },
      name: 'Orange juice',
      sku: 'ORANGEJU1837',
      briefInformation: null,
      description: null,
      ingredients: null,
      barcode: null,
      status: 'ACTIVE',
      unitPrice: 400,
      listingPrice: null,
      currency: {
        displayName: 'Singapore Dollar',
        numericCode: 702,
        currencyCode: 'SGD',
        symbol: 'SGD',
        fractionDigits: 2,
      },
      preparationTime: 5,
      images: [
        {
          id: '120099826231244800',
          position: 0,
          image: {
            path: 'pacific:image/2024/10/4/orange-juice.jpg',
            url: 'https://pacific.s3.ap-southeast-1.amazonaws.com/image/2024/10/4/orange-juice.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250618T042250Z&X-Amz-SignedHeaders=host&X-Amz-Credential=AKIAV5QLPTQ3JPXI3LZR%2F20250618%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Expires=86400&X-Amz-Signature=fde6e4343cf25124160f4a9a0454cc0bd1f6dce008a6875a171ea428c0e98a85',
          },
          createdAt: 1728634029926,
          updatedAt: 1728634029926,
        },
      ],
      options: [
        {
          id: '117505021897348096',
          title: 'Sweetie',
          productId: '117504727008416768',
          minimum: 1,
          maximum: 2,
          items: [
            {
              id: '127387972933846016',
              optionId: '117505021897348096',
              name: 'No sweetie',
              additionPrice: 0,
              active: true,
              createdAt: 1730371659504,
              updatedAt: 1730371659504,
            },
            {
              id: '127387972933846017',
              optionId: '117505021897348096',
              name: 'Sugar',
              additionPrice: 0,
              active: true,
              createdAt: 1730371659505,
              updatedAt: 1730371659505,
            },
            {
              id: '127387972933846018',
              optionId: '117505021897348096',
              name: 'Honey',
              additionPrice: 1,
              active: true,
              createdAt: 1730371659505,
              updatedAt: 1730371659505,
            },
          ],
          createdAt: 1728015380362,
          updatedAt: 1730371659506,
        },
      ],
      nutrition: [],
      allergens: [],
      keywords: null,
      createdAt: 1728015310057,
      updatedAt: 1728634029927,
      openPrice: false,
    },
  ],
  totalElements: 3,
  totalPages: 1,
  page: 0,
  sort: [],
};

const mockProductItem = {
  ...mockProductResponse.content[0],
  inventory: {
    tracking: false,
    quantity: 80,
    minimumQuantityOrder: 1,
    maximumQuantityOrder: 4,
    step: 5,
    createdAt: 1728268923081,
    updatedAt: 1732518972201,
  },
};

const mockMenuItem: IMenuItem = {
  id: '117599518592788480',
  menuId: '117548429660576768',
  position: 20,
  product: mockProductItem,
  arrangement: {
    id: '117599518592788480',
    startDate: 1727974800000,
    endDate: 1732899600000,
    startTime: '07:00:00',
    endTime: '07:30:00',
    availableOn: ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY'],
    createdAt: 1728037910137,
    updatedAt: 1740128513254,
  },
  createdAt: 1728037910134,
  updatedAt: 1728037910134,
};

const mockMenuItemResponse: IDataResponse<IMenuItem> = {
  content: [
    {
      id: '120100789433915392',
      menuId: '117548429660576768',
      position: 40,
      product: mockProductItem,
      arrangement: {
        id: '120100789433915392',
        startDate: 1729616400000,
        endDate: 1735578000000,
        startTime: '06:00:00',
        endTime: '15:00:00',
        availableOn: ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY'],
        createdAt: 1728634259604,
        updatedAt: 1733120889229,
      },
      createdAt: 1728634259595,
      updatedAt: 1728634259595,
    },
  ],
  totalElements: 1,
  totalPages: 1,
  page: 0,
  sort: [],
};

const mockFormValue = {
  productId: [mockProductResponse.content[0].id],
  position: 1,
  availableDate: {
    start: '1733158800000',
    end: '1735578000000',
  },
  availableTime: {
    start: '57600000',
    end: '3600000',
  },
  availableOn: ['MONDAY'],
};

const mockFormatFormValue = {
  position: 1,
  arrangement: {
    startDate: mockFormValue.availableDate.start,
    endDate: mockFormValue.availableDate.end,
    startTime: '08:00:00',
    endTime: '23:00:00',
    availableOn: ['MONDAY'],
  },
};

const mockAddItem = {
  ...mockFormatFormValue,
  productId: mockFormValue.productId[0],
  productName: `${mockProductResponse.content[0].name} (${mockProductResponse.content[0].sku})`,
  status: null,
  availableDate: '03/12/2024 - 31/12/2024',
  availableTime: '08:00:00 - 23:00:00',
  availableOnString: 'Every Monday',
};

// Mock services
const mockToastrService = {
  success: jest.fn(),
};

const mockMatDialogRef = {
  close: jest.fn(),
};

const mockMenuItemsService = {
  getMenuItems: jest.fn().mockReturnValue(of(mockMenuItemResponse)),
  addMenuItem: jest.fn().mockReturnValue(of(null)),
  updateMenuItem: jest.fn().mockReturnValue(of(null)),
};

const mockProductManagementService = {
  getProducts: jest.fn().mockReturnValue(of(mockProductResponse)),
};

describe('AddMenuItemComponent', () => {
  let component: AddMenuItemComponent;
  let fixture: ComponentFixture<AddMenuItemComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        AddMenuItemComponent,
        MatDialogModule,
        ReactiveFormsModule,
        ToastrModule.forRoot(),
      ],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        { provide: MatDialogRef, useValue: mockMatDialogRef },
        { provide: MAT_DIALOG_DATA, useValue: { data: {} } },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: MenuItemsService, useValue: mockMenuItemsService },
        {
          provide: ProductManagementService,
          useValue: mockProductManagementService,
        },
        {
          provide: MAT_DIALOG_DATA,
          useValue: { menuId: mockMenuId },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AddMenuItemComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle Hooks', () => {
    describe('There is detail in data', () => {
      let testComponent: AddMenuItemComponent;
      beforeEach(() => {
        testComponent =
          TestBed.createComponent(AddMenuItemComponent).componentInstance;
        testComponent.data = {
          detail: mockMenuItem,
          menuId: mockMenuId,
        };
        fixture.detectChanges();
      });

      it('should not initialize mode and products when there is detail in data', () => {
        const handleGetProductsSpy = jest.spyOn(
          testComponent,
          'handleGetProducts',
        );
        testComponent.ngOnInit();
        expect(testComponent.mode).toBe('add');
        expect(handleGetProductsSpy).not.toHaveBeenCalled();
      });

      it('should update form data when there is detail in data', () => {
        const updateFormDataSpy = jest.spyOn(testComponent, 'updateFormData');
        const detectChangesSpy = jest.spyOn(
          testComponent['_changeDetectorRef'],
          'detectChanges',
        );
        testComponent.ngAfterViewInit();
        expect(updateFormDataSpy).toHaveBeenCalled();
        expect(detectChangesSpy).toHaveBeenCalled();
      });
    });

    it('should initialize mode and products when there is no detail in data', () => {
      component.data.detail = null;
      const handleGetProductsSpy = jest.spyOn(component, 'handleGetProducts');
      component.ngOnInit();
      expect(component.mode).toBe('edit');
      expect(handleGetProductsSpy).toHaveBeenCalled();
    });

    it('should not update form data when there is no detail in data', () => {
      component.data.detail = null;
      const updateFormDataSpy = jest.spyOn(component, 'updateFormData');
      const detectChangesSpy = jest.spyOn(
        component['_changeDetectorRef'],
        'detectChanges',
      );
      component.ngAfterViewInit();
      expect(updateFormDataSpy).not.toHaveBeenCalled();
      expect(detectChangesSpy).not.toHaveBeenCalled();
    });

    it('should call ngOnDestroy and complete _destroy$', () => {
      const nextSpy = jest.spyOn(component['_destroy$'], 'next');
      const completeSpy = jest.spyOn(component['_destroy$'], 'complete');
      component.ngOnDestroy();
      expect(nextSpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    it('should fetch products and append optionListProduct when productAppend is true', () => {
      component.productAppend = true;
      component.products = [...mockProductResponse.content];

      const handleGetMenuItemsSpy = jest.spyOn(component, 'handleGetMenuItems');

      component.handleGetProducts();

      expect(mockProductManagementService.getProducts).toHaveBeenCalled();
      expect(component.products).toEqual([
        ...mockProductResponse.content,
        ...mockProductResponse.content,
      ]);
      expect(handleGetMenuItemsSpy).toHaveBeenCalled();
    });

    it('should fetch products and replace optionListProduct when productAppend is false', () => {
      component.productAppend = false;

      const handleGetMenuItemsSpy = jest.spyOn(component, 'handleGetMenuItems');

      component.handleGetProducts();

      expect(mockProductManagementService.getProducts).toHaveBeenCalled();
      expect(component.products).toEqual([...mockProductResponse.content]);
      expect(handleGetMenuItemsSpy).toHaveBeenCalled();
    });

    it('should fetch menu items and update menuItems', () => {
      const updateOptionListProductSpy = jest.spyOn(
        component,
        'updateOptionListProduct',
      );
      const objectToQueryStringSpy = jest
        .spyOn(FuseUltils, 'objectToQueryString')
        .mockReturnValue('&size=10&page=0');
      component.menuItems = [mockMenuItem];

      component.handleGetMenuItems().subscribe((res) => {
        expect(objectToQueryStringSpy).toHaveBeenCalledWith({
          size: 10,
          page: 0,
        });
        expect(mockMenuItemsService.getMenuItems).toHaveBeenCalledWith(
          mockMenuId,
          '&size=10&page=0',
        );
        expect(component.menuItems).toEqual([
          mockMenuItem,
          ...mockMenuItemResponse.content,
        ]);
        expect(updateOptionListProductSpy).toHaveBeenCalled();
      });
    });
  });

  describe('Form Submission', () => {
    beforeEach(() => {
      jest
        .spyOn(component, 'prepareMenuItemData')
        .mockReturnValue(mockFormatFormValue);
      jest.spyOn(component, 'handleAddMultiMenuItems');
      jest.spyOn(component, 'handleUpdateMenuItem');
      jest.spyOn(Utils, 'scrollToInvalid');
      jest.spyOn(component, 'onClose');
    });

    it('should validate and submit the form', () => {
      component.data.detail = null;
      component.menuItemForm.patchValue({
        ...mockFormValue,
      });

      component.submitForm();

      expect(component.prepareMenuItemData).toHaveBeenCalledWith(mockFormValue);
      expect(component.handleAddMultiMenuItems).toHaveBeenCalledWith(
        mockFormatFormValue,
      );
      expect(component.handleUpdateMenuItem).not.toHaveBeenCalled();
    });

    it('should validate and submit the form when there is data detail', () => {
      component.data.detail = mockMenuItem;
      component.menuItemForm.patchValue({
        ...mockFormValue,
      });

      component.submitForm();

      expect(component.prepareMenuItemData).toHaveBeenCalledWith(mockFormValue);
      expect(component.handleUpdateMenuItem).toHaveBeenCalledWith(
        mockFormatFormValue,
      );
      expect(component.handleAddMultiMenuItems).not.toHaveBeenCalled();
    });

    it('should not submit the form when validation fails', () => {
      component.data.detail = null;
      component.menuItemForm.patchValue({
        productId: [],
        availableDate: { start: '', end: '' },
        availableTime: { start: '', end: '' },
        availableOn: [],
      });

      component.submitForm();

      expect(Utils.scrollToInvalid).toHaveBeenCalled();
      expect(component.prepareMenuItemData).not.toHaveBeenCalled();
      expect(component.handleAddMultiMenuItems).not.toHaveBeenCalled();
      expect(component.handleAddMultiMenuItems).not.toHaveBeenCalled();
    });

    it('should call executeAddMenuItem when run handleAddMultiMenuItems', () => {
      jest.spyOn(component, 'executeAddMenuItem').mockImplementation(() => {});
      component.optionListProduct = mockProductResponse.content;
      component.menuItemForm.patchValue({
        ...mockFormValue,
      });

      component.handleAddMultiMenuItems(
        mockFormatFormValue as unknown as IMenuItem,
      );

      expect(component.executeAddMenuItem).toHaveBeenCalledWith(
        [mockAddItem],
        expect.any(Function),
      );
    });

    it('should update a menu item then call onClose and show success toast after running success', () => {
      component.data.detail = mockMenuItem;
      component.menuItemForm.patchValue({
        ...mockFormValue,
        productId: [mockMenuItem.product.id],
      });

      component.handleUpdateMenuItem(
        mockFormatFormValue as unknown as IMenuItem,
      );

      expect(mockMenuItemsService.updateMenuItem).toHaveBeenCalledWith(
        mockMenuId,
        mockMenuItem.id,
        { ...mockFormatFormValue, productId: mockMenuItem.product.id },
      );
      expect(component.onClose).toHaveBeenCalled();
      expect(mockToastrService.success).toHaveBeenCalledWith(
        'Update menu item success!',
      );
      expect(component.loading).toBe(false);
    });

    it('should update a menu item then change loading to false after running error', () => {
      component.data.detail = mockMenuItem;
      component.menuItemForm.patchValue({
        ...mockFormValue,
        productId: [mockMenuItem.product.id],
      });
      jest
        .spyOn(mockMenuItemsService, 'updateMenuItem')
        .mockReturnValue(throwError(() => new Error('Update failed')));

      component.handleUpdateMenuItem(
        mockFormatFormValue as unknown as IMenuItem,
      );

      expect(mockMenuItemsService.updateMenuItem).toHaveBeenCalledWith(
        mockMenuId,
        mockMenuItem.id,
        { ...mockFormatFormValue, productId: mockMenuItem.product.id },
      );
      expect(component.onClose).not.toHaveBeenCalled();
      expect(mockToastrService.success).not.toHaveBeenCalledWith();
      expect(component.loading).toBe(false);
    });

    it('should prepare menu item data', () => {
      const result = component.prepareMenuItemData(mockFormValue);

      expect(result).toEqual(mockFormatFormValue);
    });

    it('should add menu item successfully', () => {
      const testAddItem = {
        ...mockAddItem,
      };
      component.loading = true;

      component.executeAddMenuItem([testAddItem], (menuId, data) =>
        mockMenuItemsService.addMenuItem(menuId, data),
      );

      expect(mockMenuItemsService.addMenuItem).toHaveBeenCalledWith(
        mockMenuId,
        {
          productId: testAddItem.productId,
          position: testAddItem.position,
          arrangement: testAddItem.arrangement,
        },
      );
      expect(testAddItem).toEqual({
        ...mockAddItem,
        status: 1,
        errorMessage: null,
      });
      expect(component.loading).toBe(false);
      expect(component.mode).toBe('complete');
    });

    it('should handle error during adding menu item', () => {
      const errorMessage = 'An error occurred';
      jest
        .spyOn(component['_menuItemsService'], 'addMenuItem')
        .mockReturnValue(throwError(() => new Error(errorMessage)));
      const testAddItem = {
        ...mockAddItem,
      };
      component.loading = true;

      component.executeAddMenuItem([testAddItem], (menuId, data) =>
        mockMenuItemsService.addMenuItem(menuId, data),
      );

      expect(mockMenuItemsService.addMenuItem).toHaveBeenCalledWith(
        mockMenuId,
        {
          productId: testAddItem.productId,
          position: testAddItem.position,
          arrangement: testAddItem.arrangement,
        },
      );
      expect(testAddItem).toEqual({
        ...mockAddItem,
        status: 0,
        errorMessage: 'An unknown error occurred.',
      });
      expect(component.loading).toBe(false);
      expect(component.mode).toBe('complete');
    });
  });

  describe('Utility Methods', () => {
    it('should remove date selection when run toggleDate with date has already been chosen', () => {
      const mockDate = { value: 'MONDAY' };
      component.menuItemForm.patchValue({
        availableOn: ['MONDAY', 'TUESDAY'],
      });

      component.toggleDate(mockDate);

      expect(component.menuItemForm.value.availableOn).toEqual(['TUESDAY']);
    });
    it('should add date selection when run toggleDate with date has not been chosen', () => {
      const mockDate = { value: 'MONDAY' };
      component.menuItemForm.patchValue({
        availableOn: ['TUESDAY'],
      });

      component.toggleDate(mockDate);

      expect(component.menuItemForm.value.availableOn).toEqual([
        'TUESDAY',
        'MONDAY',
      ]);
    });

    it('should update date range in form when isDateRangeSelected is false', () => {
      const mockType = 'allday';
      jest.spyOn(component, 'isDateRangeSelected').mockReturnValue(false);
      jest
        .spyOn(Utils, 'getDatesByRange')
        .mockReturnValue([{ value: 'MONDAY' }]);

      component.updateDateRange(mockType);

      expect(Utils.getDatesByRange).toHaveBeenCalledWith(
        DateOfWeekList,
        mockType,
      );
      expect(component.menuItemForm.value.availableOn).toEqual(['MONDAY']);
    });

    it('should not update date range in form when isDateRangeSelected is false', () => {
      const mockType = 'allday';
      component.menuItemForm.patchValue({
        availableOn: ['TUESDAY'],
      });
      jest.spyOn(component, 'isDateRangeSelected').mockReturnValue(true);

      jest
        .spyOn(Utils, 'getDatesByRange')
        .mockReturnValue([{ value: 'MONDAY' }]);

      component.updateDateRange(mockType);

      expect(component.menuItemForm.value.availableOn).toEqual(['TUESDAY']);
      expect(Utils.getDatesByRange).not.toHaveBeenCalled();
    });

    it('should check and return true if date is selected', () => {
      const mockDate = { value: 'MONDAY' };
      component.menuItemForm.patchValue({
        availableOn: ['MONDAY', 'TUESDAY'],
      });

      const result = component.isDateSelected(mockDate);

      expect(result).toBe(true);
    });

    it('should check and return false if date is not selected', () => {
      const mockDate = { value: 'MONDAY' };
      component.menuItemForm.patchValue({
        availableOn: ['TUESDAY'],
      });

      const result = component.isDateSelected(mockDate);

      expect(result).toBe(false);
    });

    it('should check if date range is selected when isDateRangeSelected is not all day', () => {
      const mockType = 'weekends';
      jest
        .spyOn(Utils, 'getDatesByRange')
        .mockReturnValue([{ value: 'MONDAY' }]);
      jest.spyOn(Utils, 'compareStringArray').mockReturnValue(true);
      component.menuItemForm.patchValue({
        availableOn: ['MONDAY'],
      });

      const result = component.isDateRangeSelected(mockType);

      expect(Utils.getDatesByRange).toHaveBeenCalledWith(
        DateOfWeekList,
        mockType,
      );
      expect(Utils.compareStringArray).toHaveBeenCalledWith(
        ['MONDAY'],
        ['MONDAY'],
      );
      expect(result).toBe(true);
    });

    it('should check if date range is selected when isDateRangeSelected is all day', () => {
      const mockType = 'allday';
      component.menuItemForm.patchValue({
        availableOn: DateOfWeekList.map((day) => day.value),
      });
      jest.spyOn(Utils, 'getDatesByRange');
      jest.spyOn(Utils, 'compareStringArray');

      const result = component.isDateRangeSelected(mockType);

      expect(result).toBe(true);
      expect(Utils.compareStringArray).not.toHaveBeenCalled();
      expect(Utils.getDatesByRange).not.toHaveBeenCalled();
    });

    it('should map the product list and hide the existing value when run hideExistingProducts', () => {
      const mockMenuItems = [mockMenuItem];
      const productHidden = mockProductResponse.content[0];
      const productNotHidden = mockProductResponse.content[1];
      const mockProducts = [productHidden, productNotHidden];
      jest.spyOn(FuseUltils, 'formatPrice').mockReturnValue('$100');

      const result = component.hideExistingProducts(
        mockMenuItems,
        mockProducts,
      );

      expect(result).toEqual([
        {
          ...productHidden,
          value: productHidden.id,
          label: `${productHidden.name} - ${productHidden.sku} - $100 - ${productHidden.category.name}`,
          hidden: true,
        },
        {
          ...productNotHidden,
          value: productNotHidden.id,
          label: `${productNotHidden.name} - ${productNotHidden.sku} - $100 - ${productNotHidden.category.name}`,
          hidden: false,
        },
      ]);
    });

    it('should update form data correctly when updateFormData is called', () => {
      component.data.detail = { ...mockMenuItem };
      const testMenuItem = {
        ...mockMenuItem,
        product: {
          ...mockMenuItem.product,
          label: `${mockMenuItem.product.name} - ${mockMenuItem.product.sku} - $100 - ${mockMenuItem.product.category.name}`,
          value: mockMenuItem.product.id,
        },
      };
      jest.spyOn(FuseUltils, 'formatPrice').mockReturnValue('$100');
      jest.spyOn(Utils, 'convertTimeToTimestamp').mockReturnValue(123456789);

      component.updateFormData();

      expect(Utils.convertTimeToTimestamp).toHaveBeenCalledWith(
        testMenuItem.arrangement.startTime,
      );
      expect(Utils.convertTimeToTimestamp).toHaveBeenCalledWith(
        testMenuItem.arrangement.endTime,
      );
      expect(component.optionListProduct).toEqual([testMenuItem.product]);
      expect(component.menuItemForm.getRawValue()).toEqual({
        productId: [testMenuItem.product.id],
        position: testMenuItem.position,
        availableDate: {
          start: testMenuItem.arrangement.startDate,
          end: testMenuItem.arrangement.endDate,
        },
        availableTime: {
          start: 123456789, // Mocked timestamp
          end: 123456789, // Mocked timestamp
        },
        availableOn: mockMenuItem.arrangement.availableOn,
      });
      expect(component.menuItemForm.controls['productId'].disabled).toBe(true);
    });

    it('should handle lazy load select correctly when handleLazyLoadSelect is called', () => {
      const mockEvent = { page: 2, search: 'Mock Search' };
      jest.spyOn(component, 'handleGetProducts');

      component.handleLazyLoadSelect(mockEvent);

      expect(component.productAppend).toBe(true);
      expect(component.productParams.filter.name).toBe('Mock Search');
      expect(component.productParams.page).toBe(2);
      expect(component.handleGetProducts).toHaveBeenCalled();
    });

    it('should update option list product correctly when updateOptionListProduct is called', () => {
      component.menuItems = [mockMenuItem];
      component.products = [...mockProductResponse.content];

      jest.spyOn(component, 'hideExistingProducts').mockReturnValue([
        {
          ...mockProductResponse.content[0],
          hidden: true,
          value: 'hidden-product-id',
          label: 'hidden-product-id',
        },
        {
          ...mockProductResponse.content[1],
          hidden: false,
          value: 'visible-product-id',
          label: 'visible-product-id',
        },
      ]);

      component.updateOptionListProduct();

      expect(component.hideExistingProducts).toHaveBeenCalledWith(
        [mockMenuItem],
        [...mockProductResponse.content],
      );
      expect(component.optionListProduct).toEqual([
        {
          ...mockProductResponse.content[0],
          hidden: true,
          value: 'hidden-product-id',
          label: 'hidden-product-id',
        },
        {
          ...mockProductResponse.content[1],
          hidden: false,
          value: 'visible-product-id',
          label: 'visible-product-id',
        },
      ]);
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });
});
