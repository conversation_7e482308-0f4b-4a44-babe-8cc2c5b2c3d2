import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltip } from '@angular/material/tooltip';
import { ActivatedRoute, Router } from '@angular/router';
import { AdvancedSearchComponent } from '@fuse/components/advanced-search';
import { FuseErrorNoDataComponent } from '@fuse/components/error-no-data/error-no-data.component';
import { FuseTableComponent, IAction } from '@fuse/components/table';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { FuseUltils } from '@fuse/ultils';
import {
  getAvailableDate,
  getAvailableTime,
  getDateOfWeekString,
  IMenuItem,
  IMenuItemResponse,
} from './menu-items.types';
import { ProductCategoryManagementService } from '../../product-category-management/product-category-management.service';
import { ToastrService } from 'ngx-toastr';
import {
  debounceTime,
  forkJoin,
  Observable,
  Subject,
  takeUntil,
  tap,
} from 'rxjs';
import { ScrollCheckingDirective } from '../../../../../../../../libs/fuse/src/lib/directives/scroll-checking/scroll-checking.directive';
import { APP_TOOLTIP, PRODUCT_STATUS, ROUTE } from '../../../../core/const';
import { Currency } from '../../tenant-management/tenant.types';
import { AddMenuItemComponent } from './add-menu-tem/add-menu-item.component';
import { MenuItemsService } from './menu-items.service';
import { MenuItemCardListComponent } from './menu-tem-card-list/menu-item-card-list.component';

@Component({
  selector: 'app-menu-items',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatTooltip,
    MatIconModule,
    MatMenuModule,
    FuseTableComponent,
    AdvancedSearchComponent,
    MenuItemCardListComponent,
    FuseErrorNoDataComponent,
    ScrollCheckingDirective,
  ],
  templateUrl: './menu-items.component.html',
})
export class MenuItemsComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() id!: string;
  @Input() storeId!: string;
  @Input() mode = 'view';
  @Input() updatePermission = { update: false, productView: false };

  dataSource: Array<any> = [];
  displayedColumns: any = [];
  total = 0;
  dataAppend = false;

  actions: Array<IAction> = [];
  bulkActions: Array<IAction> = [];
  sortDefault: any = {
    field: 'name',
    direction: 'asc',
  };

  searchForm: any;
  queryParams!: any;

  optionListCategory: Array<any> = [];

  viewMode = 'grid';
  selectedMenuItems: Array<any> = [];

  productStatus = PRODUCT_STATUS;
  productWarning = APP_TOOLTIP.PRODUCT_ARCHIVE;

  private _unsubscribeAll: Subject<void> = new Subject<void>();

  /**
   * Constructor
   */
  constructor(
    private _router: Router,
    private _dialog: MatDialog,
    private _toast: ToastrService,
    private _activatedRoute: ActivatedRoute,
    private _menuItemsService: MenuItemsService,
    private _changeDetectorRef: ChangeDetectorRef,
    private _confirmationService: FuseConfirmationService,
    private _categoriesService: ProductCategoryManagementService,
  ) {
    this.searchForm = this.initSearchForm();
    this.resetQueryParams();
  }

  // Lifecycle hooks
  ngOnInit(): void {
    this._activatedRoute.queryParams
      .pipe(
        debounceTime(500),
        takeUntil(this._unsubscribeAll),
        tap((queryParams: any) => {
          if (this.queryParams?.page >= queryParams?.page) {
            this.dataAppend = false;
          }
          this.queryParams = queryParams;
        }),
      )
      .subscribe(() => {
        this.handleGetMenuItems();
      });

    this.handleGetCategory();
  }

  ngAfterViewInit(): void {
    this.initTableColumn();
    this.initTableAction();
    this._changeDetectorRef.detectChanges();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }

  handleGetCategory(): void {
    this._categoriesService.getProductCategories().subscribe((res: any) => {
      this.optionListCategory = res.content.map((item: any) => {
        return {
          ...item,
          label: item.name,
          value: item.id,
        };
      });

      const index = this.searchForm.basic.findIndex(
        (item: any) => item.name === 'categoryId',
      );

      if (index < 0) {
        return;
      }

      this.searchForm.basic[index].options = [
        { label: 'All', value: '' },
        ...this.optionListCategory,
      ];
    });
  }

  // Get menu items based on query parameters
  handleGetMenuItems(): void {
    const mappedData = this.getMenuItemParams(this.queryParams);
    this._menuItemsService
      .getMenuItems(this.id, FuseUltils.objectToQueryString(mappedData))
      .subscribe((res: IMenuItemResponse) => {
        const responseData = res.content.map((item: IMenuItem) => {
          return {
            ...item,
            product: {
              ...item.product,
              unitPriceStr: this.formatPriceValue(
                item.product.unitPrice,
                item.product?.currency,
              ),
              listingPriceStr: this.formatPriceValue(
                item.product.listingPrice,
                item.product?.currency,
              ),
            },
            name: item.product.name,
            sku: item.product.sku,
            description: item.product.description,
            category: item.product.category,
            availableDate: getAvailableDate(item.arrangement),
            availableTime: getAvailableTime(item.arrangement),
            availableOnString: getDateOfWeekString(
              item.arrangement.availableOn,
            ),
          };
        });

        this.dataSource = this.dataAppend
          ? [...this.dataSource, ...responseData]
          : [...responseData];

        this.total = res.totalElements;
        this.dataAppend = true;
      });
  }

  getMenuItemParams(queryParams: any): any {
    const basicSearch = this.searchForm.basic;

    const filter = {
      name: queryParams['name'] ?? basicSearch[0].defaultValue,
      sku: queryParams['sku'] ?? basicSearch[1].defaultValue,
      categoryId: queryParams['categoryId'] ?? basicSearch[2].defaultValue,
      // fromTime: queryParams['fromTime'] || '',
      // toTime: queryParams['toTime'] || '',
    };

    const mappedData = {
      filter,
      size: queryParams['size'] || 10,
      page: queryParams['page'] || 0,
      sortDirection: queryParams['sortDirection'] || this.sortDefault.direction,
      sortFields: queryParams['sortFields'] || [this.sortDefault.field],
    };

    return mappedData;
  }

  // Change the state of the view mode and selected menu items
  onChangeViewMode(viewMode: string): void {
    this.viewMode = viewMode;
    this.selectedMenuItems = [];
    this.resetQueryParams();
  }

  onChangeSelectedElement(event: Array<any>): void {
    this.selectedMenuItems = event;
  }

  onUpdateSelectedMenuItems(event: any): void {
    const index = this.selectedMenuItems.findIndex(
      item => item.id == event.id,
    );
    if (index != -1) {
      this.selectedMenuItems.splice(index, 1);
    } else {
      this.selectedMenuItems.push(event);
    }
  }

  // Open the dialog to modify menu items
  onOpenAddMenuItemDialog() {
    const dialogRef = this._dialog.open(AddMenuItemComponent, {
      width: FuseUltils.checkSize('width', 640) ? '80%' : '90%',
      autoFocus: false,
      disableClose: true,
      data: {
        menuId: this.id,
        storeId: this.storeId,
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.resetQueryParams();
      }
    });
  }

  onOpenEditMenuItemDialog(item: any): void {
    const dialogRef = this._dialog.open(AddMenuItemComponent, {
      width: FuseUltils.checkSize('width', 640) ? '80%' : '90%',
      autoFocus: false,
      disableClose: true,
      data: {
        menuId: this.id,
        storeId: this.storeId,
        detail: item,
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.resetQueryParams();
      }
    });
  }

  onOpenDeleteMenuItemDialog(item: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Delete Menu Item',
      message: 'Please confirm to delete this menu item.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteMenuItem(item);
      }
    });
  }

  handleDeleteMenuItem(item: any): void {
    this._menuItemsService.deleteMenuItem(this.id, item.id).subscribe(() => {
      this._toast.success('Delete menu item success!');
      this.resetQueryParams();
      this.selectedMenuItems = [];
    });
  }

  onOpenDeleteSelectedMenuItemsDialog(): void {
    const dialogRef = this._confirmationService.open({
      title: 'Delete Menu Items',
      message: 'Please confirm to delete this selected menu item(s).',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteSelectedMenuItems();
      }
    });
  }

  handleDeleteSelectedMenuItems(): void {
    forkJoin(
      this.selectedMenuItems.map(item =>
        this._menuItemsService.deleteMenuItem(this.id, item.id),
      ),
    ).subscribe({
      next: () => {
        this._toast.success(
          'Selected menu item(s) have been deleted successfully!',
        );
        this.resetQueryParams();
        this.selectedMenuItems = [];
      },
      error: () => {
        this.resetQueryParams();
        this.selectedMenuItems = [];
      },
    });
  }

  handleViewProductDetail(element: any): void {
    this._router.navigate([
      `${ROUTE.PRODUCT.MAIN}/${ROUTE.PRODUCT.DETAIL}/${element.product.id}`,
    ]);
  }

  formatPriceValue(value: any, currency: Currency): string {
    return FuseUltils.formatPrice(value, currency) ?? '';
  }

  onRowClick = (row: IMenuItem) => {
    if (this.updatePermission.update) {
      this.onOpenEditMenuItemDialog(row);
    }
  };

  // Update the query parameters to trigger data fetching
  resetQueryParams(): void {
    const defaultParams = {
      page: 0,
      size: 10,
      sortDirection: this.sortDefault.direction,
      sortFields: [this.sortDefault.field],
      t: new Date().getTime(),
    };
    // this.queryParams = { ...defaultParams };

    this.dataAppend = false;
    // this.handleGetMenuItems();

    this._router.navigate([], {
      relativeTo: this._activatedRoute,
      queryParams: defaultParams,
      queryParamsHandling: 'merge',
      onSameUrlNavigation: 'reload',
    });
  }

  updateGridQueryParams(): void {
    if (this.dataSource.length === this.total) {
      return;
    }

    const params =
      JSON.stringify(this.queryParams) !== '{}'
        ? this.queryParams
        : {
            page: 0,
            size: 10,
            sortDirection: this.sortDefault.direction,
            sortFields: [this.sortDefault.field],
          };
    const queryParams = {
      ...params,
      page: +params.page + 1,
    };

    this._router.navigate([], {
      relativeTo: this._activatedRoute,
      queryParams,
      queryParamsHandling: 'merge',
    });
  }

  // Update the displayed order of menu items
  arrangeItemsClicked(event: any, index: number, type: 'up' | 'down'): void {
    event.stopPropagation();

    this.changeDisplayOrder(index, type);
  }

  changeDisplayOrder(index: number, type: 'up' | 'down'): void {
    if (type === 'up' && index > 0) {
      this.switchDisplayOrder(index - 1, index);
    } else if (type === 'down' && index < this.dataSource.length - 1) {
      this.switchDisplayOrder(index, index + 1);
    }
  }

  switchDisplayOrder(indexStart: number, indexEnd: number): void {
    if (indexStart < 0 || indexEnd < 0) {
      return;
    }

    const newPositionStart =
      this.dataSource[indexStart].position ===
      this.dataSource[indexEnd].position
        ? this.dataSource[indexEnd].position + 1
        : this.dataSource[indexEnd].position;
    const newPositionEnd = this.dataSource[indexStart].position;

    forkJoin([
      this.updateDisplayOrder(this.dataSource[indexStart], newPositionStart),
      this.updateDisplayOrder(this.dataSource[indexEnd], newPositionEnd),
    ])
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        this._toast.success(
          'You have successfully updated the display order these menu items!',
        );

        const newDataSource = [...this.dataSource];

        const temp = newDataSource[indexStart];
        newDataSource[indexStart] = {
          ...newDataSource[indexEnd],
          position: newPositionEnd,
        };
        newDataSource[indexEnd] = {
          ...temp,
          position: newPositionStart,
        };

        this.dataSource = [...newDataSource];
        this._changeDetectorRef.detectChanges();
      });
  }

  // Send request to update the displayed order of menu items
  updateDisplayOrder(data: IMenuItem, position: number): Observable<void> {
    return this._menuItemsService.updateMenuItem(this.id, data.id, {
      ...data,
      position,
    });
  }

  /**
   * Initializes the table column configuration.
   */
  private initTableColumn(): void {
    this.displayedColumns = [
      {
        key: 'name',
        name: 'Product Name',
        sort: true,
        selected: true,
        custom: true,
        // renderHtml: (value: string) => {
        //   return `<p class="font-semibold">${value}</p>`;
        // },
      },
      {
        key: 'position',
        name: 'Display Order',
        sort: true,
        selected: true,
        custom: true,
      },
      {
        key: 'sku',
        name: 'SKU',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'category',
        name: 'Category',
        sort: true,
        selected: true,
        render: (value: any) => {
          return value?.name ?? '';
        },
      },
      {
        key: 'description',
        name: 'Description',
        selected: false,
      },
      {
        key: 'availableDate',
        name: 'Available Date',
        selected: true,
      },
      {
        key: 'availableTime',
        name: 'Available Time',
        selected: true,
      },
      {
        key: 'availableOnString',
        name: 'Available on',
        selected: true,
      },
    ];
  }

  /**
   * Initializes the actions array for the table.
   *
   * @private
   * @returns {void}
   */
  private initTableAction(): void {
    this.actions = [
      {
        name: 'View Product Detail',
        type: 'event',
        hidden: () => !this.updatePermission.productView,
        callback: element => this.handleViewProductDetail(element),
      },
      {
        name: 'Edit',
        type: 'event',
        callback: element => this.onOpenEditMenuItemDialog(element),
      },
      {
        name: 'Delete',
        type: 'event',
        callback: element => this.onOpenDeleteMenuItemDialog(element),
      },
    ];

    this.bulkActions = [
      {
        name: 'Delete',
        type: 'event',
        callback: () => this.onOpenDeleteSelectedMenuItemsDialog(),
      },
    ];
  }

  /**
   * Initializes the search form configuration object.
   *
   * @returns {any} The search form configuration object.
   */
  private initSearchForm(): any {
    return {
      basic: [
        {
          label: 'Product Name',
          name: 'name',
          placeholder: 'Enter name',
          type: 'text',
          prefixIcon: 'styl:MagnifyingGlassOutlineBold',
          defaultValue: '',
        },
        {
          label: 'SKU',
          name: 'sku',
          placeholder: 'Enter sku',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Category',
          name: 'categoryId',
          placeholder: 'Select category',
          type: 'select',
          defaultValue: '',
          options: [
            {
              label: 'All',
              value: '',
            },
          ],
        },
      ],
    };
  }
}
