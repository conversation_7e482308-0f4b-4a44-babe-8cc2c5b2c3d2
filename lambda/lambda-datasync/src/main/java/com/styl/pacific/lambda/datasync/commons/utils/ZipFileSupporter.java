/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.lambda.datasync.commons.utils;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Optional;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;

@UtilityClass
public class ZipFileSupporter {

	private static final int BUFFER_SIZE;

	static {
		BUFFER_SIZE = Optional.ofNullable(SystemEnvironmentSupporter.getEnvConfig(
				DataSyncEnvironmentConfig.ZIP_BUFFER_SIZE))
				.map(Integer::valueOf)
				.orElse(16384);
	}

	@SneakyThrows
	public static Path unzipFileToDirectory(File zipFile, String targetDirectory) {
		try (ZipInputStream zipIn = new ZipInputStream(new BufferedInputStream(new FileInputStream(zipFile)))) {

			final var directoryPath = Files.createDirectories(Paths.get(targetDirectory));

			ZipEntry entry;
			while ((entry = zipIn.getNextEntry()) != null) {
				String filePath = targetDirectory + File.separator + entry.getName();

				File outputFile = new File(filePath);
				if (!entry.isDirectory()) {
					new File(outputFile.getParent()).mkdirs();
					extractZipEntry(zipIn, outputFile);
				}

				zipIn.closeEntry();
			}
			return directoryPath;
		}
	}

	@SneakyThrows
	public static void extractZipEntry(ZipInputStream zipIn, File outputFile) {
		try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(outputFile), BUFFER_SIZE)) {

			byte[] buffer = new byte[BUFFER_SIZE];
			int read;
			while ((read = zipIn.read(buffer)) != -1) {
				bos.write(buffer, 0, read);
			}
		}
	}

}
