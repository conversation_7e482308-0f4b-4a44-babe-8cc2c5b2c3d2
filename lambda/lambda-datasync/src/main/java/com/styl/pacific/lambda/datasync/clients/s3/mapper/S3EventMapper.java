/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.lambda.datasync.clients.s3.mapper;

import com.amazonaws.services.lambda.runtime.events.models.s3.S3EventNotification;
import com.styl.pacific.lambda.datasync.clients.s3.entities.S3NotificationRecord;
import com.styl.pacific.lambda.datasync.clients.s3.entities.S3ObjectEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface S3EventMapper {
	S3EventMapper INSTANCE = Mappers.getMapper(S3EventMapper.class);

	@Mapping(target = "awsRegion", ignore = true)
	@Mapping(target = "requestParameters", ignore = true)
	@Mapping(target = "responseElements", ignore = true)
	@Mapping(target = "userIdentity", ignore = true)
	@Mapping(target = "s3.configurationId", ignore = true)
	@Mapping(target = "s3.s3SchemaVersion", ignore = true)
	@Mapping(target = "s3.bucket.name", source = "s3.bucket.name")
	@Mapping(target = "s3.bucket.ownerIdentity", ignore = true)
	@Mapping(target = "s3.bucket.arn", source = "s3.bucket.arn")
	@Mapping(target = "s3.object", expression = "java(mapToS3ObjectEntity(s3Entity.getObject()))")
	S3EventNotification.S3EventNotificationRecord toS3EventNotificationRecord(S3NotificationRecord source);

	default S3EventNotification.S3ObjectEntity mapToS3ObjectEntity(S3ObjectEntity object) {
		return new S3EventNotification.S3ObjectEntity(object.getKey(), object.getSize(), null, null, null);
	}
}