/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.lambda.datasync.chains;

import com.amazonaws.services.lambda.runtime.events.models.s3.S3EventNotification;
import com.styl.pacific.lambda.datasync.clients.s3.entities.DownloadedFileMetadata;
import com.styl.pacific.lambda.datasync.commons.checksum.ChecksumAlgorithm;
import java.nio.file.Path;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;

@Getter
@RequiredArgsConstructor
@Builder
@With
public class DataSyncChainFlowData {
	private final Long jobId;
	private final String tenantId;
	private final S3EventNotification.S3EventNotificationRecord s3NotificationRecord;
	private final String fileName;
	private final DownloadedFileMetadata checksumFileMetadata;
	private final DownloadedFileMetadata zipFileMetadata;
	private final String zipFileHashValue;
	private final ChecksumAlgorithm checksumAlgorithm;
	private final String archivedDirectory;
	private final Path localUnzippedDirectoryPath;

	@Builder.Default
	private final Boolean isValidChecksum = Boolean.FALSE;

	@Builder.Default
	private final Boolean isCleanedLocalFile = Boolean.FALSE;

}
