<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.styl.pacific</groupId>
        <artifactId>pacific-microservices</artifactId>
        <version>2.1.0</version>
    </parent>
    <groupId>com.styl.pacific.wallet.service</groupId>
    <artifactId>wallet-service</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>wallet-application</module>
        <module>wallet-data-access</module>
        <module>wallet-domain</module>
        <module>wallet-messaging</module>
        <module>wallet-rest</module>
        <module>wallet-scheduling</module>
        <module>wallet-shared</module>
        <module>wallet-job</module>
    </modules>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-provider-jdbc-template</artifactId>
                <version>${shedlock-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-spring</artifactId>
                <version>${shedlock-spring.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
