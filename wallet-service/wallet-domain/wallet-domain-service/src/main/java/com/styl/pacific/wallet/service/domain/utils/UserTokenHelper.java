/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.utils;

import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.wallet.service.exception.TokenOwnerMismatchException;
import java.util.Objects;

/**
 * <AUTHOR>
 */

public class UserTokenHelper {

	private UserTokenHelper() {
	}

	public static boolean isCustomerToken(TokenClaim tokenClaim) {
		return Objects.nonNull(tokenClaim) && UserType.CUSTOMER.equals(tokenClaim.getUserType());
	}

	public static void validateUserTokenOwner(TokenClaim tokenClaim, Long userId) {
		if (Objects.nonNull(tokenClaim) && UserType.CUSTOMER.equals(tokenClaim.getUserType()) && !Objects.equals(String
				.valueOf(userId), tokenClaim.getUserId())) {
			throw new TokenOwnerMismatchException("Token owner is mismatch");
		}
	}
}
