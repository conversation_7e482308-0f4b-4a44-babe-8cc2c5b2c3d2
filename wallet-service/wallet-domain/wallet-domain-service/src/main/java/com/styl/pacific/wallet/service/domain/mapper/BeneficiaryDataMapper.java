/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.BeneficiaryId;
import com.styl.pacific.wallet.service.entity.Beneficiary;
import com.styl.pacific.wallet.service.entity.BeneficiaryDto;
import com.styl.pacific.wallet.service.entity.FundSource;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { WalletDataCommonMapper.class, MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface BeneficiaryDataMapper {

	BeneficiaryDataMapper INSTANCE = Mappers.getMapper(BeneficiaryDataMapper.class);

	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "deletedAt", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "customerId", qualifiedByName = "longToUserId")
	@Mapping(target = "fundSourceId", qualifiedByName = "longToFundSourceId")
	Beneficiary toBeneficiary(Long tenantId, Long customerId, Long fundSourceId, BeneficiaryId id);

	@Mapping(target = "createdAt", source = "beneficiary.createdAt")
	@Mapping(target = "fundSource", source = "fundSource")
	@Mapping(target = "id", source = "beneficiary.id")
	BeneficiaryDto toBeneficiaryDto(Beneficiary beneficiary, FundSource fundSource);
}
