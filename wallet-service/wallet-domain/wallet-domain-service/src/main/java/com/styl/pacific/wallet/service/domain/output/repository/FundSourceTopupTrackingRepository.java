/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.output.repository;

import com.styl.pacific.domain.dto.pagination.PaginationQuery;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.wallet.service.domain.dto.fundsource.FundSourcesTopupTrackingFilterQuery;
import com.styl.pacific.wallet.service.entity.FundSourceTopupTracking;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface FundSourceTopupTrackingRepository {
	FundSourceTopupTracking save(FundSourceTopupTracking fundSourceTopupTracking);

	Optional<FundSourceTopupTracking> findTopupTracking(Long tenantId, Long topupHistoryId, Long customerId);

	Paging<FundSourceTopupTracking> findFundSourceTopupTracking(
			PaginationQuery<FundSourcesTopupTrackingFilterQuery> query);
}
