/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.output.repository;

import com.styl.pacific.wallet.service.domain.dto.fundsource.FindFundSourceTopupSchedulerQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsource.GetFundSourceSchedulerQuery;
import com.styl.pacific.wallet.service.entity.FundSourceTopupScheduler;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface FundSourceSchedulerRepository {

	FundSourceTopupScheduler save(FundSourceTopupScheduler fundSourceTopupScheduler);

	Optional<FundSourceTopupScheduler> getFundSourceScheduler(GetFundSourceSchedulerQuery query);

	List<FundSourceTopupScheduler> findFundSourceTopupSchedulers(FindFundSourceTopupSchedulerQuery query);

	void deleteFundSourceScheduler(Long id);

	Optional<FundSourceTopupScheduler> getOldestOrOverRunScheduler(Instant cutOffTime);

	void deleteAllFundSourceSchedulerBy(Long tenantId, Long fundSourceId);
}
