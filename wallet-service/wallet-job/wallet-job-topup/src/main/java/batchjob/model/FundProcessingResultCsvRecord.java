/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package batchjob.model;

import com.opencsv.bean.CsvBindByName;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FundProcessingResultCsvRecord extends FundTopupCsvRecord {

	public final static List<String> HEADERS = List.of("customer_no", "first_name", "last_name", "email",
			"parent_email", "group_name", "amount", "status", "transaction_id", "error");

	public FundProcessingResultCsvRecord(FundTopupCsvRecord record, Status status, BigDecimal amount,
			String transactionId) {
		this.setCustomerNo(record.getCustomerNo());
		this.setFirstName(record.getFirstName());
		this.setLastName(record.getLastName());
		this.setEmail(record.getEmail());
		this.setParentEmail(record.getParentEmail());
		this.setGroupName(record.getGroupName());
		this.setAmount(amount);
		this.setTransactionId(transactionId);
		this.setStatus(status.value);
	}

	public FundProcessingResultCsvRecord(FundTopupCsvRecord record, Status status, String error) {
		this(record, status, null, null);
		this.error = error;
	}

	@CsvBindByName(column = "status")
	private String status;

	@CsvBindByName(column = "transaction_id")
	private String transactionId;

	@CsvBindByName(column = "error")
	private String error;

	@Getter
	public enum Status {
		SUCCESS("Success"),
		FAILED("Failed"),
		SKIPPED("Skipped");

		private final String value;

		Status(String value) {
			this.value = value;
		}

	}
}
