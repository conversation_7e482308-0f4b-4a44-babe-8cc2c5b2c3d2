/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package batchjob.processor;

import com.styl.pacific.wilmar.batchjob.config.FileConfiguration;
import com.styl.pacific.wilmar.batchjob.config.TopupConfiguration;
import com.styl.pacific.wilmar.batchjob.exception.FileProcessingException;
import com.styl.pacific.wilmar.batchjob.service.FundTopupCsvService;
import com.styl.pacific.wilmar.batchjob.service.file.FileManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Main processor for fund topup batch operations
 * Handles CSV file processing for fund topup operations
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FundTopupProcessor implements FundProcessor {

	private final FundTopupCsvService fundTopupCsvProcessor;
	private final FileManagementService fileManagementService;
	private final FileConfiguration fileConfiguration;
	private final TopupConfiguration topupConfiguration;

	/**
	 * Processes fund topup operations from CSV files
	 */
	@Override
	public void process() {
		log.info("Starting fund topup processing");

		try {
			fileManagementService.ensureDirectoriesExist();

			// 1. Load the file from local or S3 into input directory
			String inputFilePath = fileManagementService.copyFileToInputDirectory(fileConfiguration.getInputFile());

			// 2. Process the file
			String outputFilePath = fileConfiguration.getOutputDirectory() + "/"
					+ fileManagementService.getOutputFileName();
			processFile(inputFilePath, outputFilePath);

			// 3. Move processed files to processed directory
			fileManagementService.moveToProcessedDirectory(outputFilePath);
			log.info("Fund topup processing completed successfully");
		} catch (Exception e) {
			throw new FileProcessingException("Error processing fund topup", e);
		}
	}

	/**
	 * Processes a specific CSV file for fund topup operations
	 *
	 * @param inputFilePath  Path to input CSV file
	 * @param outputFilePath Path to output CSV file
	 * @return Number of processed records
	 */
	public void processFile(String inputFilePath, String outputFilePath) {
		log.info("Processing fund topup file: {} -> {}", inputFilePath, outputFilePath);

		fundTopupCsvProcessor.processFundTopupRecords(inputFilePath, outputFilePath);
	}

}
