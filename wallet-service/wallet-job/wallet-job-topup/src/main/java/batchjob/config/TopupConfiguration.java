/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package batchjob.config;

import java.time.Duration;
import java.util.Currency;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for S3 integration
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "pacific.config.processor.topup")
public class TopupConfiguration {

	/**
	 * Tenant Id, will be sent to the backend via header
	 */
	private long tenantId;

	/**
	 * Time Zone of the Tenant
	 */
	private String timeZone = "Asia/Singapore";

	/**
	 * Fund Expiration
	 */
	private Duration fundExpiration = Duration.parse("PT167H"); // default 6 days 23 hours

	/**
	 * Required parent email to check if sub-account belongs to the parent
	 */
	private boolean requiredParentEmail = true;

	/**
	 * Allow negative amount to enable amount deduction from wallet
	 */
	private boolean allowDeduction = false;

	/**
	 * Allow zero amount, if enabled this will skip the topup
	 */
	private boolean allowZeroAmount = false;

	/**
	 * Allow negative balance when deducting amount, if not enabled this will deduct the remain balance to zero
	 */
	private boolean allowNegativeBalance = false;

	/**
	 * Currency of the amount, to convert BigDecimal to BigInteger unit of the currency
	 */
	private Currency currency = Currency.getInstance("SGD");
}
