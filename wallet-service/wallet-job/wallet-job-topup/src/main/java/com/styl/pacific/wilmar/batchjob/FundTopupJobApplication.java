/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wilmar.batchjob;

import com.styl.pacific.wilmar.batchjob.processor.FundTopupProcessor;
import java.util.Arrays;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;

@SpringBootApplication(scanBasePackages = { "com.styl.pacific" })
public class FundTopupJobApplication implements CommandLineRunner {

	private final static Logger logger = LoggerFactory.getLogger(FundTopupJobApplication.class);

	public static void main(String[] args) {
		new SpringApplicationBuilder(FundTopupJobApplication.class).web(WebApplicationType.NONE)
				.run(args);
	}

	@Autowired
	private FundTopupProcessor fundTopupProcessor;

	@Override
	public void run(String... args) {
		logger.info("Start FundTopupJobApplication");
		logger.info("args: {}", Arrays.toString(args));
		Set<String> argsSet = Set.of(args);

		try {
			if (argsSet.contains("--topup")) {
				logger.info("Running fund topup processing");
				fundTopupProcessor.process();
			} else {
				logger.info("No specific operation specified. Available options: --topup");
				printUsage();
			}
			logger.info("FundTopupJobApplication completed successfully");
		} catch (Exception e) {
			logger.error("Error during batch job execution", e);
			System.exit(1);
		}
	}

	private void printUsage() {
		logger.info("Usage:");
		logger.info("  --topup  : Process fund topup CSV files");
		logger.info("  --reset  : Process fund reset CSV files");
	}
}