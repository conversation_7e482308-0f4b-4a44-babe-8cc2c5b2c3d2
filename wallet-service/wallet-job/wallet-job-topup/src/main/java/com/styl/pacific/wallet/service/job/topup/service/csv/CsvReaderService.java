/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.job.topup.service.csv;

import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import com.styl.pacific.wallet.service.job.topup.exception.CsvProcessingException;
import java.io.FileReader;
import java.io.IOException;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Generic CSV reader service using OpenCSV CSVBean
 * Provides methods to read CSV files and convert them to Java objects
 */
@Slf4j
@Service
public class CsvReaderService {

	/**
	 * Reads CSV file and converts to list of objects using CSVBean
	 *
	 * @param filePath Path to the CSV file
	 * @param clazz    Class type to convert CSV records to
	 * @param <T>      Type of the target class
	 * @return List of objects converted from CSV records
	 * @throws CsvProcessingException if reading fails
	 */
	public <T> List<T> readCsvFile(String filePath, Class<T> clazz) {
		log.info("Reading CSV file: {} for class: {}", filePath, clazz.getSimpleName());

		try (Reader reader = new FileReader(filePath, StandardCharsets.UTF_8)) {
			return readCsvFromReader(reader, clazz);
		} catch (IOException e) {
			String errorMessage = String.format("Failed to read CSV file: %s", filePath);
			log.error(errorMessage, e);
			throw new CsvProcessingException(errorMessage, e);
		}
	}

	/**
	 * Reads CSV from Reader and converts to list of objects using CSVBean
	 *
	 * @param reader Reader containing CSV data
	 * @param clazz  Class type to convert CSV records to
	 * @param <T>    Type of the target class
	 * @return List of objects converted from CSV records
	 * @throws CsvProcessingException if reading fails
	 */
	public <T> List<T> readCsvFromReader(Reader reader, Class<T> clazz) {
		log.debug("Reading CSV from reader for class: {}", clazz.getSimpleName());

		try {
			CsvToBean<T> csvToBean = new CsvToBeanBuilder<T>(reader).withType(clazz)
					.withIgnoreLeadingWhiteSpace(true)
					.withIgnoreEmptyLine(true)
					.withSkipLines(0) // Don't skip header line as CSVBean handles it
					.build();

			List<T> records = csvToBean.parse();
			log.info("Successfully read {} records from CSV", records.size());

			// Check for parsing errors
			if (csvToBean.getCapturedExceptions() != null && !csvToBean.getCapturedExceptions()
					.isEmpty()) {
				log.warn("Found {} parsing errors while reading CSV", csvToBean.getCapturedExceptions()
						.size());
				csvToBean.getCapturedExceptions()
						.forEach(exception -> log.warn("CSV parsing error: {}", exception.getMessage()));
			}

			return records;
		} catch (Exception e) {
			String errorMessage = String.format("Failed to parse CSV for class: %s", clazz.getSimpleName());
			log.error(errorMessage, e);
			throw new CsvProcessingException(errorMessage, e);
		}
	}

	/**
	 * Reads CSV file with custom configuration
	 *
	 * @param filePath  Path to the CSV file
	 * @param clazz     Class type to convert CSV records to
	 * @param separator CSV field separator character
	 * @param skipLines Number of lines to skip at the beginning
	 * @param <T>       Type of the target class
	 * @return List of objects converted from CSV records
	 * @throws CsvProcessingException if reading fails
	 */
	public <T> List<T> readCsvFileWithConfig(String filePath, Class<T> clazz, char separator, int skipLines) {
		log.info("Reading CSV file: {} with separator: '{}' and skipLines: {}", filePath, separator, skipLines);

		try (Reader reader = new FileReader(filePath, StandardCharsets.UTF_8)) {
			CsvToBean<T> csvToBean = new CsvToBeanBuilder<T>(reader).withType(clazz)
					.withSeparator(separator)
					.withIgnoreLeadingWhiteSpace(true)
					.withIgnoreEmptyLine(true)
					.withSkipLines(skipLines)
					.build();

			List<T> records = csvToBean.parse();
			log.info("Successfully read {} records from CSV with custom config", records.size());

			return records;
		} catch (IOException e) {
			String errorMessage = String.format("Failed to read CSV file with custom config: %s", filePath);
			log.error(errorMessage, e);
			throw new CsvProcessingException(errorMessage, e);
		}
	}
}
