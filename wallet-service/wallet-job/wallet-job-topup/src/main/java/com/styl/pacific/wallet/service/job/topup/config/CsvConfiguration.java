/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.job.topup.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for CSV processing
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "pacific.config.csv")
public class CsvConfiguration {

	/**
	 * Default CSV field separator
	 */
	private char separator = ',';

	/**
	 * Default number of lines to skip at the beginning of CSV files
	 */
	private int skipLines = 0;

	/**
	 * Whether to ignore leading whitespace in CSV fields
	 */
	private boolean ignoreLeadingWhiteSpace = true;

	/**
	 * Whether to ignore empty lines in CSV files
	 */
	private boolean ignoreEmptyLine = true;

	/**
	 * Whether to apply quotes to all fields when writing CSV
	 */
	private boolean applyQuotesToAll = false;

	/**
	 * Maximum number of records to process in a single batch
	 */
	private int batchSize = 1000;

	/**
	 * Whether to validate CSV records before processing
	 */
	private boolean validateRecords = true;

	/**
	 * Whether to create backup of input files before processing
	 */
	private boolean createBackup = true;

	/**
	 * File encoding for CSV files
	 */
	private String encoding = "UTF-8";

	/**
	 * Date format pattern for CSV date fields
	 */
	private String dateFormat = "yyyy-MM-dd HH:mm:ss";
}
