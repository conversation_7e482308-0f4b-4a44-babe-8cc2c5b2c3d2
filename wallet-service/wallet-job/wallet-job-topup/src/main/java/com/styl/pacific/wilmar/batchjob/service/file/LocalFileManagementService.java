/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wilmar.batchjob.service.file;

import com.styl.pacific.wilmar.batchjob.config.FileConfiguration;
import com.styl.pacific.wilmar.batchjob.config.TopupConfiguration;
import com.styl.pacific.wilmar.batchjob.exception.FileProcessingException;
import java.io.File;
import java.io.IOException;
import java.nio.file.FileAlreadyExistsException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class LocalFileManagementService extends BaseFileManagementService {

	private static final Logger logger = LoggerFactory.getLogger(LocalFileManagementService.class);

	public LocalFileManagementService(TopupConfiguration topupConfiguration, FileConfiguration fileConfiguration) {
		super(topupConfiguration, fileConfiguration);
	}

	@Override
	public String copyFileToInputDirectory(String filePath) {
		File file = new File(filePath);
		if (!file.exists() || !file.isFile()) {
			throw new FileProcessingException("Input file does not exist");
		}

		File inputFolder = new File(fileConfiguration.getInputDirectory());
		if (!inputFolder.exists() && !inputFolder.isDirectory()) {
			throw new FileProcessingException("Input folder is not created");
		}

		String fileName = file.getName();
		Path destPath = inputFolder.toPath()
				.resolve(fileName);
		try {
			Files.copy(file.toPath(), destPath, StandardCopyOption.REPLACE_EXISTING);
		} catch (IOException e) {
			throw new FileProcessingException("Unable to copy file to input folder", e);
		}

		// Copy file from filePath to fileConfiguration.inputDirectory
		return destPath.toString();
	}

	@Override
	public void moveToProcessedDirectory(String outputFilePath) {
		if (StringUtils.isBlank(outputFilePath)) {
			throw new FileProcessingException("Processed directory is not set");
		}

		File processedDirectory = new File(fileConfiguration.getProcessedDirectory());
		if (!processedDirectory.exists() && !processedDirectory.mkdirs()) {
			throw new FileProcessingException("Error creating processed directory");
		}

		File outputFile = new File(outputFilePath);

		try {
			Files.move(Path.of(outputFilePath), processedDirectory.toPath()
					.resolve(outputFile.getName()));
		} catch (FileAlreadyExistsException e) {
			logger.error("Unable to move output file. File already exists in processed directory {}", outputFilePath);
		} catch (IOException e) {
			throw new FileProcessingException("Error moving file to processed directory", e);
		}
	}
}
