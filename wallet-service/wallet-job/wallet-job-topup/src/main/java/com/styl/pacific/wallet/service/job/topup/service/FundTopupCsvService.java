/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.job.topup.service;

import static com.styl.pacific.wallet.service.job.topup.model.FundProcessingResultCsvRecord.Status;

import com.opencsv.bean.HeaderColumnNameMappingStrategy;
import com.styl.pacific.common.feign.exception.FeignBadRequestException;
import com.styl.pacific.wallet.service.responses.transaction.WalletTransactionResponse;
import com.styl.pacific.wallet.service.job.topup.config.TopupConfiguration;
import com.styl.pacific.wallet.service.job.topup.exception.CsvProcessingException;
import com.styl.pacific.wallet.service.job.topup.exception.TopupException;
import com.styl.pacific.wallet.service.job.topup.model.FundProcessingResultCsvRecord;
import com.styl.pacific.wallet.service.job.topup.model.FundTopupCsvRecord;
import com.styl.pacific.wallet.service.job.topup.service.csv.CsvReaderService;
import com.styl.pacific.wallet.service.job.topup.service.csv.CsvWriterService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * Service for processing fund topup CSV files
 * Handles reading, writing, and validation of fund topup CSV records
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FundTopupCsvService {

	private final static Logger logger = LoggerFactory.getLogger(FundTopupCsvService.class);

	private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
	private final CsvReaderService csvReaderService;
	private final CsvWriterService csvWriterService;
	private final FundTopupService fundTopupService;

	private final TopupConfiguration topupConfiguration;

	/**
	 * Reads fund topup records from CSV file
	 *
	 * @param filePath Path to the CSV file
	 * @return List of fund topup records
	 * @throws CsvProcessingException if reading fails
	 */
	public List<FundTopupCsvRecord> readFundTopupRecords(String filePath) {
		log.info("Reading fund topup records from file: {}", filePath);

		List<FundTopupCsvRecord> records = csvReaderService.readCsvFile(filePath, FundTopupCsvRecord.class);

		log.info("Successfully read and validated {} fund topup records", records.size());
		return records;
	}

	/**
	 * Writes fund topup records to CSV file
	 *
	 * @param filePath Path to the output CSV file
	 * @param records  List of fund topup records to write
	 * @throws CsvProcessingException if writing fails
	 */
	public void writeFundTopupRecords(String filePath, List<FundProcessingResultCsvRecord> records) {
		log.info("Writing {} fund topup records to file: {}", records.size(), filePath);

		// Note: FundProcessingResultCsvRecord doesn't have timestamp fields in the current structure
		// If needed, add createdAt and updatedAt fields to the model

		HeaderColumnNameMappingStrategy<FundProcessingResultCsvRecord> mappingStrategy = new HeaderColumnNameMappingStrategy<>();
		mappingStrategy.setType(FundProcessingResultCsvRecord.class);
		mappingStrategy.setColumnOrderOnWrite(new Comparator<String>() {
			@Override
			public int compare(String o1, String o2) {
				return FundProcessingResultCsvRecord.HEADERS.indexOf(o1.toLowerCase())
						- FundProcessingResultCsvRecord.HEADERS.indexOf(o2.toLowerCase());
			}
		});

		csvWriterService.writeCsvFile(filePath, records, mappingStrategy);
		log.info("Successfully wrote {} fund topup records to file: {}", records.size(), filePath);
	}

	/**
	 * Processes fund topup records (read, validate, transform, write)
	 *
	 * @param inputFilePath  Path to input CSV file
	 * @param outputFilePath Path to output CSV file
	 * @return Number of processed records
	 * @throws CsvProcessingException if processing fails
	 */
	public void processFundTopupRecords(String inputFilePath, String outputFilePath) {
		log.info("Processing fund topup records from {} to {}", inputFilePath, outputFilePath);

		// Read records
		List<FundTopupCsvRecord> records = readFundTopupRecords(inputFilePath);

		// Transform/process records if needed
		List<FundProcessingResultCsvRecord> processedRecords = processTopupRecords(records);

		// Write processed records
		writeFundTopupRecords(outputFilePath, processedRecords);

		log.info("Successfully processed {} fund topup records", processedRecords.size());
	}

	/**
	 * Transforms/processes fund topup records
	 * Override this method to add custom business logic
	 *
	 * @param records List of records to transform
	 * @return List of transformed records
	 */
	protected List<FundProcessingResultCsvRecord> processTopupRecords(List<FundTopupCsvRecord> records) {
		log.debug("Transforming {} fund topup records", records.size());

		// Add any transformation logic here
		// For now, just return the records as-is
		return records.stream()
				.map(this::processSingleTopupRecord)
				.collect(Collectors.toList());
	}

	/**
	 * Transforms a single fund topup record
	 *
	 * @param record Record to transform
	 * @return Transformed record
	 */
	private FundProcessingResultCsvRecord processSingleTopupRecord(FundTopupCsvRecord record) {
		try {
			transformRecord(record);
			validateFundTopupRecord(record);

			if (record.getAmount()
					.compareTo(BigDecimal.ZERO) == 0) {
				return new FundProcessingResultCsvRecord(record, Status.SKIPPED, "Amount is zero");
			}

			WalletTransactionResponse transaction = fundTopupService.proceedTopup(record);
			BigDecimal amount = new BigDecimal(transaction.getAmount()).movePointLeft(topupConfiguration.getCurrency()
					.getDefaultFractionDigits())
					.setScale(2, RoundingMode.DOWN);

			return new FundProcessingResultCsvRecord(record, Status.SUCCESS, amount, transaction.getTransactionId());
		} catch (CsvProcessingException e) {
			return new FundProcessingResultCsvRecord(record, Status.FAILED, e.getMessage());
		} catch (FeignBadRequestException e) {
			logger.error("Error processing record: {}\n{}\n{}", record.getEmail(), e.getError()
					.getMessage(), e.getError()
							.getDetails());
			return new FundProcessingResultCsvRecord(record, Status.FAILED, e.getError()
					.getMessage());
		} catch (TopupException e) {
			logger.error("Error processing record: {}", record.getEmail(), e);
			return new FundProcessingResultCsvRecord(record, Status.FAILED, e.getMessage());
		} catch (Exception e) {
			logger.error("Error processing record: {}", record.getEmail(), e);
			return new FundProcessingResultCsvRecord(record, FundProcessingResultCsvRecord.Status.FAILED,
					"Unexpected error");
		}
	}

	private static void transformRecord(FundTopupCsvRecord record) {
		// Add any single record transformation logic here
		// For example: normalize email, trim whitespace, etc.
		if (record.getEmail() != null) {
			record.setEmail(record.getEmail()
					.toLowerCase()
					.trim());
		}

		if (record.getParentEmail() != null) {
			record.setParentEmail(record.getParentEmail()
					.toLowerCase()
					.trim());
		}

		if (record.getFirstName() != null) {
			record.setFirstName(record.getFirstName()
					.trim());
		}

		if (record.getLastName() != null) {
			record.setLastName(record.getLastName()
					.trim());
		}

		if (record.getGroupName() != null) {
			record.setGroupName(record.getGroupName()
					.trim());
		}

		if (record.getAmount() != null) {
			record.setAmount(record.getAmount()
					.setScale(2, RoundingMode.DOWN));
		}
	}

	private void validateFundTopupRecord(FundTopupCsvRecord record) {
		List<String> errors = new ArrayList<>();

		// email is required and valid
		if (StringUtils.isBlank(record.getEmail())) {
			errors.add("Email is required");
		} else if (!EMAIL_PATTERN.matcher(record.getEmail())
				.matches()) {
			errors.add("Invalid email format");
		}

		// parent email is required and valid
		if (topupConfiguration.isRequiredParentEmail() && StringUtils.isBlank(record.getParentEmail())) {
			errors.add("Parent email is required");
		} else if (StringUtils.isNotBlank(record.getParentEmail()) && !EMAIL_PATTERN.matcher(record.getParentEmail())
				.matches()) {
			errors.add("Invalid parent email format");
		}

		// amount is required
		if (record.getAmount() == null) {
			errors.add("Amount is required");
		} else if (!topupConfiguration.isAllowDeduction() && record.getAmount()
				.compareTo(BigDecimal.ZERO) < 0) {
			logger.debug("Amount: {}", record.getAmount());
			logger.debug("topupConfiguration.isAllowDeduction(): {}", topupConfiguration.isAllowDeduction());
			errors.add("Amount must not be negative");
		} else if (!topupConfiguration.isAllowZeroAmount() && record.getAmount()
				.compareTo(BigDecimal.ZERO) == 0) {
			errors.add("Amount must not be zero");
		}

		if (!errors.isEmpty()) {
			throw new CsvProcessingException(Strings.join(errors, ';'));
		}
	}

}
