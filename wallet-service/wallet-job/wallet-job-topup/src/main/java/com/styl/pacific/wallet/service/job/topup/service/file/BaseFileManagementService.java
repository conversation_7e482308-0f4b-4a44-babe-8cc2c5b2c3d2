/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.job.topup.service.file;

import com.styl.pacific.wallet.service.job.topup.config.FileConfiguration;
import com.styl.pacific.wallet.service.job.topup.config.TopupConfiguration;
import com.styl.pacific.wallet.service.job.topup.exception.FileProcessingException;

import java.io.File;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

@NoArgsConstructor
@RequiredArgsConstructor
public abstract class BaseFileManagementService implements FileManagementService {

    protected final TopupConfiguration topupConfiguration;
    protected final FileConfiguration fileConfiguration;


    @Override
    public void ensureDirectoriesExist() {
        try {
            if (fileConfiguration.getInputDirectory() != null) {
                File inputDirectory = new File(fileConfiguration.getInputDirectory());
                if (!inputDirectory.exists() && !inputDirectory.mkdirs()) {
                    throw new FileProcessingException("Error creating input directory");
                }
            } else {
                throw new FileProcessingException("Input directory is not set");
            }

            if (fileConfiguration.getOutputDirectory() != null) {
                File outputDirectory = new File(fileConfiguration.getOutputDirectory());
                if (!outputDirectory.exists() && !outputDirectory.mkdirs()) {
                    throw new FileProcessingException("Error creating output directory");
                }
            } else {
                throw new FileProcessingException("Output directory is not set");
            }
        } catch (Exception e) {
            throw new FileProcessingException("Error creating directories", e);
        }
    }

    // Generate output file name YYYYMMDD_topup_result.csv
    @Override
    public String getOutputFileName() {
        ZonedDateTime now = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of(topupConfiguration.getTimeZone()));
        return String.format("%s_%s", now.format(DateTimeFormatter.ofPattern("yyyyMMdd")), fileConfiguration
                .getOutputFileSuffix());
    }
}
