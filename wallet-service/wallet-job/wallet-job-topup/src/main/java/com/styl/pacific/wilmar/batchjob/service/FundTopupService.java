/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wilmar.batchjob.service;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.user.shared.http.subaccounts.request.FilterSubAccountRequest;
import com.styl.pacific.user.shared.http.subaccounts.request.QuerySubAccountPaginationRequest;
import com.styl.pacific.user.shared.http.subaccounts.response.SubAccountResponse;
import com.styl.pacific.user.shared.http.users.request.QueryUserPaginationRequest;
import com.styl.pacific.user.shared.http.users.request.QueryUserRequest;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import com.styl.pacific.wallet.service.requests.wallet.AddBalanceRequest;
import com.styl.pacific.wallet.service.requests.wallet.DeductBalanceRequest;
import com.styl.pacific.wallet.service.requests.wallet.NotificationControlRequest;
import com.styl.pacific.wallet.service.responses.transaction.WalletTransactionResponse;
import com.styl.pacific.wallet.service.responses.wallet.WalletResponse;
import com.styl.pacific.wilmar.batchjob.config.TopupConfiguration;
import com.styl.pacific.wilmar.batchjob.exception.TopupException;
import com.styl.pacific.wilmar.batchjob.model.FundTopupCsvRecord;
import com.styl.pacific.wilmar.batchjob.service.rest.SubAccountClient;
import com.styl.pacific.wilmar.batchjob.service.rest.UserClient;
import com.styl.pacific.wilmar.batchjob.service.rest.WalletClient;
import jakarta.annotation.PostConstruct;
import java.math.BigInteger;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Currency;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FundTopupService {

	private final UserClient userClient;
	private final SubAccountClient subAccountClient;
	private final WalletClient walletClient;

	private final TopupConfiguration topupConfiguration;

	private Currency currency;

	private String description;

	@PostConstruct
	public void init() {
		ZonedDateTime now = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of(topupConfiguration.getTimeZone()));
		description = String.format("Scheduled Topup on %s", now.format(DateTimeFormatter.ofPattern(
				"EEE, dd MMM yyyy")));
		//        currency = Currency.getInstance(topupConfiguration.getCurrency());

	}

	public WalletTransactionResponse proceedTopup(FundTopupCsvRecord record) {
		WalletResponse wallet = validateUserProfileAndWallet(record);

		long walletId = Long.parseLong(wallet.getWalletId());
		BigInteger amount = record.getAmount()
				.movePointRight(topupConfiguration.getCurrency()
						.getDefaultFractionDigits())
				.toBigInteger();

		if (amount.compareTo(BigInteger.ZERO) > 0) {
			return topup(walletId, amount, description, Instant.now()
					.plus(topupConfiguration.getFundExpiration())
					.toEpochMilli());
		}
		return deductFund(walletId, amount.negate());
	}

	private WalletTransactionResponse deductFund(long walletId, BigInteger amount) {
		if (!topupConfiguration.isAllowNegativeBalance()) {
			WalletResponse wallet = walletClient.getWallet(walletId);

			if (wallet.getBalance()
					.compareTo(amount) < 0) {
				amount = wallet.getBalance();
			}
		}
		DeductBalanceRequest request = DeductBalanceRequest.builder()
				.amount(amount)
				.build();
		return walletClient.deductBalance(request, walletId);
	}

	public WalletResponse validateUserProfileAndWallet(FundTopupCsvRecord record) {

		UserResponse user = getUserByEmail(record.getEmail());
		Objects.requireNonNull(user);
		Long userId = Long.parseLong(user.getId());

		// If parent email is presented, check if sub-account belongs to the parent
		if (StringUtils.isNotBlank(record.getParentEmail())) {
			UserResponse parent = getUserByEmail(record.getParentEmail());
			Objects.requireNonNull(parent);

			List<SubAccountResponse> subAccountResponses = subAccountClient.querySubAccounts(Long.parseLong(parent
					.getId()), QuerySubAccountPaginationRequest.builder()
							.filter(FilterSubAccountRequest.builder()
									.bySubUserId(userId)
									.build())
							.build())
					.getContent();
			if (subAccountResponses.isEmpty()) {
				throw new TopupException("Parent for this user not found");
			}
		}

		return walletClient.getWalletByCustomerId(userId);
	}

	public WalletTransactionResponse topup(Long walletId, BigInteger amount, String description, Long expiresOn) {
		NotificationControlRequest notificationControl = NotificationControlRequest.builder()
				.build();
		AddBalanceRequest request = AddBalanceRequest.builder()
				.amount(amount)
				.description(description)
				.expiresOn(expiresOn)
				.build();
		return walletClient.addBalance(request, walletId, notificationControl);
	}

	private UserResponse getUserByEmail(String email) {
		QueryUserPaginationRequest request = QueryUserPaginationRequest.builder()
				.filter(QueryUserRequest.builder()
						.byEmail(email)
						.build())
				.build();
		Paging<UserResponse> response = userClient.queryUsers(request);
		if (response.getTotalElements() == 0) {
			throw new TopupException("User not found");
		} else if (response.getTotalElements() > 1) {
			throw new TopupException("Multiple users found");
		}
		return response.getContent()
				.getFirst();
	}

}
