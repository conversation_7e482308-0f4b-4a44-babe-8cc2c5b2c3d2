/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wilmar.batchjob;

import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.wilmar.batchjob.config.TopupConfiguration;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
@Primary
public class TenantIdHeaderSetterInterceptor implements RequestInterceptor {

	private final TopupConfiguration topupConfiguration;

	@Override
	public void apply(RequestTemplate template) {
		template.header(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, String.valueOf(topupConfiguration
				.getTenantId()));
	}
}
