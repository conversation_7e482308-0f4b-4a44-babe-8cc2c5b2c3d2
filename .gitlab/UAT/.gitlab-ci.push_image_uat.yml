.push_image_uat:
  extends: .copy_images
  stage: push-image-uat
  variables:
    AWS_ACCESS_KEY_ID: ${ACCESS_KEY_ID_UAT}
    AWS_SECRET_ACCESS_KEY: ${SECRET_KEY_ID_UAT}
    AWS_ACCOUNT_ID: ${AWS_ACCOUNT_ID_UAT}
    AWS_ECR_URL: ${AWS_ECR_UAT}

push_image_backoffice_ui_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/frontend/backoffice-ui"
    AWS_ECR_IMAGE: "pacific-ii-backoffice-ui"
  needs: [ "build_image_backoffice_ui" ]

push_image_customer_portal_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/frontend/customer-portal"
    AWS_ECR_IMAGE: "pacific-ii-customer-portal"
  needs: [ "build_image_customer_portal" ]

push_image_tenant_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/backend/tenant"
    AWS_ECR_IMAGE: "pacific-ii-tenant"
  needs: [ "build_tenant_service_image" ]

push_image_user_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/backend/user"
    AWS_ECR_IMAGE: "pacific-ii-user"
  needs: [ "build_user_service_image" ]

push_image_catalog_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/backend/catalog"
    AWS_ECR_IMAGE: "pacific-ii-catalog"
  needs: [ "build_catalog_service_image" ]

push_image_store_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/backend/store"
    AWS_ECR_IMAGE: "pacific-ii-store"
  needs: [ "build_store_service_image" ]

push_image_core_gw_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/backend/gateway"
    AWS_ECR_IMAGE: "pacific-ii-gateway"
  needs: [ "build_core_gw_service_image" ]

push_image_device_gw_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/backend/aggregator"
    AWS_ECR_IMAGE: "pacific-ii-device-aggregator"
  needs: [ "build_device_aggregator_image" ]

push_image_auth_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/backend/authorization"
    AWS_ECR_IMAGE: "pacific-ii-authorization"
  needs: [ "build_auth_service_image" ]

push_image_notification_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/backend/notification"
    AWS_ECR_IMAGE: "pacific-ii-notification"
  needs: [ "build_notification_service_image" ]

push_image_order_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/backend/order"
    AWS_ECR_IMAGE: "pacific-ii-order"
  needs: [ "build_order_service_image" ]

push_image_payment_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/backend/payment"
    AWS_ECR_IMAGE: "pacific-ii-payment"
  needs: [ "build_payment_service_image" ]

push_image_utility_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/backend/utility"
    AWS_ECR_IMAGE: "pacific-ii-utility"
  needs: [ "build_utility_service_image" ]

push_image_wallet_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/backend/wallet"
    AWS_ECR_IMAGE: "pacific-ii-wallet"
  needs: [ "build_wallet_service_image" ]

push_image_lambda_datasync_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/lambda/lambda-datasync"
    AWS_ECR_IMAGE: "pacific-ii-lambda-datasync"
  needs: [ "build_image_lambda_datasync" ]

push_image_sftp_adapter_uat:
  stage: push-image-uat
  extends: .push_image_uat
  variables:
    NEXUS_IMAGE: "repository/pacific-project/sftp/sftp-adapter"
    AWS_ECR_IMAGE: "pacific-ii-sftp-adapter"
  needs: [ "build_image_sftp_adapter" ]