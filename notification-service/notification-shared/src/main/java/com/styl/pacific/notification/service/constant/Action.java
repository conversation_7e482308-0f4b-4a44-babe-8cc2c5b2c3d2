/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Action {

	public static final String INVITE_USER = "INVITE_USER";

	public static final String WELCOME_USER = "WELCOME_USER";

	public static final String INVITE_USER_TO_ANOTHER_TENANT = "INVITE_USER_TO_ANOTHER_TENANT";

	public static final String ADD_SUB_ACCOUNT = "ADD_SUB_ACCOUNT";

	public static final String BALANCE_CHANGE = "BALANCE_CHANGE";

	public static final String PAYMENT_SUCCESS = "PAYMENT_SUCCESS";

	public static final String PAYMENT_REFUND = "PAYMENT_REFUND";

	public static final String TENANT_SUBMIT_REVIEW = "TENANT_SUBMIT_REVIEW";

	public static final String IMPORT_SUCCESS = "IMPORT_SUCCESS";

	public static final String IMPORT_FAIL = "IMPORT_FAIL";

	public static final String WELCOME_TENANT = "WELCOME_TENANT";

	public static final String DATA_SYNC_MONITOR = "DATA_SYNC_MONITOR";

	public static final String USER_FEEDBACK = "USER_FEEDBACK";

	public static final String ACCEPT_SUB_ACCOUNT_INVITATION = "ACCEPT_SUB_ACCOUNT_INVITATION";

	public static final String REJECT_SUB_ACCOUNT_INVITATION = "REJECT_SUB_ACCOUNT_INVITATION";

	public static final String UPDATE_ORDER_STATUS = "UPDATE_ORDER_STATUS";

	public static final String CONFIRM_PRE_ORDER = "CONFIRM_PRE_ORDER";

	public enum TemplateScope {
		INVITE_USER,
		WELCOME_USER,
		BALANCE_CHANGE,
		PAYMENT_SUCCESS
	}
}
