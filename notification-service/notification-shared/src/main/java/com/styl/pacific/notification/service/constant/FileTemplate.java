/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.constant;

import com.styl.pacific.notification.service.enums.NotificationChannel;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class FileTemplate {
	private static final Map<String, String> mapTemplateContentFile = new HashMap<>();
	private static final Map<String, String> mapTemplateSubjectFile = new HashMap<>();
	private static final Map<String, String> mapTemplateExtension = new HashMap<>();

	private static final Map<String, List<NotificationChannel>> mapActionChannels = new HashMap<>();

	public static final String TEMPLATE_FILE_EXTENSION = ".ftlh";

	static {
		mapTemplateContentFile.put(Action.INVITE_USER, "invite-user-body");
		mapTemplateSubjectFile.put(Action.INVITE_USER, "invite-user-subject");
		mapTemplateExtension.put(Action.INVITE_USER, TEMPLATE_FILE_EXTENSION);
		mapActionChannels.put(Action.INVITE_USER, List.of(NotificationChannel.EMAIL));

		mapTemplateContentFile.put(Action.WELCOME_USER, "welcome-user-body");
		mapTemplateSubjectFile.put(Action.WELCOME_USER, "welcome-user-subject");
		mapTemplateExtension.put(Action.WELCOME_USER, TEMPLATE_FILE_EXTENSION);
		mapActionChannels.put(Action.WELCOME_USER, List.of(NotificationChannel.EMAIL));

		mapTemplateContentFile.put(Action.ADD_SUB_ACCOUNT, "add-sub-account-body");
		mapTemplateSubjectFile.put(Action.ADD_SUB_ACCOUNT, "add-sub-account-subject");
		mapTemplateExtension.put(Action.ADD_SUB_ACCOUNT, TEMPLATE_FILE_EXTENSION);
		mapActionChannels.put(Action.ADD_SUB_ACCOUNT, List.of(NotificationChannel.EMAIL));

		mapTemplateContentFile.put(Action.BALANCE_CHANGE, "balance-change-body");
		mapTemplateSubjectFile.put(Action.BALANCE_CHANGE, "balance-change-subject");
		mapTemplateExtension.put(Action.BALANCE_CHANGE, TEMPLATE_FILE_EXTENSION);
		mapActionChannels.put(Action.BALANCE_CHANGE, List.of(NotificationChannel.EMAIL));

		mapTemplateContentFile.put(Action.PAYMENT_SUCCESS, "payment-success-body");
		mapTemplateSubjectFile.put(Action.PAYMENT_SUCCESS, "payment-success-subject");
		mapTemplateExtension.put(Action.PAYMENT_SUCCESS, TEMPLATE_FILE_EXTENSION);
		mapActionChannels.put(Action.PAYMENT_SUCCESS, List.of(NotificationChannel.EMAIL));

		mapTemplateContentFile.put(Action.USER_FEEDBACK, "user-feedback-body");
		mapTemplateSubjectFile.put(Action.USER_FEEDBACK, "user-feedback-subject");
		mapTemplateExtension.put(Action.USER_FEEDBACK, TEMPLATE_FILE_EXTENSION);
		mapActionChannels.put(Action.USER_FEEDBACK, List.of(NotificationChannel.EMAIL));

		mapTemplateContentFile.put(Action.UPDATE_ORDER_STATUS, "order-status-update-body");
		mapTemplateSubjectFile.put(Action.UPDATE_ORDER_STATUS, "order-status-update-subject");
		mapTemplateExtension.put(Action.UPDATE_ORDER_STATUS, TEMPLATE_FILE_EXTENSION);
		mapActionChannels.put(Action.UPDATE_ORDER_STATUS, List.of(NotificationChannel.EMAIL));

		mapTemplateContentFile.put(Action.CONFIRM_PRE_ORDER, "pre-order-confirm-body");
		mapTemplateSubjectFile.put(Action.CONFIRM_PRE_ORDER, "pre-order-confirm-subject");
		mapTemplateExtension.put(Action.CONFIRM_PRE_ORDER, TEMPLATE_FILE_EXTENSION);
		mapActionChannels.put(Action.CONFIRM_PRE_ORDER, List.of(NotificationChannel.EMAIL));
	}

	public static String getContentFileName(String action) {
		return mapTemplateContentFile.get(action);
	}

	public static String getSubjectFileName(String action) {
		return mapTemplateSubjectFile.get(action);
	}

	public static String getFileExtension(String action) {
		return mapTemplateExtension.get(action);
	}

	public static List<NotificationChannel> getChannels(String action) {
		return mapActionChannels.get(action);
	}
}
