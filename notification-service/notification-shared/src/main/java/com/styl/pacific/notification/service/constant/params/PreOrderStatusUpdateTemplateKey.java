/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.constant.params;

public class PreOrderStatusUpdateTemplateKey {
	public static String BUSINESS_NAME = "businessName";
	public static String TAX_REG_NO = "taxRegNo";
	public static String PRE_ORDER_ID = "preOrderId";
	public static String PRE_ORDER_NUMBER = "preOrderNumber";
	public static String DATE_OF_ISSUE = "dateOfIssue";
	public static String PAYMENT_STATUS = "paymentStatus";
	public static String PAYMENT_METHOD = "paymentMethod";
	public static String SUB_ORDER_COLLECTIONS = "subOrderCollections";
	public static String SUB_TOTAL_AMOUNT = "subtotalAmount";
	public static String DISCOUNT_AMOUNT = "discountAmount";
	public static String TAX_NAME = "taxName";
	public static String TAX_AMOUNT = "taxAmount";
	public static String TOTAL_AMOUNT = "totalAmount";
}
