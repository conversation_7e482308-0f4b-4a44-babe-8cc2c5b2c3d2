/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.utils;

import com.styl.pacific.notification.service.domain.exception.NotificationDomainException;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringWriter;
import java.io.Writer;

/**
 * <AUTHOR>
 */

public class TemplateRender {

	private TemplateRender() {
	}

	public static String render(String fileName, Object data, InputStream inputStream, Configuration freemarkerConfig) {
		try {
			Writer output = new StringWriter();
			Template template = new Template(fileName, new InputStreamReader(inputStream), freemarkerConfig);
			template.process(data, output);
			return output.toString();
		} catch (TemplateException | IOException e) {
			throw new NotificationDomainException("Render error ", e);
		}
	}
}
