/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.model.converter;

import com.styl.pacific.domain.enums.ModelConverterId;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ModelConverterManager {
	private final List<ModelConverter> dataModelConverters;

	public Optional<ModelConverter> getConverterById(String converterId) {
		if (converterId == null || ModelConverterId.valueOf(converterId) == null) {
			return Optional.empty();
		}
		ModelConverterId id = ModelConverterId.valueOf(converterId);
		return dataModelConverters.stream()
				.filter(converter -> converter.getConverterId()
						.equals(id))
				.findFirst();
	}
}
