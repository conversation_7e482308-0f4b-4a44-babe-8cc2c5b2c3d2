/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.hander.helper;

import com.styl.pacific.notification.service.constant.EmailNotificationKey;
import com.styl.pacific.notification.service.constant.FileTemplate;
import com.styl.pacific.notification.service.constant.LoaderType;
import com.styl.pacific.notification.service.constant.params.NotificationCommonKey;
import com.styl.pacific.notification.service.domain.dto.CreateNotificationCommand;
import com.styl.pacific.notification.service.domain.entity.Notification;
import com.styl.pacific.notification.service.domain.mapper.NotificationDataMapper;
import com.styl.pacific.notification.service.domain.model.converter.ModelConverter;
import com.styl.pacific.notification.service.domain.model.converter.ModelConverterManager;
import com.styl.pacific.notification.service.domain.output.dto.LoaderMetaData;
import com.styl.pacific.notification.service.domain.utils.TemplateRender;
import com.styl.pacific.notification.service.enums.TemplateLayout;
import freemarker.template.Configuration;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class CreateEmailNotificationHelper {

	private final Configuration freemarkerConfig;
	private final LoaderHelper loaderHelper;
	private final FilePathHelper filePathHelper;
	private final ModelConverterManager modelConverterManager;

	public Notification createEmailNotification(CreateNotificationCommand command) {

		String fileNameBody = FileTemplate.getContentFileName(command.getAction());
		String fileNameSubject = FileTemplate.getSubjectFileName(command.getAction());
		String extension = FileTemplate.getFileExtension(command.getAction());

		LoaderMetaData bodyMetaData = buildMetaData(command, fileNameBody, extension, TemplateLayout.BODY);
		LoaderMetaData subjectMetaData = buildMetaData(command, fileNameSubject, extension, TemplateLayout.SUBJECT);

		Map<String, String> data = new HashMap<>(command.getData());
		String body = renderBody(data, bodyMetaData, fileNameBody, command.getUserId());
		String subject = renderSubject(data, subjectMetaData, fileNameSubject);

		data.put(EmailNotificationKey.BODY, body);
		data.put(EmailNotificationKey.SUBJECT, subject);
		command.setData(data);
		return NotificationDataMapper.INSTANCE.createNotificationCommandToNotification(command);
	}

	private LoaderMetaData buildMetaData(CreateNotificationCommand command, String fileName, String extension,
			TemplateLayout layout) {
		return LoaderMetaData.builder()
				.tenantId(command.getTenantId())
				.channel(command.getChannel()
						.name())
				.action(command.getAction())
				.path(filePathHelper.getPath(command.getChannel()))
				.fileName(fileName)
				.extension(extension)
				.layout(layout)
				.build();
	}

	private String renderBody(Map<String, String> data, LoaderMetaData metaData, String fileName, Long userId) {
		InputStream inputStream = loaderHelper.loadInputStream(LoaderType.DB, metaData);
		Optional<ModelConverter> modelConverter = modelConverterManager.getConverterById(data.get(
				NotificationCommonKey.MODEL_CONVERTER_ID));
		if (modelConverter.isPresent()) {
			return TemplateRender.render(fileName, modelConverter.get()
					.convert(data, userId), inputStream, freemarkerConfig);
		}
		return TemplateRender.render(fileName, data, inputStream, freemarkerConfig);
	}

	private String renderSubject(Map<String, String> data, LoaderMetaData metaData, String fileName) {
		InputStream inputStream = loaderHelper.loadInputStream(LoaderType.DB, metaData);
		return TemplateRender.render(fileName, data, inputStream, freemarkerConfig);
	}
}
