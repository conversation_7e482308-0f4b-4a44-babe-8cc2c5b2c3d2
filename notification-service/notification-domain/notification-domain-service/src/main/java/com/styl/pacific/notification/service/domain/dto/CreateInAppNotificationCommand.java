/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.dto;

import com.styl.pacific.notification.service.enums.NotificationChannel;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public class CreateInAppNotificationCommand extends BaseNotificationCommand {
	private Long userId;
	private Long tenantId;
	private String title;
	private String action;
	private String content;
	private Map<String, String> data;
	private String source;

	private CreateInAppNotificationCommand(Builder builder) {
		setChannel(builder.channel);
		userId = builder.userId;
		tenantId = builder.tenantId;
		action = builder.action;
		content = builder.content;
		data = builder.data;
		source = builder.source;
		title = builder.title;
	}

	public static final class Builder {
		private NotificationChannel channel;
		private Long userId;
		private Long tenantId;
		private String action;
		private String content;
		private Map<String, String> data;
		private String source;
		private String title;

		private Builder() {
		}

		public static Builder newBuilder() {
			return new Builder();
		}

		public Builder channel(NotificationChannel channel) {
			this.channel = channel;
			return this;
		}

		public Builder userId(Long userId) {
			this.userId = userId;
			return this;
		}

		public Builder tenantId(Long tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder action(String action) {
			this.action = action;
			return this;
		}

		public Builder content(String content) {
			this.content = content;
			return this;
		}

		public Builder data(Map<String, String> data) {
			this.data = data;
			return this;
		}

		public Builder source(String source) {
			this.source = source;
			return this;
		}

		public Builder title(String title) {
			this.title = title;
			return this;
		}

		public CreateInAppNotificationCommand build() {
			return new CreateInAppNotificationCommand(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof CreateInAppNotificationCommand command))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(userId, command.userId) && Objects.equals(tenantId, command.tenantId) && Objects.equals(
				title, command.title) && Objects.equals(action, command.action) && Objects.equals(content,
						command.content) && Objects.equals(source, command.source) && Objects.equals(data,
								command.data);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), userId, tenantId, title, action, content, data, source);
	}
}
