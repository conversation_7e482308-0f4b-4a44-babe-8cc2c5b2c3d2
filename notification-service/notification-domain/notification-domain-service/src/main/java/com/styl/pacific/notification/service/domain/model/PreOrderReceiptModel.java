/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.model;

import com.styl.pacific.notification.service.requests.SubOrderCollectionRequest;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class PreOrderReceiptModel {
	private String businessName;
	private String taxRegNo;
	private String preOrderId;
	private String preOrderNumber;
	private String dateOfIssue;
	private String paymentStatus;
	private String paymentMethod;
	private Map<String, List<SubOrderCollectionRequest>> orderCollections;
	private String subTotalAmount;
	private String discountAmount;
	private String taxName;
	private String taxAmount;
	private String totalAmount;
}
