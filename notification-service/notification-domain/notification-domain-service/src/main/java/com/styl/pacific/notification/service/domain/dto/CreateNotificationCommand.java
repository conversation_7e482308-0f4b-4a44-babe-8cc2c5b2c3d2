/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.dto;

import com.styl.pacific.notification.service.enums.NotificationChannel;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */

@Getter
@Builder
@AllArgsConstructor
public class CreateNotificationCommand {
	private UUID id;
	private Long userId;
	private Long tenantId;
	private String action;
	private String source;
	private NotificationChannel channel;
	@Setter
	private Map<String, String> data;
	private Long createdAt;
	private Long expiresAt;
}
