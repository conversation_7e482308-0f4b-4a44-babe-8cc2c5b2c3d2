/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.model.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.styl.pacific.domain.enums.ModelConverterId;
import com.styl.pacific.notification.service.constant.params.PreOrderStatusUpdateTemplateKey;
import com.styl.pacific.notification.service.domain.model.PreOrderReceiptModel;
import com.styl.pacific.notification.service.domain.output.repository.UserPreferenceRepository;
import com.styl.pacific.user.shared.http.preferences.response.DateTimeFormatPreferenceDataResponse;
import com.styl.pacific.utils.json.JsonFormatUtil;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class PreOrderModelConverter implements ModelConverter<PreOrderReceiptModel> {
	private final UserPreferenceRepository userPreferenceRepository;
	private static final String DATE_OF_ISSUE_PATTERN = "EEEE, MMM dd yyyy 'at' HH:mm:ss";

	@Override
	public ModelConverterId getConverterId() {
		return ModelConverterId.PRE_ORDER_CONVERTER;
	}

	@Override
	public PreOrderReceiptModel convert(Map<String, String> data, Long userId) {
		DateTimeFormatPreferenceDataResponse dateTimeFormat = userPreferenceRepository.getDateTimeFormatUserPreference(
				userId);
		DateTimeFormatter dateOfIssueFormatter = DateTimeFormatter.ofPattern(DATE_OF_ISSUE_PATTERN)
				.withZone(ZoneId.of(dateTimeFormat.getTimeZone()
						.getZoneId()));
		Instant dateOfIssue = Instant.ofEpochMilli(Long.parseLong(data.get(
				PreOrderStatusUpdateTemplateKey.DATE_OF_ISSUE)));

		return PreOrderReceiptModel.builder()
				.businessName(data.get(PreOrderStatusUpdateTemplateKey.BUSINESS_NAME))
				.taxRegNo(data.get(PreOrderStatusUpdateTemplateKey.TAX_REG_NO))
				.dateOfIssue(data.get(PreOrderStatusUpdateTemplateKey.DATE_OF_ISSUE))
				.preOrderId(data.get(PreOrderStatusUpdateTemplateKey.PRE_ORDER_ID))
				.preOrderNumber(data.get(PreOrderStatusUpdateTemplateKey.PRE_ORDER_NUMBER))
				.dateOfIssue(dateOfIssueFormatter.format(dateOfIssue))
				.paymentStatus(data.get(PreOrderStatusUpdateTemplateKey.PAYMENT_STATUS))
				.paymentMethod(data.get(PreOrderStatusUpdateTemplateKey.PAYMENT_METHOD))
				.orderCollections(JsonFormatUtil.convertToMap(data.get(
						PreOrderStatusUpdateTemplateKey.SUB_ORDER_COLLECTIONS), new TypeReference<>() {
						}))
				.subTotalAmount(data.get(PreOrderStatusUpdateTemplateKey.SUB_TOTAL_AMOUNT))
				.discountAmount(data.get(PreOrderStatusUpdateTemplateKey.DISCOUNT_AMOUNT))
				.taxName(data.get(PreOrderStatusUpdateTemplateKey.TAX_NAME))
				.taxAmount(data.get(PreOrderStatusUpdateTemplateKey.TAX_AMOUNT))
				.totalAmount(data.get(PreOrderStatusUpdateTemplateKey.TOTAL_AMOUNT))
				.build();
	}
}
