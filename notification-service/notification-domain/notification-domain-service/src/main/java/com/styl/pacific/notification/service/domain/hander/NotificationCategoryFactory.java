/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.hander;

import com.styl.pacific.notification.service.constant.Action;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class NotificationCategoryFactory {
	private UserNotificationHandler userNotificationHandler;
	private UserPreferenceNotificationHandler userPreferenceNotificationHandler;

	public NotificationCategoryHandler getNotificationCategory(String action) {
		return switch (action) {
		case Action.BALANCE_CHANGE, Action.PAYMENT_SUCCESS, Action.UPDATE_ORDER_STATUS, Action.CONFIRM_PRE_ORDER ->
			userPreferenceNotificationHandler;
		default -> userNotificationHandler;
		};
	}
}
