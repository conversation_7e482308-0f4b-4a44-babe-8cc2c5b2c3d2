/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.hander.helper;

import com.styl.pacific.notification.service.constant.Action;
import com.styl.pacific.notification.service.constant.EmailNotificationKey;
import com.styl.pacific.notification.service.constant.params.BalanceChangeTemplateKey;
import com.styl.pacific.notification.service.constant.params.OrderStatusUpdateTemplateKey;
import com.styl.pacific.notification.service.constant.params.PaymentSuccessTemplateKey;
import com.styl.pacific.notification.service.domain.entity.dto.CustomerDto;
import com.styl.pacific.notification.service.domain.output.repository.UserPreferenceRepository;
import com.styl.pacific.notification.service.domain.output.repository.UserRepository;
import com.styl.pacific.user.shared.http.preferences.response.DateTimeFormatPreferenceDataResponse;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class UserPreferenceHelper {

	private final UserPreferenceRepository userPreferenceRepository;

	private final UserRepository userRepository;

	public boolean isValidChannel(Long customerId, NotificationChannel channel, Map<String, String> data) {
		// todo add InApp + Push Notification later, prevent redundant data
		return switch (channel) {
		case NotificationChannel.EMAIL -> hasEmail(customerId, data);
		case NotificationChannel.IN_APP -> userRepository.getCustomerById(customerId) != null;
		default -> false;
		};
	}

	private boolean hasEmail(Long customerId, Map<String, String> data) {

		if (data.containsKey(EmailNotificationKey.TO) && StringUtils.hasText(data.get(EmailNotificationKey.TO))) {
			return true;
		}

		CustomerDto customerDto = userRepository.getCustomerById(customerId);

		if ((!data.containsKey(EmailNotificationKey.TO) || !StringUtils.hasText(data.get(EmailNotificationKey.TO)))
				&& StringUtils.hasText(customerDto.getEmail())) {
			data.put(EmailNotificationKey.TO, customerDto.getEmail());
			return true;
		}
		return false;
	}

	public void formatDateTime(Map<String, String> data, String action, Long customerId) {
		DateTimeFormatPreferenceDataResponse dateTimeFormat = userPreferenceRepository.getDateTimeFormatUserPreference(
				customerId);
		switch (action) {
		case Action.BALANCE_CHANGE:
			format(List.of(BalanceChangeTemplateKey.DATE_CHANGE), data, dateTimeFormat);
			break;
		case Action.PAYMENT_SUCCESS:
			format(List.of(PaymentSuccessTemplateKey.PAYMENT_DATE), data, dateTimeFormat);
			break;
		case Action.UPDATE_ORDER_STATUS:
			format(List.of(OrderStatusUpdateTemplateKey.UPDATED_DATE), data, dateTimeFormat);
			break;
		default:
			break;
		}
	}

	private void format(List<String> keys, Map<String, String> data,
			DateTimeFormatPreferenceDataResponse dateTimeFormat) {
		keys.forEach(key -> {
			if (data.containsKey(key) && StringUtils.hasText(data.get(key))) {

				final var newValue = DateTimeFormatter.ofPattern("%s %s".formatted(dateTimeFormat.getDateFormat(),
						dateTimeFormat.getTimeFormat()))
						.format(Instant.ofEpochMilli(Long.parseLong(data.get(key)))
								.atZone(ZoneId.of(dateTimeFormat.getTimeZone()
										.getZoneId())));
				data.put(key, newValue);
			}
		});

	}
}
