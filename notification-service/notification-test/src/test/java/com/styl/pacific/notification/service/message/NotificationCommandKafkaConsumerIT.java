/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.message;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.common.test.container.DynamoDbContainerTest;
import com.styl.pacific.common.test.container.KafkaContainerTest;
import com.styl.pacific.domain.dto.TimezoneResponse;
import com.styl.pacific.domain.enums.ModelConverterId;
import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.kafka.notification.avro.model.NotificationAvroChannel;
import com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent;
import com.styl.pacific.kafka.producer.service.KafkaProducer;
import com.styl.pacific.notification.service.config.IntegrationTestConfiguration;
import com.styl.pacific.notification.service.constant.Action;
import com.styl.pacific.notification.service.constant.params.NotificationCommonKey;
import com.styl.pacific.notification.service.constant.params.PreOrderStatusUpdateTemplateKey;
import com.styl.pacific.notification.service.domain.entity.EmailNotification;
import com.styl.pacific.notification.service.domain.output.sender.MailSender;
import com.styl.pacific.notification.service.requests.SubOrderCollectionRequest;
import com.styl.pacific.user.shared.enums.UserPreferenceKey;
import com.styl.pacific.user.shared.http.preferences.enums.NotificationChannel;
import com.styl.pacific.user.shared.http.preferences.response.DateTimeFormatPreferenceDataResponse;
import com.styl.pacific.user.shared.http.preferences.response.NotificationPreferenceDataResponse;
import com.styl.pacific.user.shared.http.preferences.response.UserPreferenceResponse;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import com.styl.pacific.utils.json.JsonFormatUtil;
import java.math.BigInteger;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import org.apache.commons.text.WordUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.kafka.support.SendResult;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@DirtiesContext
@ExtendWith(SpringExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
public class NotificationCommandKafkaConsumerIT extends BaseWebClientWithDbTest implements KafkaContainerTest,
		DynamoDbContainerTest {

	private DateTimeFormatter collectionDateTimeFormatter = DateTimeFormatter.ofPattern("EEEE, dd/MM/yyyy");
	private static final String NOTIFICATION_SOURCE = "order-service";
	@Autowired
	private KafkaProducer<UUID, NotificationCreatedAvroEvent> producer;

	@Autowired
	@Qualifier("userService")
	private WireMockServer userServiceMock;

	@Value(value = "${pacific.kafka.notification-service.consumers.notification-event.topic-name}")
	private String TOPIC;

	@MockitoSpyBean
	private MailSender mailSender;
	@Autowired
	private ObjectMapper objectMapper;

	@AfterEach
	public void setup() throws JsonProcessingException {

	}

	@Test
	@Order(0)
	void shouldSendConfirmationEmail_whenPreOrderConfirms() throws ExecutionException, InterruptedException,
			JsonProcessingException {

		TimezoneResponse timezoneResponse = new TimezoneResponse();
		timezoneResponse.setZoneId("Asia/Ho_Chi_Minh");
		timezoneResponse.setGtmOffset("GMT+07:00");
		timezoneResponse.setDisplayName("(GMT+07:00) Asia/Ho_Chi_Minh");
		UserPreferenceResponse response = UserPreferenceResponse.builder()
				.data(DateTimeFormatPreferenceDataResponse.builder()
						.timeZone(timezoneResponse)
						.build())
				.build();
		userServiceMock.stubFor(WireMock.get(WireMock.urlPathEqualTo(
				"/api/user/users/1/preferences/ACCOUNT_DATE_TIME_FORMAT/key"))
				.withQueryParam("isUsedTemplateIfNonExisted", WireMock.equalTo("true"))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(response))));

		UserPreferenceResponse responsePref = UserPreferenceResponse.builder()
				.data(NotificationPreferenceDataResponse.builder()
						.key(UserPreferenceKey.NOTIFICATION_PRE_ORDER_CONFIRMED)
						.channels(Set.of(NotificationChannel.EMAIL))
						.build())
				.build();

		userServiceMock.stubFor(WireMock.get(WireMock.urlPathEqualTo(
				"/api/user/users/1/preferences/NOTIFICATION_PRE_ORDER_CONFIRMED/key"))
				.withQueryParam("isUsedTemplateIfNonExisted", WireMock.equalTo("true"))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(responsePref))));

		UserResponse userResponse = UserResponse.builder()
				.id("1")
				.email("<EMAIL>")
				.build();
		userServiceMock.stubFor(WireMock.get(WireMock.urlPathEqualTo("/api/user/users/1/profile"))
				.withQueryParam("fetchPermissions", WireMock.equalTo("true"))
				.withQueryParam("countSponsors", WireMock.equalTo("true"))
				.withQueryParam("countSubAccounts", WireMock.equalTo("true"))
				.withQueryParam("alternativeUserAccess", WireMock.equalTo("false"))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(userResponse))));

		doNothing().when(mailSender)
				.sendMail(any(EmailNotification.class));

		Instant instant = Instant.now();
		ZoneId zone = ZoneId.systemDefault();

		LocalDate localDate = instant.atZone(zone)
				.toLocalDate();

		Map<String, List<SubOrderCollectionRequest>> convertedSubOrderMap = new LinkedHashMap<>();

		convertedSubOrderMap.put(localDate.minus(2, ChronoUnit.DAYS)
				.format(collectionDateTimeFormatter)
				.toString(), List.of(SubOrderCollectionRequest.builder()
						.mealTimeName("Breakfast")
						.totalAmount(BigInteger.TEN)
						.build(), SubOrderCollectionRequest.builder()
								.mealTimeName("Afternoon")
								.totalAmount(BigInteger.TEN)
								.build()));
		convertedSubOrderMap.put(localDate.minus(1, ChronoUnit.DAYS)
				.format(collectionDateTimeFormatter)
				.toString(), List.of(SubOrderCollectionRequest.builder()
						.mealTimeName("Lunch")
						.totalAmount(BigInteger.TEN)
						.build(), SubOrderCollectionRequest.builder()
								.mealTimeName("Dinner")
								.totalAmount(BigInteger.TEN)
								.build()));

		Map<String, String> expectedData = Map.ofEntries(Map.entry(NotificationCommonKey.MODEL_CONVERTER_ID,
				ModelConverterId.PRE_ORDER_CONVERTER.name()), Map.entry(PreOrderStatusUpdateTemplateKey.BUSINESS_NAME,
						"Test Business"), Map.entry(PreOrderStatusUpdateTemplateKey.TAX_REG_NO, "*********"), Map.entry(
								PreOrderStatusUpdateTemplateKey.PRE_ORDER_ID, "123456"), Map.entry(
										PreOrderStatusUpdateTemplateKey.PRE_ORDER_NUMBER, "ABCXYZ"), Map.entry(
												PreOrderStatusUpdateTemplateKey.DATE_OF_ISSUE, String.valueOf(instant
														.toEpochMilli())), Map.entry(
																PreOrderStatusUpdateTemplateKey.PAYMENT_STATUS,
																WordUtils.capitalizeFully(OrderPaymentStatus.PAID
																		.name())), Map.entry(
																				PreOrderStatusUpdateTemplateKey.PAYMENT_METHOD,
																				"Credit Card"), Map.entry(
																						PreOrderStatusUpdateTemplateKey.SUB_ORDER_COLLECTIONS,
																						JsonFormatUtil.convertToString(
																								convertedSubOrderMap)),
				Map.entry(PreOrderStatusUpdateTemplateKey.SUB_TOTAL_AMOUNT, String.valueOf(40)), Map.entry(
						PreOrderStatusUpdateTemplateKey.DISCOUNT_AMOUNT, String.valueOf(5)), Map.entry(
								PreOrderStatusUpdateTemplateKey.TAX_NAME, "GST"), Map.entry(
										PreOrderStatusUpdateTemplateKey.TAX_AMOUNT, String.valueOf(5)), Map.entry(
												PreOrderStatusUpdateTemplateKey.TOTAL_AMOUNT, String.valueOf(40)));

		NotificationCreatedAvroEvent notificationCreatedAvroEvent = NotificationCreatedAvroEvent.newBuilder()
				.setId(UUID.randomUUID())
				.setAction(Action.CONFIRM_PRE_ORDER)
				.setChannels(List.of(NotificationAvroChannel.EMAIL))
				.setCreatedAt(Instant.now()
						.toEpochMilli())
				.setSource(NOTIFICATION_SOURCE)
				.setData(expectedData)
				.setUserId(1L)
				.setTenantId(2L)
				.build();

		// Act
		CompletableFuture<SendResult<UUID, NotificationCreatedAvroEvent>> completableFuture = new CompletableFuture<>();
		completableFuture.orTimeout(5000, TimeUnit.MILLISECONDS);
		producer.send(TOPIC, notificationCreatedAvroEvent.getId(), notificationCreatedAvroEvent, (
				uuidEventAvroModelSendResult, throwable) -> {
			if (throwable != null) {
				completableFuture.completeExceptionally(throwable);

			} else {
				completableFuture.complete(uuidEventAvroModelSendResult);
			}
		});

		// Assert
		assertNotNull(completableFuture.get());

		ArgumentCaptor<EmailNotification> captor = ArgumentCaptor.forClass(EmailNotification.class);
		verify(mailSender, timeout(5000).times(1)).sendMail(captor.capture());
		EmailNotification emailNotification = captor.getValue();
		assertNotNull(emailNotification);
		assertEquals(emailNotification.getSubject(), "Your Pre-Order is Confirmed!");
		assertNotNull(emailNotification.getBody()
				.contains(expectedData.get(PreOrderStatusUpdateTemplateKey.BUSINESS_NAME)));
	}
}
