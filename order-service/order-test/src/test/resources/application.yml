server:
  port: 9205

spring:
  application:
    name: order-service
    version: '@project.version@'
    author: <EMAIL>
  output.ansi.enabled: always
  sql:
    init:
      platform: postgres
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  datasource:
    url: ********************************************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
  flyway:
    enabled: true
    baselineOnMigrate: true
    validateOnMigrate: true
    locations: classpath:db/migration

  kafka:


pacific:
  clients:
    tenant-service:
      url: http://localhost:9201
    user-service:
      url: http://localhost:9202
    store-service:
      url: http://localhost:9204
    payment-service:
      url: http://localhost:9206
  tracing:
    otlp:
      endpoint: http://localhost:4317
  aws:
    s3:
      endpoint: http://localhost:9000
      region: ap-southeast-1
      accessKey: test
      secretKey: test
    dynamodb:
      endpoint: http://localhost:8000/
      accessKey: key
      secretKey: key2
      region: eu-west-2
      init-table:
        enable: false
        packageName: com.styl.pacific.order.service.data.access.dynamodb
  kafka:
    order-service:
      consumers:
        retry-attempts: 3
        retry-interval-ms: 3000
        payment-settlement-created-event:
          enabled: true
          group-id: order-service-payment-settlement-created-event
          topic-name: payment-service-payment-settlement-created-event
          retry-attempts: 5
          retry-interval-ms: 3500
        payment-reversed-event:
          enabled: true
          group-id: order-service-payment-reversed-event
          topic-name: payment-service-payment-reversed-event
          retry-attempts: 5
          retry-interval-ms: 500

      producers:
        reserve-inventory-command:
          topic-name: order-service-reserve-inventory-command
        order-changed-event:
          topic-name: order-service-order-changed-event
        payment-reversal-command:
          topic-name: payment-service-payment-reversal-command

      notification-event:
        topic-name: notification-service-notification-command

kafka-config:
  bootstrap-servers: "localhost:19092, localhost:29092, localhost:39092"
  schema-registry-url-key: schema.registry.url
  schema-registry-url: mock://testurl
  num-of-partitions: 3
  replication-factor: 3
kafka-producer-config:
  key-serializer-class: org.apache.kafka.common.serialization.UUIDSerializer
  value-serializer-class: org.springframework.kafka.support.serializer.JsonSerializer
  compression-type: none
  acks: all
  batch-size: 16384
  batch-size-boost-factor: 100
  linger-ms: 5
  request-timeout-ms: 60000
  retry-count: 5
kafka-consumer-config:
  key-deserializer: org.apache.kafka.common.serialization.UUIDDeserializer
  value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
  payment-consumer-group-id: payment-topic-consumer
  restaurant-approval-consumer-group-id: restaurant-approval-topic-consumer
  customer-group-id: customer-topic-consumer
  auto-offset-reset: earliest
  specific-avro-reader-key: specific.avro.reader
  specific-avro-reader: true
  batch-listener: true
  auto-startup: true
  concurrency-level: 1
  session-timeout-ms: 10000
  heartbeat-interval-ms: 3000
  max-poll-interval-ms: 300000
  max-poll-records: 500
  max-partition-fetch-bytes-default: 1048576
  max-partition-fetch-bytes-boost-factor: 1
  poll-timeout-ms: 150

logging:
  level:
    com.styl.pacific: INFO
    org.hibernate.SQL: INFO
    org.hibernate.type: INFO
    org.hibernate.type.descriptor.sql.BasicBinder: INFO