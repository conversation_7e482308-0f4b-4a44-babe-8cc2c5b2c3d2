/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.servicecharge.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.common.test.BaseDataJpaTest;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.config.IntegrationTestConfiguration;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.query.ServiceChargeFilter;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.query.ServiceChargeQuery;
import com.styl.pacific.order.service.domain.features.servicecharge.entity.ServiceCharge;
import com.styl.pacific.order.service.domain.features.servicecharge.ports.output.repository.ServiceChargeByPlatformRepository;
import com.styl.pacific.order.service.domain.features.servicecharge.ports.output.repository.ServiceChargeRepository;
import com.styl.pacific.order.service.domain.features.servicecharge.valueobjects.ServiceChargeId;
import io.micrometer.core.instrument.MeterRegistry;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(SpringExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
public class ServiceChargeRepositoryIntegrationTest extends BaseDataJpaTest {

	@MockitoBean
	private MeterRegistry meterRegistry;

	@Autowired
	private ServiceChargeRepository serviceChargeRepository;

	@Autowired
	private ServiceChargeByPlatformRepository serviceChargeByPlatformRepository;

	@Test
	@Order(0)
	public void shouldReturn_whenById() {
		// Arrange
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(100L))
				.tenantId(new TenantId(2L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		serviceChargeRepository.save(serviceCharge);
		// Act
		Optional<ServiceCharge> resultOpt = serviceChargeRepository.findById(serviceCharge.getTenantId(), serviceCharge
				.getId());
		// Assert
		assertTrue(resultOpt.isPresent());
		ServiceCharge result = resultOpt.get();
		assertEquals(serviceCharge.getId(), result.getId());
		assertEquals(serviceCharge.getTenantId(), result.getTenantId());
		assertEquals(serviceCharge.getName(), result.getName());
		assertEquals(serviceCharge.getDescription(), result.getDescription());
		assertEquals(serviceCharge.getActive(), result.getActive());
		assertEquals(serviceCharge.getChargeFixedAmount(), result.getChargeFixedAmount());
		assertEquals(serviceCharge.getChargeRate(), result.getChargeRate());
		assertEquals(serviceCharge.getCurrencyCode(), result.getCurrencyCode());
	}

	@Test
	@Order(1)
	public void shouldReturn_whenFindAll() {
		// Arrange
		int sizeExpected = 1;
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(100L))
				.tenantId(new TenantId(3L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		ServiceCharge serviceCharge2 = ServiceCharge.builder()
				.id(new ServiceChargeId(101L))
				.tenantId(new TenantId(3L))
				.name("Test")
				.isActive(false)
				.description("Description")
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		serviceChargeRepository.save(serviceCharge);
		serviceChargeRepository.save(serviceCharge2);

		// Act
		List<ServiceCharge> content = serviceChargeRepository.findAll(new TenantId(3L), ServiceChargeQuery.builder()
				.filter(ServiceChargeFilter.builder()
						.isActive(true)
						.build())
				.build());
		// Assert
		assertEquals(sizeExpected, content.size());
	}

	@Test
	@Order(2)
	public void shouldReturn_whenCreate() {
		// Arrange
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(100L))
				.tenantId(new TenantId(3L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		// Act
		ServiceCharge result = serviceChargeRepository.save(serviceCharge);
		// Assert
		assertTrue(serviceChargeRepository.existsById(serviceCharge.getTenantId(), serviceCharge.getId()));
		assertEquals(serviceCharge.getId(), result.getId());
		assertEquals(serviceCharge.getTenantId(), result.getTenantId());
		assertEquals(serviceCharge.getName(), result.getName());
		assertEquals(serviceCharge.getDescription(), result.getDescription());
		assertEquals(serviceCharge.getActive(), result.getActive());
		assertEquals(serviceCharge.getChargeFixedAmount(), result.getChargeFixedAmount());
		assertEquals(serviceCharge.getChargeRate(), result.getChargeRate());
		assertEquals(serviceCharge.getCurrencyCode(), result.getCurrencyCode());
	}

	@Test
	@Order(3)
	public void shouldReturn_whenUpdate() {
		// Arrange
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(100L))
				.tenantId(new TenantId(3L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		serviceChargeRepository.save(serviceCharge);
		serviceCharge.setName("Test Update");
		// Act
		ServiceCharge result = serviceChargeRepository.update(serviceCharge);
		// Assert
		assertEquals(serviceCharge.getId(), result.getId());
		assertEquals(serviceCharge.getTenantId(), result.getTenantId());
		assertEquals(serviceCharge.getName(), result.getName());
		assertEquals(serviceCharge.getDescription(), result.getDescription());
		assertEquals(serviceCharge.getActive(), result.getActive());
		assertEquals(serviceCharge.getChargeFixedAmount(), result.getChargeFixedAmount());
		assertEquals(serviceCharge.getChargeRate(), result.getChargeRate());
		assertEquals(serviceCharge.getCurrencyCode(), result.getCurrencyCode());
	}

	@Test
	@Order(4)
	public void shouldInvoke_whenDelete() {
		// Arrange
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(100L))
				.tenantId(new TenantId(3L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		serviceChargeRepository.save(serviceCharge);
		// Act
		serviceChargeRepository.delete(serviceCharge.getTenantId(), serviceCharge.getId());
		// Assert
		Optional<ServiceCharge> result = serviceChargeRepository.findById(serviceCharge.getTenantId(), serviceCharge
				.getId());
		assertTrue(result.isEmpty());
	}

	@Test
	@Order(5)
	public void shouldTrue_whenExistsById() {
		// Arrange
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(100L))
				.tenantId(new TenantId(3L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		serviceChargeRepository.save(serviceCharge);
		// Act
		boolean result = serviceChargeRepository.existsById(serviceCharge.getTenantId(), serviceCharge.getId());
		// Assert
		assertTrue(result);
	}

	@Test
	@Order(6)
	public void shouldFalse_whenNotExistsById() {
		// Arrange
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(100L))
				.tenantId(new TenantId(3L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		serviceChargeRepository.save(serviceCharge);
		// Act
		boolean result = serviceChargeRepository.existsById(serviceCharge.getTenantId(), new ServiceChargeId(101L));
		// Assert
		assertFalse(result);
	}

	@Test
	@Order(7)
	public void shouldTrue_whenExistsByName() {
		// Arrange
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(100L))
				.tenantId(new TenantId(3L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		serviceChargeRepository.save(serviceCharge);
		// Act
		boolean result = serviceChargeRepository.existsByName(serviceCharge.getTenantId(), serviceCharge.getName()
				.toLowerCase());
		// Assert
		assertTrue(result);
	}

	@Test
	@Order(8)
	public void shouldFalse_whenNotExistsByName() {
		// Arrange
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(100L))
				.tenantId(new TenantId(3L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		serviceChargeRepository.save(serviceCharge);
		// Act
		boolean result = serviceChargeRepository.existsByName(serviceCharge.getTenantId(), "Test 1");
		// Assert
		assertFalse(result);
	}

	@Test
	@Order(9)
	public void shouldReturnTrue_whenExistsByNameAndNotId() {
		// Arrange
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(100L))
				.tenantId(new TenantId(3L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		ServiceCharge serviceCharge2 = ServiceCharge.builder()
				.id(new ServiceChargeId(101L))
				.tenantId(new TenantId(3L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		serviceChargeRepository.save(serviceCharge);
		serviceChargeRepository.save(serviceCharge2);
		// Act
		boolean result = serviceChargeRepository.existsByNameAndNotId(serviceCharge.getTenantId(), serviceCharge
				.getId(), "Test");
		// Assert
		assertTrue(result);
	}

	@Test
	@Order(10)
	public void shouldReturnFalse_whenExistByNameAndNotId() {
		// Arrange
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(100L))
				.tenantId(new TenantId(3L))
				.name("Test 1")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		ServiceCharge serviceCharge2 = ServiceCharge.builder()
				.id(new ServiceChargeId(101L))
				.tenantId(new TenantId(3L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		serviceChargeRepository.save(serviceCharge);
		serviceChargeRepository.save(serviceCharge2);
		// Act
		boolean result = serviceChargeRepository.existsByNameAndNotId(serviceCharge.getTenantId(), serviceCharge
				.getId(), "Test 1");
		// Assert
		assertFalse(result);
	}

}
