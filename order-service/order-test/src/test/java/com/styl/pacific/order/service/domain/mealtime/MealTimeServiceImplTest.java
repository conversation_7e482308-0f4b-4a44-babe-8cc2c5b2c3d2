/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.mealtime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.mealtime.MealTimeServiceImpl;
import com.styl.pacific.order.service.domain.features.mealtime.dto.command.CreateMealTimeCommand;
import com.styl.pacific.order.service.domain.features.mealtime.dto.command.UpdateMealTimeCommand;
import com.styl.pacific.order.service.domain.features.mealtime.dto.query.MealTimeQuery;
import com.styl.pacific.order.service.domain.features.mealtime.entity.MealTime;
import com.styl.pacific.order.service.domain.features.mealtime.exception.MealTimeNotFoundException;
import com.styl.pacific.order.service.domain.features.mealtime.id.generator.MealTimeIdGenerator;
import com.styl.pacific.order.service.domain.features.mealtime.ports.output.repository.MealTimeRepository;
import com.styl.pacific.order.service.domain.features.mealtime.valueobjects.MealTimeId;
import com.styl.pacific.order.service.domain.features.tenant.dto.TenantDto;
import com.styl.pacific.order.service.domain.features.tenant.output.repository.TenantRepository;
import java.awt.Color;
import java.time.Instant;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class MealTimeServiceImplTest {
	private static final Long ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final String NAME = "name";
	private static final LocalTime START_TIME = LocalTime.now();
	private static final LocalTime END_TIME = LocalTime.now();
	private static final Color COLOR = Color.BLACK;
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();

	@Mock
	private TenantRepository tenantRepository;

	@Mock
	private MealTimeRepository mealTimeRepository;

	@Mock
	private MealTimeIdGenerator mealTimeIdGenerator;

	@InjectMocks
	private MealTimeServiceImpl mealTimeService;

	@Test
	void shouldReturnListMealTime_whenGetAll() {
		// Arrange
		MealTime mealTimeMock = getMealTime();
		List<MealTime> mealTimeListMock = List.of(mealTimeMock);
		MealTimeQuery queryMock = MealTimeQuery.builder()
				.build();
		when(mealTimeRepository.findAll(any(), eq(queryMock))).thenReturn(mealTimeListMock);
		// Act
		Content<MealTime> mealTimeList = mealTimeService.findAll(new TenantId(TENANT_ID), queryMock);
		// Assert
		verify(mealTimeRepository, times(1)).findAll(any(), any());
		assertEquals(Content.<MealTime>builder()
				.content(mealTimeListMock)
				.build(), mealTimeList);
	}

	@Test
	void shouldReturnMealTime_whenGetById() {
		// Arrange
		MealTime mealTimeMock = getMealTime();
		when(mealTimeRepository.findById(any(), any())).thenReturn(Optional.of(mealTimeMock));
		// Act
		MealTime mealTime = mealTimeService.findById(new TenantId(TENANT_ID), new MealTimeId(ID));
		// Assert
		verify(mealTimeRepository, times(1)).findById(any(), any());
		assertEquals(mealTimeMock, mealTime);
	}

	@Test
	void shouldReturnMealTime_whenCreate() {
		// Arrange
		CreateMealTimeCommand commandMock = CreateMealTimeCommand.builder()
				.color(Mappers.getMapper(CommonDataMapper.class)
						.colorToString(COLOR))
				.name(NAME)
				.startTime(START_TIME.toString())
				.endTime(END_TIME.toString())
				.build();
		MealTime mealTimeMock = getMealTime();
		when(mealTimeRepository.create(any())).thenReturn(mealTimeMock);
		when(mealTimeIdGenerator.nextId()).thenReturn(new MealTimeId(ID));
		TenantDto tenantDtoMock = TenantDto.builder()
				.tenantId(TENANT_ID)
				.build();
		when(tenantRepository.findById(anyLong())).thenReturn(Optional.of(tenantDtoMock));
		// Act
		MealTime mealTime = mealTimeService.create(new TenantId(TENANT_ID), commandMock);
		// Assert
		ArgumentCaptor<MealTime> captor = ArgumentCaptor.forClass(MealTime.class);
		verify(mealTimeRepository, times(1)).create(captor.capture());
		MealTime input = captor.getValue();
		assertEquals(commandMock.getName(), input.getName());
		assertEquals(commandMock.getColor(), Mappers.getMapper(CommonDataMapper.class)
				.colorToString(input.getColor()));
		assertEquals(commandMock.getStartTime(), input.getStartTime()
				.toString());
		assertEquals(commandMock.getEndTime(), input.getEndTime()
				.toString());

		assertEquals(mealTimeMock, mealTime);
	}

	@Test
	void shouldReturnMealTime_whenUpdate() {
		// Arrange
		UpdateMealTimeCommand commandMock = UpdateMealTimeCommand.builder()
				.color(Mappers.getMapper(CommonDataMapper.class)
						.colorToString(COLOR))
				.name(NAME)
				.startTime(START_TIME.toString())
				.endTime(END_TIME.toString())
				.build();
		MealTime mealTimeMock = getMealTime();
		when(mealTimeRepository.findById(any(), any())).thenReturn(Optional.of(mealTimeMock));
		when(mealTimeRepository.update(any())).thenReturn(mealTimeMock);
		// Act
		MealTime mealTime = mealTimeService.update(new TenantId(TENANT_ID), new MealTimeId(ID), commandMock);
		// Assert
		verify(mealTimeRepository, times(1)).findById(any(), any());
		ArgumentCaptor<MealTime> captor = ArgumentCaptor.forClass(MealTime.class);
		verify(mealTimeRepository, times(1)).update(captor.capture());
		MealTime mealTimeInput = captor.getValue();
		assertEquals(commandMock.getName(), mealTimeInput.getName());
		assertEquals(commandMock.getColor(), Mappers.getMapper(CommonDataMapper.class)
				.colorToString(mealTimeInput.getColor()));
		assertEquals(commandMock.getStartTime(), mealTimeInput.getStartTime()
				.toString());
		assertEquals(commandMock.getEndTime(), mealTimeInput.getEndTime()
				.toString());

		assertEquals(mealTimeMock, mealTime);
	}

	@Test
	void shouldThrowNotFoundException_whenUpdate() {
		// Arrange
		String exceptionMessage = String.format("Meal Time %s not found", ID);
		UpdateMealTimeCommand commandMock = UpdateMealTimeCommand.builder()
				.color(Mappers.getMapper(CommonDataMapper.class)
						.colorToString(COLOR))
				.name(NAME)
				.startTime(START_TIME.toString())
				.endTime(END_TIME.toString())
				.build();
		when(mealTimeRepository.findById(any(), any())).thenReturn(Optional.empty());
		// Act
		MealTimeNotFoundException exception = assertThrows(MealTimeNotFoundException.class, () -> mealTimeService
				.update(new TenantId(TENANT_ID), new MealTimeId(ID), commandMock));
		// Assert
		verify(mealTimeRepository, times(1)).findById(any(), any());
		assertEquals(exceptionMessage, exception.getMessage());
	}

	@Test
	void shouldRemove_whenDelete() {
		// Arrange
		when(mealTimeRepository.existsById(any(), any())).thenReturn(true);
		// Act
		mealTimeService.deleteById(new TenantId(TENANT_ID), new MealTimeId(ID));
		// Assert
		verify(mealTimeRepository, times(1)).existsById(any(), any());
		verify(mealTimeRepository, times(1)).deleteById(any(), any());
	}

	@Test
	void shouldThrowNotFound_whenDelete() {
		// Arrange
		String exceptionMessage = String.format("Meal Time %s not found", ID);
		when(mealTimeRepository.existsById(any(), any())).thenReturn(false);
		// Act
		MealTimeNotFoundException exception = assertThrows(MealTimeNotFoundException.class, () -> mealTimeService
				.deleteById(new TenantId(TENANT_ID), new MealTimeId(ID)));
		// Assert
		verify(mealTimeRepository, times(1)).existsById(any(), any());
		assertEquals(exceptionMessage, exception.getMessage());
	}

	private MealTime getMealTime() {
		return MealTime.builder()
				.id(new MealTimeId(ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.startTime(START_TIME)
				.endTime(END_TIME)
				.color(COLOR)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}
}
