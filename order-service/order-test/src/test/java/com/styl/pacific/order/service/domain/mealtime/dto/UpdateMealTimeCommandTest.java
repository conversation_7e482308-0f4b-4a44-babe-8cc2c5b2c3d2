/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.mealtime.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.order.service.domain.features.mealtime.dto.command.UpdateMealTimeCommand;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import java.time.LocalTime;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class UpdateMealTimeCommandTest {
	private static final String NAME = "name";
	private static final LocalTime START_TIME = LocalTime.now();
	private static final LocalTime END_TIME = LocalTime.now();
	private static final String COLOR = "#000000";

	private Validator validator;

	@BeforeEach
	public void setUp() {
		try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
			validator = factory.getValidator();
		}
	}

	@Test
	void shouldCreated_whenBuilder() {
		// Act
		UpdateMealTimeCommand command = UpdateMealTimeCommand.builder()
				.name(NAME)
				.startTime(START_TIME.toString())
				.endTime(END_TIME.toString())
				.color(COLOR)
				.build();
		// Assert
		assertEquals(NAME, command.getName());
		assertEquals(START_TIME.toString(), command.getStartTime());
		assertEquals(END_TIME.toString(), command.getEndTime());
		assertEquals(COLOR, command.getColor());
	}

	@Test
	void shouldInvoke_whenSetter() {
		// Arrange
		UpdateMealTimeCommand commandMock = UpdateMealTimeCommand.builder()
				.build();
		// Act
		commandMock.setName(NAME);
		commandMock.setStartTime(START_TIME.toString());
		commandMock.setEndTime(END_TIME.toString());
		commandMock.setColor(COLOR);
		// Assert
		assertEquals(NAME, commandMock.getName());
		assertEquals(START_TIME.toString(), commandMock.getStartTime());
		assertEquals(END_TIME.toString(), commandMock.getEndTime());
		assertEquals(COLOR, commandMock.getColor());
	}

	@Test
	void whenCommand_shouldValid() {
		// Arrange
		UpdateMealTimeCommand command = UpdateMealTimeCommand.builder()
				.name(NAME)
				.startTime(START_TIME.toString())
				.endTime(END_TIME.toString())
				.color(COLOR)
				.build();

		// Act
		Set<ConstraintViolation<UpdateMealTimeCommand>> violations = validator.validate(command);

		// Assert
		assertTrue(violations.isEmpty());
	}

	@Test
	void whenCommandWithMissingProperties_shouldNotValid() {
		// Arrange
		int violationsExpect = 3;
		UpdateMealTimeCommand command = UpdateMealTimeCommand.builder()
				.build();
		// Act
		Set<ConstraintViolation<UpdateMealTimeCommand>> violations = validator.validate(command);
		// Assert
		assertEquals(violationsExpect, violations.size());
	}
}
