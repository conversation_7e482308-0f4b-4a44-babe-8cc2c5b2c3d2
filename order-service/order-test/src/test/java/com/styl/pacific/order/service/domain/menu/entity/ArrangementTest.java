/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.menu.entity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.domain.valueobject.MenuItemId;
import com.styl.pacific.order.service.domain.features.common.exception.enums.DateOfWeek;
import com.styl.pacific.order.service.domain.features.menu.entity.Arrangement;
import java.time.Instant;
import java.time.LocalTime;
import java.util.List;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
public class ArrangementTest {
	private static final Long MENU_ITEM_ID = 1L;
	private static final Instant START_DATE = Instant.now();
	private static final Instant END_DATE = Instant.now();
	private static final LocalTime START_TIME = LocalTime.now();
	private static final LocalTime END_TIME = LocalTime.now();
	private static final DateOfWeek DATE_OF_WEEK = DateOfWeek.MONDAY;
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();

	@Test
	void shouldCreate_whenBuilder() {
		// Act
		Arrangement arrangement = Arrangement.builder()
				.id(new MenuItemId(MENU_ITEM_ID))
				.startDate(START_DATE)
				.endDate(END_DATE)
				.startTime(START_TIME)
				.endTime(END_TIME)
				.availableOn(List.of(DATE_OF_WEEK))
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
		// Assert
		assertEquals(MENU_ITEM_ID, arrangement.getId()
				.getValue());
		assertEquals(START_DATE, arrangement.getStartDate());
		assertEquals(END_DATE, arrangement.getEndDate());
		assertEquals(START_TIME, arrangement.getStartTime());
		assertEquals(END_TIME, arrangement.getEndTime());
		assertEquals(List.of(DATE_OF_WEEK), arrangement.getAvailableOn());
		assertEquals(CREATED_AT, arrangement.getCreatedAt());
		assertEquals(UPDATED_AT, arrangement.getUpdatedAt());
	}

	@Test
	void shouldReturn_whenSetter() {
		// Arrange
		Arrangement arrangement = Arrangement.builder()
				.build();
		// Act
		arrangement.setId(new MenuItemId(MENU_ITEM_ID));
		arrangement.setStartDate(START_DATE);
		arrangement.setEndDate(END_DATE);
		arrangement.setStartTime(START_TIME);
		arrangement.setEndTime(END_TIME);
		arrangement.setAvailableOn(List.of(DATE_OF_WEEK));
		arrangement.setCreatedAt(CREATED_AT);
		arrangement.setUpdatedAt(UPDATED_AT);
		// Assert
		assertEquals(MENU_ITEM_ID, arrangement.getId()
				.getValue());
		assertEquals(START_DATE, arrangement.getStartDate());
		assertEquals(END_DATE, arrangement.getEndDate());
		assertEquals(START_TIME, arrangement.getStartTime());
		assertEquals(END_TIME, arrangement.getEndTime());
		assertEquals(List.of(DATE_OF_WEEK), arrangement.getAvailableOn());
		assertEquals(CREATED_AT, arrangement.getCreatedAt());
		assertEquals(UPDATED_AT, arrangement.getUpdatedAt());
	}

	@Test
	void shouldReturnTrue_whenEquals() {
		// Arrange
		Arrangement arrangement1 = getArrangement();
		Arrangement arrangement2 = getArrangement();
		// Act
		boolean result = arrangement1.equals(arrangement2);
		// Assert
		assertTrue(result);
	}

	@Test
	void shouldReturnFalse_whenNotEquals() {
		// Arrange
		Arrangement arrangement1 = getArrangement();
		Arrangement arrangement2 = getArrangement();
		arrangement2.setId(new MenuItemId(2L));
		// Act
		boolean result = arrangement1.equals(arrangement2);
		// Assert
		assertFalse(result);
	}

	@Test
	void shouldReturnTrue_whenHashCode() {
		// Arrange
		Arrangement arrangement1 = getArrangement();
		Arrangement arrangement2 = getArrangement();
		// Act
		boolean result = arrangement1.hashCode() == arrangement2.hashCode();
		// Assert
		assertTrue(result);
	}

	@Test
	void shouldReturnFalse_whenNotHashCode() {
		// Arrange
		Arrangement arrangement1 = getArrangement();
		Arrangement arrangement2 = getArrangement();
		arrangement2.setId(new MenuItemId(2L));
		// Act
		boolean result = arrangement1.hashCode() == arrangement2.hashCode();
		// Assert
		assertFalse(result);
	}

	private Arrangement getArrangement() {
		return Arrangement.builder()
				.id(new MenuItemId(MENU_ITEM_ID))
				.startDate(START_DATE)
				.endDate(END_DATE)
				.startTime(START_TIME)
				.endTime(END_TIME)
				.availableOn(List.of(DATE_OF_WEEK))
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}

}
