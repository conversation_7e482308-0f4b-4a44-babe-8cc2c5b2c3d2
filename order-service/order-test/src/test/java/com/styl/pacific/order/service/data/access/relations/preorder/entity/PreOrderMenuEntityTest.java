/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.preorder.entity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.domain.enums.PreOrderMenuType;
import com.styl.pacific.order.service.data.access.jpa.features.mealtime.entity.MealTimeEntity;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.menu.entity.PreOrderMenuEntity;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.menu.entity.PreOrderMenuItemEntity;
import com.styl.pacific.order.service.data.access.jpa.features.product.entity.ProductEntity;
import com.styl.pacific.order.service.domain.features.common.exception.enums.DateOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class PreOrderMenuEntityTest {
	private static final Long PRE_ORDER_MENU_ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final Long STORE_ID = 3L;
	private static final String NAME = "name";
	private static final String DESCRIPTION = "description";
	private static final PreOrderMenuType TYPE = PreOrderMenuType.COLLECTION;

	private static final Long PRE_ORDER_MENU_ITEM_ID = 4L;
	private static final Long CHAIN_ID = 5L;
	private static final Long MEAL_TIME_ID = 6L;
	private static final Long PRODUCT_ID = 7L;
	private static final LocalDate DATE = LocalDate.now();
	private static final Integer CAPACITY = 10;
	private static final boolean REPEATED = true;
	private static final List<DateOfWeek> AVAILABLE_ON = List.of(DateOfWeek.FRIDAY);
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();
	private static final Instant DELETED_AT = Instant.now();

	@Test
	void shouldCreated_whenBuilder() {
		// Arrange
		PreOrderMenuItemEntity entityItem = getPreOrderMenuItemEntity();
		// Act
		PreOrderMenuEntity entity = PreOrderMenuEntity.builder()
				.id(PRE_ORDER_MENU_ID)
				.tenantId(TENANT_ID)
				.storeId(STORE_ID)
				.name(NAME)
				.description(DESCRIPTION)
				.type(TYPE)
				.items(List.of(entityItem))
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.deletedAt(DELETED_AT)
				.build();
		// Assert
		assertEquals(PRE_ORDER_MENU_ID, entity.getId());
		assertEquals(TENANT_ID, entity.getTenantId());
		assertEquals(STORE_ID, entity.getStoreId());
		assertEquals(NAME, entity.getName());
		assertEquals(DESCRIPTION, entity.getDescription());
		assertEquals(TYPE, entity.getType());
		assertEquals(entityItem, entity.getItems()
				.getFirst());
		assertEquals(CREATED_AT, entity.getCreatedAt());
		assertEquals(UPDATED_AT, entity.getUpdatedAt());
		assertEquals(DELETED_AT, entity.getDeletedAt());
	}

	@Test
	void shouldInvoke_whenSetter() {
		// Arrange
		PreOrderMenuItemEntity entityItem = getPreOrderMenuItemEntity();
		PreOrderMenuEntity entityMock = PreOrderMenuEntity.builder()
				.build();
		// Act
		entityMock.setId(PRE_ORDER_MENU_ID);
		entityMock.setTenantId(TENANT_ID);
		entityMock.setStoreId(STORE_ID);
		entityMock.setName(NAME);
		entityMock.setDescription(DESCRIPTION);
		entityMock.setType(TYPE);
		entityMock.setItems(List.of(entityItem));
		entityMock.setCreatedAt(CREATED_AT);
		entityMock.setUpdatedAt(UPDATED_AT);
		entityMock.setDeletedAt(DELETED_AT);
		// Assert
		assertEquals(PRE_ORDER_MENU_ID, entityMock.getId());
		assertEquals(TENANT_ID, entityMock.getTenantId());
		assertEquals(STORE_ID, entityMock.getStoreId());
		assertEquals(NAME, entityMock.getName());
		assertEquals(DESCRIPTION, entityMock.getDescription());
		assertEquals(TYPE, entityMock.getType());
		assertEquals(entityItem, entityMock.getItems()
				.getFirst());
		assertEquals(CREATED_AT, entityMock.getCreatedAt());
		assertEquals(UPDATED_AT, entityMock.getUpdatedAt());
		assertEquals(DELETED_AT, entityMock.getDeletedAt());
	}

	@Test
	void shouldEqual_whenEquals() {
		// Arrange
		PreOrderMenuEntity entity1 = getPreOrderMenuEntity();
		PreOrderMenuEntity entity2 = getPreOrderMenuEntity();
		// Act
		boolean isEqual = entity1.equals(entity2);
		// Assert
		assertTrue(isEqual);
	}

	@Test
	void shouldNotEqual_whenEquals() {
		// Arrange
		PreOrderMenuEntity entity1 = getPreOrderMenuEntity();
		PreOrderMenuEntity entity2 = getPreOrderMenuEntity();
		entity2.setName("New name");
		// Act
		boolean isEqual = entity1.equals(entity2);
		// Assert
		assertFalse(isEqual);
	}

	@Test
	void shouldEqual_whenHashCode() {
		// Arrange
		PreOrderMenuEntity entity1 = getPreOrderMenuEntity();
		PreOrderMenuEntity entity2 = getPreOrderMenuEntity();
		// Act
		boolean isEqual = entity1.hashCode() == entity2.hashCode();
		// Assert
		assertTrue(isEqual);
	}

	@Test
	void shouldNotEqual_whenHashCode() {
		// Arrange
		PreOrderMenuEntity entity1 = getPreOrderMenuEntity();
		PreOrderMenuEntity entity2 = getPreOrderMenuEntity();
		entity2.setName("New name");
		// Act
		boolean isEqual = entity1.hashCode() == entity2.hashCode();
		// Assert
		assertFalse(isEqual);
	}

	private PreOrderMenuEntity getPreOrderMenuEntity() {
		PreOrderMenuItemEntity entity = getPreOrderMenuItemEntity();
		return PreOrderMenuEntity.builder()
				.id(PRE_ORDER_MENU_ID)
				.tenantId(TENANT_ID)
				.storeId(STORE_ID)
				.name(NAME)
				.description(DESCRIPTION)
				.type(TYPE)
				.items(List.of(entity))
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.deletedAt(DELETED_AT)
				.build();
	}

	private PreOrderMenuItemEntity getPreOrderMenuItemEntity() {
		return PreOrderMenuItemEntity.builder()
				.id(PRE_ORDER_MENU_ITEM_ID)
				.chainId(CHAIN_ID)
				.preOrderMenuId(PRE_ORDER_MENU_ID)
				.mealTime(MealTimeEntity.builder()
						.id(MEAL_TIME_ID)
						.build())
				.mealTimeId(MEAL_TIME_ID)
				.product(ProductEntity.builder()
						.id(PRODUCT_ID)
						.build())
				.productId(PRODUCT_ID)
				.date(DATE)
				.capacity(CAPACITY)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.deletedAt(DELETED_AT)
				.build();
	}

}
