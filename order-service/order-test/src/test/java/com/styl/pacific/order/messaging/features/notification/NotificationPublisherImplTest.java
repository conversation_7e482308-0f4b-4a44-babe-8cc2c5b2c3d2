/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.messaging.features.notification;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.PreOrderId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent;
import com.styl.pacific.notification.service.constant.params.PreOrderStatusUpdateTemplateKey;
import com.styl.pacific.notification.service.requests.SubOrderCollectionRequest;
import com.styl.pacific.order.messaging.features.notification.kafka.NotificationCreatedEventKafkaPublisher;
import com.styl.pacific.order.service.domain.features.mealtime.entity.MealTime;
import com.styl.pacific.order.service.domain.features.notification.NotificationPublisher;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.preorder.order.entity.PreOrder;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class NotificationPublisherImplTest {

	private ObjectMapper objectMapper;
	private NotificationPublisher notificationPublisher;
	@Mock
	private NotificationCreatedEventKafkaPublisher kafkaPublisher;
	private static final LocalTime START_TIME = LocalTime.now();

	@BeforeEach
	public void setup() {
		objectMapper = new ObjectMapper();
		notificationPublisher = new NotificationPublisherImpl(kafkaPublisher);
	}

	@Test
	public void testParsingPreOrderToAvroEventSuccess() throws JsonProcessingException {
		List<Order> orders = List.of(Order.builder()
				.collectionDate(LocalDate.of(2025, 7, 31))
				.mealTime(MealTime.builder()
						.name("Lunch")
						.startTime(START_TIME.minus(1, ChronoUnit.HOURS))
						.build())
				.totalAmount(Money.ZERO)
				.build(), Order.builder()
						.collectionDate(LocalDate.of(2025, 7, 31))
						.mealTime(MealTime.builder()
								.name("Breakfast")
								.startTime(START_TIME.minus(2, ChronoUnit.HOURS))
								.build())
						.totalAmount(Money.ZERO)
						.build(), Order.builder()
								.collectionDate(LocalDate.of(2025, 8, 8))
								.mealTime(MealTime.builder()
										.name("Dinner")
										.startTime(START_TIME.minus(3, ChronoUnit.HOURS))
										.build())
								.totalAmount(Money.ZERO)
								.build(), Order.builder()
										.collectionDate(LocalDate.of(2025, 9, 12))
										.mealTime(MealTime.builder()
												.name("Dinner")
												.startTime(START_TIME.minus(3, ChronoUnit.HOURS))
												.build())
										.totalAmount(Money.ZERO)
										.build());
		PreOrder preOrder = PreOrder.builder()
				.tenantId(new TenantId(2L))
				.businessName("Khanh business")
				.customerId(new UserId(123L))
				.customerEmail("<EMAIL>")
				.id(new PreOrderId(123L))
				.preOrderNumber("PRE123456")
				.paymentStatus(OrderPaymentStatus.PAID)
				.paymentMethodName("Credit Card")
				.taxRegNo("TAX123456")
				.taxAmount(Money.ZERO)
				.taxName("GST")
				.totalAmount(Money.ZERO)
				.discountAmount(Money.ZERO)
				.subtotalAmount(Money.ZERO)
				.subOrders(orders)
				.build();
		notificationPublisher.publish(preOrder, Instant.now());

		ArgumentCaptor<NotificationCreatedAvroEvent> captor = ArgumentCaptor.forClass(
				NotificationCreatedAvroEvent.class);
		verify(kafkaPublisher, timeout(5000).times(1)).publish(captor.capture());

		NotificationCreatedAvroEvent event = captor.getValue();

		Map<String, List<SubOrderCollectionRequest>> orderCollections = objectMapper.readValue(event.getData()
				.get(PreOrderStatusUpdateTemplateKey.SUB_ORDER_COLLECTIONS), new TypeReference<>() {
				});

		assertEquals(3, orderCollections.size());

		List<LocalDate> expectedDates = List.of(LocalDate.of(2025, 7, 31), LocalDate.of(2025, 8, 8), LocalDate.of(2025,
				9, 12));
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEEE, dd/MM/yyyy");
		List<LocalDate> actualDates = orderCollections.keySet()
				.stream()
				.map(dateStr -> LocalDate.parse(dateStr, formatter))
				.toList();
		assertEquals(expectedDates, actualDates);
	}
}
