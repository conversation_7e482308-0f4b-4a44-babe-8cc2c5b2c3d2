/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.rest.preorder;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.styl.pacific.application.rest.dto.ErrorResponse;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.common.test.container.DynamoDbContainerTest;
import com.styl.pacific.common.test.container.KafkaContainerTest;
import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.StoreStatus;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.exception.GlobalErrorCode;
import com.styl.pacific.domain.tokenclaims.UserTokenClaim;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.config.IntegrationTestConfiguration;
import com.styl.pacific.order.service.data.access.jpa.features.inventory.repository.InventoryReservationJpaRepository;
import com.styl.pacific.order.service.domain.features.inventory.handler.InventoryReservationCommandHandler;
import com.styl.pacific.order.service.domain.features.inventory.ports.input.service.InventoryReservationService;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderFilter;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderPagingQuery;
import com.styl.pacific.order.service.domain.features.order.ports.input.service.OrderService;
import com.styl.pacific.order.service.domain.features.order.ports.output.producer.ReserveInventoryCommandProducer;
import com.styl.pacific.order.service.domain.utils.TimeHelper;
import com.styl.pacific.order.service.shared.http.order.request.place.OrderServiceChargeRequest;
import com.styl.pacific.order.service.shared.http.preorder.menu.enums.PreOrderMenuType;
import com.styl.pacific.order.service.shared.http.preorder.order.enums.PreOrderStatus;
import com.styl.pacific.order.service.shared.http.preorder.order.request.CreatePreOrderRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.PreOrderDetailRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.PreOrderLineItemOptionItemRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.PreOrderLineItemOptionRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.PreOrderLineItemRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.PreOrderPaymentRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.SubOrderDetailRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.response.PreOrderLightResponse;
import com.styl.pacific.payment.shared.http.methods.response.PaymentMethodResponse;
import com.styl.pacific.store.shared.http.responses.store.StoreResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TaxResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import com.styl.pacific.user.shared.enums.UserSubAccountStatus;
import com.styl.pacific.user.shared.http.groups.response.UserGroupResponse;
import com.styl.pacific.user.shared.http.subaccounts.response.SubAccountResponse;
import com.styl.pacific.user.shared.http.users.response.UserLiteResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Random;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.web.reactive.server.WebTestClient;

/**
 * <AUTHOR>
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
public class PreOrderControllerIntegrationTest extends BaseWebClientWithDbTest implements DynamoDbContainerTest,
		KafkaContainerTest {
	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	@Qualifier("tenantService")
	private WireMockServer tenantServiceMock;

	@Autowired
	@Qualifier("storeService")
	private WireMockServer storeServiceMock;

	@Autowired
	@Qualifier("paymentService")
	private WireMockServer paymentServiceMock;

	@Autowired
	@Qualifier("userService")
	private WireMockServer userServiceMock;

	@MockitoSpyBean
	private InventoryReservationService inventoryService;

	@Autowired
	private OrderService orderService;

	@Autowired
	private InventoryReservationJpaRepository inventoryReservationJpaRepository;

	@MockitoBean
	private ReserveInventoryCommandProducer reserveInventoryCommandProducer;

	@MockitoSpyBean
	private InventoryReservationCommandHandler inventoryReservationCommandHandler;

	@MockitoSpyBean
	private TimeHelper timeHelper;

	private final String preOrderNumberRegex = "^[A-Z0-9]{6}-\\d{3}$";

	private TaxResponse taxResponse;

	@BeforeEach
	public void setup() throws JsonProcessingException {
		TenantResponse tenantResponse = TenantResponse.builder()
				.tenantId(2L)
				.settings(TenantSettingsResponse.builder()
						.currency(CurrencyResponse.builder()
								.displayName("Vietnamese Dong")
								.currencyCode("VND")
								.symbol("₫")
								.fractionDigits(0)
								.numericCode(704)
								.build())
						.build())
				.build();
		tenantServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/tenant/tenants/" + 2))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(tenantResponse))));

		taxResponse = TaxResponse.builder()
				.id("1")
				.tenantId("2")
				.name("TAX")
				.taxRegNo("12345")
				.businessName("Test Business")
				.rate(new BigDecimal("0.1100"))
				.description("test")
				.enabled(true)
				.includeInPrice(false)
				.effectiveDate(1726245964L)
				.build();
		tenantServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/tenant/taxes/currentTax"))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(taxResponse))));

		StoreResponse storeResponse = StoreResponse.builder()
				.storeId(2L)
				.orderNumberPrefix("OrderNumberPrefix")
				.tenantId(2L)
				.status(StoreStatus.ACTIVE)
				.build();
		storeServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/store/stores/" + 1L))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(storeResponse))));
		storeServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/store/stores/" + 2L))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(storeResponse))));

		PaymentMethodResponse paymentMethodResponse = PaymentMethodResponse.builder()
				.id("1")
				.displayName("Pay with cash")
				.description("Pay with cash")
				.currency(CurrencyResponse.builder()
						.displayName("Vietnamese Dong")
						.currencyCode("VND")
						.symbol("₫")
						.fractionDigits(0)
						.numericCode(704)
						.build())
				.isActive(true)
				.processorId(PaymentProcessorId.CASH_PAYMENT)
				.surchargeRate("0")
				.fixedSurcharge(0L)
				.surchargeTitle("Surcharge")
				.isTransactionReversible(false)
				.build();
		paymentServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/payment/methods/" + 1))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(paymentMethodResponse))));

		Paging<SubAccountResponse> subAccountResponse = Paging.<SubAccountResponse>builder()
				.content(List.of(SubAccountResponse.builder()
						.id("167554043103075328")
						.subAccountStatus(UserSubAccountStatus.ACTIVE)
						.subUser(UserLiteResponse.builder()
								.id("167554043103075328")
								.userGroup(UserGroupResponse.builder()
										.id("1")
										.build())
								.build())
						.createdAt(Instant.now())
						.updatedAt(Instant.now())
						.build()))
				.page(1)
				.totalElements(1)
				.totalPages(1)
				.build();
		userServiceMock.stubFor(WireMock.get(WireMock.anyUrl())
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(subAccountResponse))));
	}

	@Test
	@Order(0)
	public void shouldReturnInvalidPreOrderSubAmount_whenCreatePreOrder() {
		// Arrange
		String instantExpected = "2024-10-22T10:15:30Z";
		Clock clock = Clock.fixed(Instant.parse(instantExpected), ZoneId.of("UTC"));
		Instant instant = Instant.now(clock);
		Mockito.when(timeHelper.getCurrentTime())
				.thenReturn(instant);
		CreatePreOrderRequest request = CreatePreOrderRequest.builder()
				.order(PreOrderDetailRequest.builder()
						.customerId("167554043103075328")
						.orders(List.of(

								SubOrderDetailRequest.builder()
										.storeId("1")
										.collectionDate(LocalDate.parse("2024-11-10"))
										.mealTimeId("1")
										.preOrderMenuType(PreOrderMenuType.DELIVERY)
										.lineItems(List.of(PreOrderLineItemRequest.builder()
												.preOrderMenuItemId("1")
												.productId("1")
												.productName("Product 1")
												.quantity(1L)
												.note("Note test 1")
												.options(List.of(PreOrderLineItemOptionRequest.builder()
														.optionId("1")
														.title("Option 1")
														.items(List.of(PreOrderLineItemOptionItemRequest.builder()
																.optionItemId("2")
																.name("item 2")
																.additionalPrice(BigInteger.valueOf(1000))
																.build()))
														.build()))
												.unitPrice(BigInteger.valueOf(2000))
												.totalDiscount(BigInteger.valueOf(0))
												.totalAmount(BigInteger.valueOf(3000))
												.build()))
										.discountAmount(BigInteger.valueOf(0))
										.subtotalAmount(BigInteger.valueOf(3000))
										.serviceCharges(List.of(OrderServiceChargeRequest.builder()
												.serviceChargeId("2")
												.name("Hi")
												.amount(BigInteger.valueOf(2300))
												.build()))
										.serviceChargeAmount(BigInteger.valueOf(2300))
										.taxName("TAX")
										.taxRate(new BigDecimal("0.1100"))
										.taxInclude(false)
										.taxAmount(BigInteger.valueOf(583))
										.totalAmount(BigInteger.valueOf(5883))
										.build())

						)
						.subtotalAmount(BigInteger.valueOf(2000))
						.serviceChargeAmount(BigInteger.valueOf(2300))
						.taxName("TAX")
						.taxRate(new BigDecimal("0.1100"))
						.taxInclude(false)
						.taxAmount(BigInteger.valueOf(583))
						.totalAmount(BigInteger.valueOf(5883))
						.discountAmount(BigInteger.valueOf(0))
						.currencyCode("VND")
						.build())
				.payment(PreOrderPaymentRequest.builder()
						.paymentMethodId("1")
						.build())
				.build();

		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path("/api/order/pre-orders/orders")
							.pathSegment("place");
					return uriBuilder.build();
				})
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.value(errorResponse -> {
					assertEquals(GlobalErrorCode.INVALID_PRE_ORDER_AMOUNT.getValue(), errorResponse.getCode());
				});

	}

	@Test
	@Order(0)
	public void shouldReturnInvalidPreOrderTotalAmount_whenCreatePreOrder() {
		// Arrange
		String instantExpected = "2024-10-22T10:15:30Z";
		Clock clock = Clock.fixed(Instant.parse(instantExpected), ZoneId.of("UTC"));
		Instant instant = Instant.now(clock);
		Mockito.when(timeHelper.getCurrentTime())
				.thenReturn(instant);
		CreatePreOrderRequest request = CreatePreOrderRequest.builder()
				.order(PreOrderDetailRequest.builder()
						.customerId("167554043103075328")
						.orders(List.of(

								SubOrderDetailRequest.builder()
										.storeId("1")
										.collectionDate(LocalDate.parse("2024-11-10"))
										.mealTimeId("1")
										.preOrderMenuType(PreOrderMenuType.DELIVERY)
										.lineItems(List.of(PreOrderLineItemRequest.builder()
												.preOrderMenuItemId("1")
												.productId("1")
												.productName("Product 1")
												.quantity(1L)
												.note("Note test 1")
												.options(List.of(PreOrderLineItemOptionRequest.builder()
														.optionId("1")
														.title("Option 1")
														.items(List.of(PreOrderLineItemOptionItemRequest.builder()
																.optionItemId("2")
																.name("item 2")
																.additionalPrice(BigInteger.valueOf(1000))
																.build()))
														.build()))
												.unitPrice(BigInteger.valueOf(2000))
												.totalDiscount(BigInteger.valueOf(0))
												.totalAmount(BigInteger.valueOf(3000))
												.build()))
										.discountAmount(BigInteger.valueOf(0))
										.subtotalAmount(BigInteger.valueOf(3000))
										.serviceCharges(List.of(OrderServiceChargeRequest.builder()
												.serviceChargeId("2")
												.name("Hi")
												.amount(BigInteger.valueOf(2300))
												.build()))
										.serviceChargeAmount(BigInteger.valueOf(2300))
										.taxName("TAX")
										.taxRate(new BigDecimal("0.1100"))
										.taxInclude(false)
										.taxAmount(BigInteger.valueOf(583))
										.totalAmount(BigInteger.valueOf(5883))
										.build())

						)
						.subtotalAmount(BigInteger.valueOf(3000))
						.serviceChargeAmount(BigInteger.valueOf(2300))
						.taxName("TAX")
						.taxRate(new BigDecimal("0.1100"))
						.taxInclude(false)
						.taxAmount(BigInteger.valueOf(583))
						.totalAmount(BigInteger.valueOf(4883))
						.discountAmount(BigInteger.valueOf(0))
						.currencyCode("VND")
						.build())
				.payment(PreOrderPaymentRequest.builder()
						.paymentMethodId("1")
						.build())
				.build();

		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path("/api/order/pre-orders/orders")
							.pathSegment("place");
					return uriBuilder.build();
				})
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.value(errorResponse -> {
					assertEquals(GlobalErrorCode.INVALID_PRE_ORDER_AMOUNT.getValue(), errorResponse.getCode());
				});

	}

	@Test
	@Order(0)
	public void shouldReturnInvalidPreOrderInfo_whenCreatePreOrder() {
		// Arrange
		String instantExpected = "2024-10-22T10:15:30Z";
		Clock clock = Clock.fixed(Instant.parse(instantExpected), ZoneId.of("UTC"));
		Instant instant = Instant.now(clock);
		Mockito.when(timeHelper.getCurrentTime())
				.thenReturn(instant);
		CreatePreOrderRequest request = CreatePreOrderRequest.builder()
				.order(PreOrderDetailRequest.builder()
						.customerId("167554043103075328")
						.orders(List.of(

								SubOrderDetailRequest.builder()
										.storeId("1")
										.collectionDate(LocalDate.parse("2024-11-10"))
										.mealTimeId("1")
										.preOrderMenuType(PreOrderMenuType.DELIVERY)
										.lineItems(List.of(PreOrderLineItemRequest.builder()
												.preOrderMenuItemId("1")
												.productId("1")
												.productName("Product 2")
												.quantity(1L)
												.note("Note test 1")
												.options(List.of(PreOrderLineItemOptionRequest.builder()
														.optionId("1")
														.title("Option 1")
														.items(List.of(PreOrderLineItemOptionItemRequest.builder()
																.optionItemId("2")
																.name("item 2")
																.additionalPrice(BigInteger.valueOf(1000))
																.build()))
														.build()))
												.unitPrice(BigInteger.valueOf(2000))
												.totalDiscount(BigInteger.valueOf(0))
												.totalAmount(BigInteger.valueOf(3000))
												.build()))
										.discountAmount(BigInteger.valueOf(0))
										.subtotalAmount(BigInteger.valueOf(3000))
										.serviceCharges(List.of(OrderServiceChargeRequest.builder()
												.serviceChargeId("1")
												.name("Hi")
												.amount(BigInteger.valueOf(2300))
												.build()))
										.serviceChargeAmount(BigInteger.valueOf(2300))
										.taxName("TAX")
										.taxRate(new BigDecimal("0.1100"))
										.taxInclude(false)
										.taxAmount(BigInteger.valueOf(583))
										.totalAmount(BigInteger.valueOf(5883))
										.build())

						)
						.subtotalAmount(BigInteger.valueOf(3000))
						.serviceChargeAmount(BigInteger.valueOf(2300))
						.taxName("TAX")
						.taxRate(new BigDecimal("0.1100"))
						.taxInclude(false)
						.taxAmount(BigInteger.valueOf(583))
						.totalAmount(BigInteger.valueOf(5883))
						.discountAmount(BigInteger.valueOf(0))
						.currencyCode("VND")
						.build())
				.payment(PreOrderPaymentRequest.builder()
						.paymentMethodId("1")
						.build())
				.build();

		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path("/api/order/pre-orders/orders")
							.pathSegment("place");
					return uriBuilder.build();
				})
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.value(errorResponse -> {
					assertEquals(GlobalErrorCode.INVALID_ORDER_LINE_ITEM_INFO.getValue(), errorResponse.getCode());
				});

	}

	@Test
	@Order(0)
	public void shouldReturnInvalidPreOrderCutOff_whenCreatePreOrder() {
		// Arrange
		String instantExpected = "2024-12-22T10:15:30Z";
		Clock clock = Clock.fixed(Instant.parse(instantExpected), ZoneId.of("UTC"));
		Instant instant = Instant.now(clock);
		Mockito.when(timeHelper.getCurrentTime())
				.thenReturn(instant);
		CreatePreOrderRequest request = CreatePreOrderRequest.builder()
				.order(PreOrderDetailRequest.builder()
						.customerId("167554043103075328")
						.orders(List.of(

								SubOrderDetailRequest.builder()
										.storeId("1")
										.collectionDate(LocalDate.parse("2024-11-10"))
										.mealTimeId("1")
										.preOrderMenuType(PreOrderMenuType.DELIVERY)
										.lineItems(List.of(PreOrderLineItemRequest.builder()
												.preOrderMenuItemId("1")
												.productId("1")
												.productName("Product 1")
												.quantity(1L)
												.note("Note test 1")
												.options(List.of(PreOrderLineItemOptionRequest.builder()
														.optionId("1")
														.title("Option 1")
														.items(List.of(PreOrderLineItemOptionItemRequest.builder()
																.optionItemId("2")
																.name("item 2")
																.additionalPrice(BigInteger.valueOf(1000))
																.build()))
														.build()))
												.unitPrice(BigInteger.valueOf(2000))
												.totalDiscount(BigInteger.valueOf(0))
												.totalAmount(BigInteger.valueOf(3000))
												.build()))
										.discountAmount(BigInteger.valueOf(0))
										.subtotalAmount(BigInteger.valueOf(3000))
										.serviceCharges(List.of(OrderServiceChargeRequest.builder()
												.serviceChargeId("1")
												.name("Hi")
												.amount(BigInteger.valueOf(2300))
												.build()))
										.serviceChargeAmount(BigInteger.valueOf(2300))
										.taxName("TAX")
										.taxRate(new BigDecimal("0.1100"))
										.taxInclude(false)
										.taxAmount(BigInteger.valueOf(583))
										.totalAmount(BigInteger.valueOf(5883))
										.build())

						)
						.subtotalAmount(BigInteger.valueOf(3000))
						.serviceChargeAmount(BigInteger.valueOf(2300))
						.taxName("TAX")
						.taxRate(new BigDecimal("0.1100"))
						.taxInclude(false)
						.taxAmount(BigInteger.valueOf(583))
						.totalAmount(BigInteger.valueOf(5883))
						.discountAmount(BigInteger.valueOf(0))
						.currencyCode("VND")
						.build())
				.payment(PreOrderPaymentRequest.builder()
						.paymentMethodId("1")
						.build())
				.build();

		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path("/api/order/pre-orders/orders")
							.pathSegment("place");
					return uriBuilder.build();
				})
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.value(errorResponse -> {
					assertEquals(GlobalErrorCode.PRE_ORDER_CUTOFF_TIME_EXCEEDED.getValue(), errorResponse.getCode());
				});

	}

	@Test
	@Order(0)
	public void shouldReturnInvalidPreOrderMenuMaximum_whenCreatePreOrder() {
		// Arrange
		String instantExpected = "2024-10-22T10:15:30Z";
		Clock clock = Clock.fixed(Instant.parse(instantExpected), ZoneId.of("UTC"));
		Instant instant = Instant.now(clock);
		Mockito.when(timeHelper.getCurrentTime())
				.thenReturn(instant);
		CreatePreOrderRequest request = CreatePreOrderRequest.builder()
				.order(PreOrderDetailRequest.builder()
						.customerId("167554043103075328")
						.orders(List.of(

								SubOrderDetailRequest.builder()
										.storeId("1")
										.collectionDate(LocalDate.parse("2024-11-10"))
										.mealTimeId("1")
										.preOrderMenuType(PreOrderMenuType.DELIVERY)
										.lineItems(List.of(PreOrderLineItemRequest.builder()
												.preOrderMenuItemId("1")
												.productId("1")
												.productName("Product 1")
												.quantity(2L)
												.note("Note test 1")
												.options(List.of(PreOrderLineItemOptionRequest.builder()
														.optionId("1")
														.title("Option 1")
														.items(List.of(PreOrderLineItemOptionItemRequest.builder()
																.optionItemId("2")
																.name("item 2")
																.additionalPrice(BigInteger.valueOf(1000))
																.build()))
														.build()))
												.unitPrice(BigInteger.valueOf(2000))
												.totalDiscount(BigInteger.valueOf(0))
												.totalAmount(BigInteger.valueOf(6000))
												.build()))
										.discountAmount(BigInteger.valueOf(0))
										.subtotalAmount(BigInteger.valueOf(6000))
										.serviceCharges(List.of(OrderServiceChargeRequest.builder()
												.serviceChargeId("1")
												.name("Hi")
												.amount(BigInteger.valueOf(2600))
												.build()))
										.serviceChargeAmount(BigInteger.valueOf(2600))
										.taxName("TAX")
										.taxRate(new BigDecimal("0.1100"))
										.taxInclude(false)
										.taxAmount(BigInteger.valueOf(860))
										.totalAmount(BigInteger.valueOf(8860))
										.build())

						)
						.subtotalAmount(BigInteger.valueOf(6000))
						.serviceChargeAmount(BigInteger.valueOf(2600))
						.taxName("TAX")
						.taxRate(new BigDecimal("0.1100"))
						.taxInclude(false)
						.taxAmount(BigInteger.valueOf(860))
						.totalAmount(BigInteger.valueOf(8860))
						.discountAmount(BigInteger.valueOf(0))
						.currencyCode("VND")
						.build())
				.payment(PreOrderPaymentRequest.builder()
						.paymentMethodId("1")
						.build())
				.build();

		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path("/api/order/pre-orders/orders")
							.pathSegment("place");
					return uriBuilder.build();
				})
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.value(errorResponse -> {
					assertEquals(GlobalErrorCode.PRE_ORDER_ITEM_EXCEEDED_MAX_QUANTITY.getValue(), errorResponse
							.getCode());
				});

	}

	@Test
	@Order(1)
	public void shouldReturnPreOrder_whenCreatePreOrder() {
		// Arrange
		String instantExpected = "2024-10-22T10:15:30Z";
		Clock clock = Clock.fixed(Instant.parse(instantExpected), ZoneId.of("UTC"));
		Instant instant = Instant.now(clock);
		Mockito.when(timeHelper.getCurrentTime())
				.thenReturn(instant);
		CreatePreOrderRequest request = CreatePreOrderRequest.builder()
				.order(PreOrderDetailRequest.builder()
						.customerId("167554043103075328")
						.orders(List.of(SubOrderDetailRequest.builder()
								.storeId("1")
								.collectionDate(LocalDate.parse("2024-11-10"))
								.mealTimeId("1")
								.preOrderMenuType(PreOrderMenuType.DELIVERY)
								.lineItems(List.of(PreOrderLineItemRequest.builder()
										.preOrderMenuItemId("1")
										.productId("1")
										.productName("Product 1")
										.quantity(1L)
										.note("Note test 1")
										.options(List.of(PreOrderLineItemOptionRequest.builder()
												.optionId("1")
												.title("Option 1")
												.items(List.of(PreOrderLineItemOptionItemRequest.builder()
														.optionItemId("2")
														.name("item 2")
														.additionalPrice(BigInteger.valueOf(1000))
														.build()))
												.build()))
										.unitPrice(BigInteger.valueOf(2000))
										.totalDiscount(BigInteger.valueOf(0))
										.totalAmount(BigInteger.valueOf(3000))
										.build()))
								.discountAmount(BigInteger.valueOf(0))
								.subtotalAmount(BigInteger.valueOf(3000))
								.serviceCharges(List.of(OrderServiceChargeRequest.builder()
										.serviceChargeId("2")
										.name("Hi")
										.amount(BigInteger.valueOf(2300))
										.build()))
								.serviceChargeAmount(BigInteger.valueOf(2300))
								.taxName("TAX")
								.taxRate(new BigDecimal("0.1100"))
								.taxInclude(false)
								.taxAmount(BigInteger.valueOf(583))
								.totalAmount(BigInteger.valueOf(5883))
								.build(),

								SubOrderDetailRequest.builder()
										.storeId("1")
										.collectionDate(LocalDate.parse("2024-11-19"))
										.mealTimeId("1")
										.preOrderMenuType(PreOrderMenuType.DELIVERY)
										.lineItems(List.of(PreOrderLineItemRequest.builder()
												.preOrderMenuItemId("2")
												.productId("2")
												.productName("Product 2")
												.quantity(1L)
												.note("Note test 2")
												.options(List.of(PreOrderLineItemOptionRequest.builder()
														.optionId("2")
														.title("Option 2")
														.items(List.of(PreOrderLineItemOptionItemRequest.builder()
																.optionItemId("5")
																.name("item 2")
																.additionalPrice(BigInteger.valueOf(1000))
																.build()))
														.build()))
												.unitPrice(BigInteger.valueOf(1500))
												.totalDiscount(BigInteger.valueOf(0))
												.totalAmount(BigInteger.valueOf(2500))
												.build()))
										.discountAmount(BigInteger.valueOf(0))
										.subtotalAmount(BigInteger.valueOf(2500))
										.serviceCharges(List.of(OrderServiceChargeRequest.builder()
												.serviceChargeId("2")
												.name("Hi")
												.amount(BigInteger.valueOf(2250))
												.build()))
										.serviceChargeAmount(BigInteger.valueOf(2250))
										.taxName("TAX")
										.taxRate(new BigDecimal("0.1100"))
										.taxInclude(false)
										.taxAmount(BigInteger.valueOf(523))
										.totalAmount(BigInteger.valueOf(5273))
										.build())

						)
						.subtotalAmount(BigInteger.valueOf(5500))
						.serviceChargeAmount(BigInteger.valueOf(4550))
						.taxName("TAX")
						.taxRate(new BigDecimal("0.1100"))
						.taxInclude(false)
						.taxAmount(BigInteger.valueOf(1106))
						.totalAmount(BigInteger.valueOf(11156))
						.discountAmount(BigInteger.valueOf(0))
						.currencyCode("VND")
						.build())
				.payment(PreOrderPaymentRequest.builder()
						.paymentMethodId("1")
						.build())
				.build();

		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path("/api/order/pre-orders/orders")
							.pathSegment("place");
					return uriBuilder.build();
				})
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange();
		// Assert
		PreOrderLightResponse preOrderLightResponse = responseSpec.expectStatus()
				.isCreated()
				.returnResult(PreOrderLightResponse.class)
				.getResponseBody()
				.blockFirst();

		Long preOrderId = Long.valueOf(preOrderLightResponse.getId());
		assertEquals("167550539722578944", preOrderLightResponse.getIssuerId());
		assertEquals("167554043103075328", preOrderLightResponse.getCustomerId());
		assertNotNull(preOrderLightResponse.getPreOrderNumber());
		assertEquals(taxResponse.getTaxRegNo(), preOrderLightResponse.getTaxRegNo());
		assertEquals(taxResponse.getBusinessName(), preOrderLightResponse.getBusinessName());
		assertEquals(PreOrderStatus.PENDING, preOrderLightResponse.getStatus());
		assertNotNull(preOrderLightResponse.getPaymentRef());

		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("167554043103075328")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("2")
						.userRoleId("1")
						.build()))
				.userType(UserType.CUSTOMER)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();
		OrderFilter filter = OrderFilter.builder()
				.preOrderIds(List.of(preOrderId))
				.build();
		OrderPagingQuery query = new OrderPagingQuery(filter, null, null, null, null);
		Paging<com.styl.pacific.order.service.domain.features.order.entity.Order> paging = orderService.findAllPaging(
				new TenantId(2L), userTokenClaim, query);
		assertNotNull(paging.getContent());
		List<com.styl.pacific.order.service.domain.features.order.entity.Order> orders = paging.getContent();
		assertTrue(paging.getContent()
				.size() == 2);
		String preOrderPrefix = orders.get(0)
				.getOrderNumber()
				.substring(0, 6);
		orders.stream()
				.forEach(order -> {
					assertTrue(order.getOrderNumber()
							.startsWith(preOrderPrefix + "-"));
					assertTrue(order.getOrderNumber()
							.matches(preOrderNumberRegex));
				});
	}

	@Test
	@Order(2)
	public void shouldIgnorePriceCheck_whenCreatingPreOrder() {
		// Arrange
		String instantExpected = "2024-10-22T10:15:30Z";
		Clock clock = Clock.fixed(Instant.parse(instantExpected), ZoneId.of("UTC"));
		Instant instant = Instant.now(clock);
		Mockito.when(timeHelper.getCurrentTime())
				.thenReturn(instant);
		CreatePreOrderRequest request = CreatePreOrderRequest.builder()
				.order(PreOrderDetailRequest.builder()
						.customerId("167554043103075328")
						.orders(List.of(

								SubOrderDetailRequest.builder()
										.storeId("2")
										.collectionDate(LocalDate.parse("2024-11-10"))
										.mealTimeId("1")
										.preOrderMenuType(PreOrderMenuType.DELIVERY)
										.lineItems(List.of(PreOrderLineItemRequest.builder()
												.preOrderMenuItemId("6")
												.productId("4")
												.productName("Product 4")
												.quantity(2L)
												.note("Note test 1")
												.options(List.of(PreOrderLineItemOptionRequest.builder()
														.optionId("3")
														.title("Option 3")
														.items(List.of(PreOrderLineItemOptionItemRequest.builder()
																.optionItemId("7")
																.name("item 3")
																.additionalPrice(BigInteger.valueOf(0))
																.build()))
														.build()))
												.unitPrice(BigInteger.valueOf(2000))
												.totalDiscount(BigInteger.valueOf(0))
												.totalAmount(BigInteger.valueOf(4000))
												.build()))
										.discountAmount(BigInteger.valueOf(0))
										.subtotalAmount(BigInteger.valueOf(4000))
										.serviceCharges(List.of(OrderServiceChargeRequest.builder()
												.serviceChargeId("2")
												.name("Hi")
												.amount(BigInteger.valueOf(2400))
												.build()))
										.serviceChargeAmount(BigInteger.valueOf(2400))
										.taxName("TAX")
										.taxRate(new BigDecimal("0.1100"))
										.taxInclude(false)
										.taxAmount(BigInteger.valueOf(704))
										.totalAmount(BigInteger.valueOf(7104))
										.build(),

								SubOrderDetailRequest.builder()
										.storeId("2")
										.collectionDate(LocalDate.parse("2024-11-19"))
										.mealTimeId("1")
										.preOrderMenuType(PreOrderMenuType.DELIVERY)
										.lineItems(List.of(PreOrderLineItemRequest.builder()
												.preOrderMenuItemId("7")
												.productId("5")
												.productName("Product 5")
												.quantity(3L)
												.note("Note test 2")
												.options(List.of(PreOrderLineItemOptionRequest.builder()
														.optionId("4")
														.title("Option 4")
														.items(List.of(PreOrderLineItemOptionItemRequest.builder()
																.optionItemId("8")
																.name("item 4")
																.additionalPrice(BigInteger.valueOf(1000))
																.build()))
														.build()))
												.unitPrice(BigInteger.valueOf(1500))
												.totalDiscount(BigInteger.valueOf(0))
												.totalAmount(BigInteger.valueOf(7500))
												.build()))
										.discountAmount(BigInteger.valueOf(0))
										.subtotalAmount(BigInteger.valueOf(7500))
										.serviceCharges(List.of(OrderServiceChargeRequest.builder()
												.serviceChargeId("2")
												.name("Hi")
												.amount(BigInteger.valueOf(2750))
												.build()))
										.serviceChargeAmount(BigInteger.valueOf(2750))
										.taxName("TAX")
										.taxRate(new BigDecimal("0.1100"))
										.taxInclude(false)
										.taxAmount(BigInteger.valueOf(1128))
										.totalAmount(BigInteger.valueOf(11378))
										.build())

						)
						.subtotalAmount(BigInteger.valueOf(11500))
						.serviceChargeAmount(BigInteger.valueOf(5150))
						.taxName("TAX")
						.taxRate(new BigDecimal("0.1100"))
						.taxInclude(false)
						.taxAmount(BigInteger.valueOf(1832))
						.totalAmount(BigInteger.valueOf(18482))
						.discountAmount(BigInteger.valueOf(0))
						.currencyCode("VND")
						.build())
				.payment(PreOrderPaymentRequest.builder()
						.paymentMethodId("1")
						.build())
				.build();

		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path("/api/order/pre-orders/orders")
							.pathSegment("place");
					return uriBuilder.build();
				})
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isCreated()
				.expectBody(PreOrderLightResponse.class)
				.value(preOrderLightResponse -> {
					assertEquals("167550539722578944", preOrderLightResponse.getIssuerId());
					assertEquals("167554043103075328", preOrderLightResponse.getCustomerId());
					assertEquals(PreOrderStatus.PENDING, preOrderLightResponse.getStatus());
					assertNotNull(preOrderLightResponse.getPaymentRef());
				});

	}

	private void setHeaders(HttpHeaders headers) {
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(2));
		headers.set(HttpHeaders.AUTHORIZATION, "Bearer "
				+ "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJXcURlcmM0Z0FyR041ZkhpcnFLVUFoc3BhNTJFR0NEZm03UnNsVlNzU1N3In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.zUcwu0REROtfWcTa0PRyJqX-BNjj1AfCzat5VJIqzFMCOTElSw7q3W5Qkg52EepC4hSMHeYCxMWh9ZLYXNnfydfB-u_etuIB2anI0whmUBs85CqdKJyb7P9dq6PAQi4fK-AGnF9ib0EvHZerwiIa9CrNzZXVQEWqiNQLWlwF-g0ne7LkaqfObvCLmBXiIudS2dc9BL-p3tWjIHmXSDB563KpYRfgiVUwRZ5SBQU8FkFkWJCIMQCADfaFT0ISkhrz0-BB566YFa1B53Pdz5e5IvyoPykmrFtj-rlDJyD75DvIJMnJpAR7j8nY7QjsSVoUZUX7kPIFVeHZ7tyBizoiDg");
	}
}
