/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.payment.event;

import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.PaymentTransactionId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.common.event.OrderEvent;

/**
 * <AUTHOR>
 */
public class PaymentReverseEvent extends OrderEvent {
	private final TenantId tenantId;
	private final PaymentTransactionId paymentTransactionId;
	private final String idempotencyKey;
	private final Money refundingAmount;
	private final String reason;

	public PaymentReverseEvent(TenantId tenantId, PaymentTransactionId paymentTransactionId, String idempotencyKey,
			Money refundingAmount, String reason) {
		this.tenantId = tenantId;
		this.paymentTransactionId = paymentTransactionId;
		this.idempotencyKey = idempotencyKey;
		this.refundingAmount = refundingAmount;
		this.reason = reason;
	}

	public TenantId getTenantId() {
		return tenantId;
	}

	public PaymentTransactionId getPaymentTransactionId() {
		return paymentTransactionId;
	}

	public String getIdempotencyKey() {
		return idempotencyKey;
	}

	public Money getRefundingAmount() {
		return refundingAmount;
	}

	public String getReason() {
		return reason;
	}
}
