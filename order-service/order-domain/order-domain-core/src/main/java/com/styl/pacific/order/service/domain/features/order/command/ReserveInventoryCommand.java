/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.command;

import com.styl.pacific.domain.event.DomainEvent;
import com.styl.pacific.domain.valueobject.OrderId;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.inventory.entity.Inventory;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class ReserveInventoryCommand implements DomainEvent<Inventory> {
	private final UUID id;
	private final TenantId tenantId;
	private final OrderId orderId;
	private final StoreId storeId;
	private final List<ProductQuantity> reservations;
	private final ZonedDateTime createdAt;

	public ReserveInventoryCommand(TenantId tenantId, OrderId orderId, StoreId storeId,
			List<ProductQuantity> reservations) {
		this.id = UUID.randomUUID();
		this.tenantId = tenantId;
		this.orderId = orderId;
		this.storeId = storeId;
		this.reservations = reservations;
		this.createdAt = ZonedDateTime.now(ZoneId.of("UTC"));
	}

	public UUID getId() {
		return id;
	}

	public List<ProductQuantity> getReservations() {
		return reservations;
	}

	public ZonedDateTime getCreatedAt() {
		return createdAt;
	}

	public TenantId getTenantId() {
		return tenantId;
	}

	public OrderId getOrderId() {
		return orderId;
	}

	public StoreId getStoreId() {
		return storeId;
	}

	public record ProductQuantity(ProductId productId,
			Long quantity) {
	}
}
