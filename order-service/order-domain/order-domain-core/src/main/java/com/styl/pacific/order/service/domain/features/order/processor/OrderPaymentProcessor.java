/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.processor;

import com.styl.pacific.domain.enums.order.OrderType;
import com.styl.pacific.domain.valueobject.Money;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public interface OrderPaymentProcessor {
	String generatePaymentReference(Long id, OrderType type, String currencyCode, Money totalAmount, UUID nonce);

	Long getOrderId(String paymentReference);

	boolean validate(Long id, OrderType type, String currencyCode, Money totalAmount, UUID nonce,
			String currentPaymentReference, String paymentReference);

	Optional<OrderType> getOrderType(String paymentReference);
}
