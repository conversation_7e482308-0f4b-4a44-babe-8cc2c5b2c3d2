/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.menuboard.entity;

import com.styl.pacific.domain.entity.BaseEntity;
import com.styl.pacific.domain.valueobject.MenuBoardId;
import com.styl.pacific.domain.valueobject.MenuBoardImageId;
import java.time.Instant;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class MenuBoardImage extends BaseEntity<MenuBoardImageId> {
	private MenuBoardId menuBoardId;
	private String imagePath;
	private Instant createdAt;
	private Instant updatedAt;

	private MenuBoardImage(Builder builder) {
		setId(builder.id);
		setMenuBoardId(builder.menuBoardId);
		setImagePath(builder.imagePath);
		setCreatedAt(builder.createdAt);
		setUpdatedAt(builder.updatedAt);
	}

	public static Builder builder() {
		return new Builder();
	}

	public MenuBoardId getMenuBoardId() {
		return menuBoardId;
	}

	public void setMenuBoardId(MenuBoardId menuBoardId) {
		this.menuBoardId = menuBoardId;
	}

	public String getImagePath() {
		return imagePath;
	}

	public void setImagePath(String imagePath) {
		this.imagePath = imagePath;
	}

	public Instant getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Instant createdAt) {
		this.createdAt = createdAt;
	}

	public Instant getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Instant updatedAt) {
		this.updatedAt = updatedAt;
	}

	public static final class Builder {
		private MenuBoardImageId id;
		private MenuBoardId menuBoardId;
		private String imagePath;
		private Instant createdAt;
		private Instant updatedAt;

		private Builder() {
		}

		public Builder id(MenuBoardImageId id) {
			this.id = id;
			return this;
		}

		public Builder menuBoardId(MenuBoardId menuBoardId) {
			this.menuBoardId = menuBoardId;
			return this;
		}

		public Builder imagePath(String imagePath) {
			this.imagePath = imagePath;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public MenuBoardImage build() {
			return new MenuBoardImage(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof MenuBoardImage that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(menuBoardId, that.menuBoardId) && Objects.equals(imagePath, that.imagePath) && Objects
				.equals(createdAt, that.createdAt) && Objects.equals(updatedAt, that.updatedAt);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), menuBoardId, imagePath, createdAt, updatedAt);
	}
}
