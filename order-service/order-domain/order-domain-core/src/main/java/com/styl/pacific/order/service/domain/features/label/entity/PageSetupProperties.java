/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.label.entity;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class PageSetupProperties {
	private String pageName;
	private Integer pageWidth;
	private Integer pageHeight;
	private Integer pageMarginTop;
	private Integer pageMarginBottom;
	private Integer pageMarginLeft;
	private Integer pageMarginRight;

	private PageSetupProperties(Builder builder) {
		setPageName(builder.pageName);
		setPageWidth(builder.pageWidth);
		setPageHeight(builder.pageHeight);
		setPageMarginTop(builder.pageMarginTop);
		setPageMarginBottom(builder.pageMarginBottom);
		setPageMarginLeft(builder.pageMarginLeft);
		setPageMarginRight(builder.pageMarginRight);
	}

	public static Builder builder() {
		return new Builder();
	}

	public String getPageName() {
		return pageName;
	}

	public void setPageName(String pageName) {
		this.pageName = pageName;
	}

	public Integer getPageWidth() {
		return pageWidth;
	}

	public void setPageWidth(Integer pageWidth) {
		this.pageWidth = pageWidth;
	}

	public Integer getPageHeight() {
		return pageHeight;
	}

	public void setPageHeight(Integer pageHeight) {
		this.pageHeight = pageHeight;
	}

	public Integer getPageMarginTop() {
		return pageMarginTop;
	}

	public void setPageMarginTop(Integer pageMarginTop) {
		this.pageMarginTop = pageMarginTop;
	}

	public Integer getPageMarginBottom() {
		return pageMarginBottom;
	}

	public void setPageMarginBottom(Integer pageMarginBottom) {
		this.pageMarginBottom = pageMarginBottom;
	}

	public Integer getPageMarginLeft() {
		return pageMarginLeft;
	}

	public void setPageMarginLeft(Integer pageMarginLeft) {
		this.pageMarginLeft = pageMarginLeft;
	}

	public Integer getPageMarginRight() {
		return pageMarginRight;
	}

	public void setPageMarginRight(Integer pageMarginRight) {
		this.pageMarginRight = pageMarginRight;
	}

	public static final class Builder {
		private String pageName;
		private Integer pageWidth;
		private Integer pageHeight;
		private Integer pageMarginTop;
		private Integer pageMarginBottom;
		private Integer pageMarginLeft;
		private Integer pageMarginRight;

		private Builder() {
		}

		public Builder pageName(String pageName) {
			this.pageName = pageName;
			return this;
		}

		public Builder pageWidth(Integer pageWidth) {
			this.pageWidth = pageWidth;
			return this;
		}

		public Builder pageHeight(Integer pageHeight) {
			this.pageHeight = pageHeight;
			return this;
		}

		public Builder pageMarginTop(Integer pageMarginTop) {
			this.pageMarginTop = pageMarginTop;
			return this;
		}

		public Builder pageMarginBottom(Integer pageMarginBottom) {
			this.pageMarginBottom = pageMarginBottom;
			return this;
		}

		public Builder pageMarginLeft(Integer pageMarginLeft) {
			this.pageMarginLeft = pageMarginLeft;
			return this;
		}

		public Builder pageMarginRight(Integer pageMarginRight) {
			this.pageMarginRight = pageMarginRight;
			return this;
		}

		public PageSetupProperties build() {
			return new PageSetupProperties(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof PageSetupProperties that))
			return false;
		return Objects.equals(pageName, that.pageName) && Objects.equals(pageWidth, that.pageWidth) && Objects.equals(
				pageHeight, that.pageHeight) && Objects.equals(pageMarginTop, that.pageMarginTop) && Objects.equals(
						pageMarginBottom, that.pageMarginBottom) && Objects.equals(pageMarginLeft, that.pageMarginLeft)
				&& Objects.equals(pageMarginRight, that.pageMarginRight);
	}

	@Override
	public int hashCode() {
		return Objects.hash(pageName, pageWidth, pageHeight, pageMarginTop, pageMarginBottom, pageMarginLeft,
				pageMarginRight);
	}
}
