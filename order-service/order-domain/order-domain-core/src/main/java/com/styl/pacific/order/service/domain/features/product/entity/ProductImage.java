/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.product.entity;

import com.styl.pacific.domain.entity.BaseEntity;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.order.service.domain.features.product.valueobjects.ProductImageId;
import java.time.Instant;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ProductImage extends BaseEntity<ProductImageId> {
	private ProductId productId;
	private Integer position;
	private String imagePath;
	private Instant createdAt;
	private Instant updatedAt;

	private ProductImage(Builder builder) {
		setId(builder.id);
		setProductId(builder.productId);
		setPosition(builder.position);
		setImagePath(builder.imagePath);
		setCreatedAt(builder.createdAt);
		setUpdatedAt(builder.updatedAt);
	}

	public static Builder builder() {
		return new Builder();
	}

	public ProductId getProductId() {
		return productId;
	}

	public void setProductId(ProductId productId) {
		this.productId = productId;
	}

	public Integer getPosition() {
		return position;
	}

	public void setPosition(Integer position) {
		this.position = position;
	}

	public String getImagePath() {
		return imagePath;
	}

	public void setImagePath(String imagePath) {
		this.imagePath = imagePath;
	}

	public Instant getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Instant createdAt) {
		this.createdAt = createdAt;
	}

	public Instant getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Instant updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof ProductImage that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(productId, that.productId) && Objects.equals(position, that.position) && Objects.equals(
				imagePath, that.imagePath) && Objects.equals(createdAt, that.createdAt) && Objects.equals(updatedAt,
						that.updatedAt);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), productId, position, imagePath, createdAt, updatedAt);
	}

	public static final class Builder {
		private ProductImageId id;
		private ProductId productId;
		private Integer position;
		private String imagePath;
		private Instant createdAt;
		private Instant updatedAt;

		private Builder() {
		}

		public Builder id(ProductImageId id) {
			this.id = id;
			return this;
		}

		public Builder productId(ProductId productId) {
			this.productId = productId;
			return this;
		}

		public Builder position(Integer position) {
			this.position = position;
			return this;
		}

		public Builder imagePath(String imagePath) {
			this.imagePath = imagePath;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public ProductImage build() {
			return new ProductImage(this);
		}
	}
}
