/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.entity;

import com.styl.pacific.domain.entity.AggregateRoot;
import com.styl.pacific.domain.enums.order.OrderCancellationType;
import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.PaymentMethodId;
import com.styl.pacific.domain.valueobject.PaymentTransactionId;
import com.styl.pacific.domain.valueobject.PreOrderId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.preorder.order.enums.PreOrderStatus;
import com.styl.pacific.order.service.domain.features.preorder.order.exception.PreOrderCancelException;
import com.styl.pacific.order.service.domain.features.preorder.order.exception.PreOrderExpireException;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * <AUTHOR>
 */
public class PreOrder extends AggregateRoot<PreOrderId> {
	private String preOrderNumber;
	private String businessName;
	private Long version;
	private String systemSource;
	private UserId issuerId;
	private UserId customerId;
	private String customerName;
	private String customerEmail;
	private List<Order> subOrders;

	private PreOrderStatus status;

	private Money subtotalAmount;
	private String taxName;
	private String taxRegNo;
	private BigDecimal taxRate;
	private Boolean taxInclude;
	private Money taxAmount;
	private Money discountAmount;
	private Money serviceChargeAmount;
	private Money totalAmount;
	private String currencyCode;

	private PaymentMethodId paymentMethodId;
	private String paymentMethodName;
	private String paymentRef;
	private PaymentTransactionId paymentTransactionId;
	private OrderPaymentStatus paymentStatus;
	private Boolean refundable;
	private UUID nonce;

	private Instant cancellationDueAt;
	private OrderCancellationType cancellationType;
	private String cancelReason;
	private Instant canceledAt;
	private Instant expiredAt;

	private Instant createdAt;
	private Instant updatedAt;

	private PreOrder(Builder builder) {
		setId(builder.id);
		setTenantId(builder.tenantId);
		setSystemSource(builder.systemSource);
		setPreOrderNumber(builder.preOrderNumber);
		setBusinessName(builder.businessName);
		setVersion(builder.version);
		setIssuerId(builder.issuerId);
		setCustomerId(builder.customerId);
		setCustomerName(builder.customerName);
		setCustomerEmail(builder.customerEmail);
		setSubOrders(builder.subOrders);
		setStatus(builder.status);
		setSubtotalAmount(builder.subtotalAmount);
		setServiceChargeAmount(builder.serviceChargeAmount);
		setTaxName(builder.taxName);
		setTaxRegNo(builder.taxRegNo);
		setTaxRate(builder.taxRate);
		setTaxInclude(builder.taxInclude);
		setTaxAmount(builder.taxAmount);
		setDiscountAmount(builder.discountAmount);
		setTotalAmount(builder.totalAmount);
		setCurrencyCode(builder.currencyCode);
		setPaymentMethodId(builder.paymentMethodId);
		setPaymentMethodName(builder.paymentMethodName);
		setPaymentRef(builder.paymentRef);
		setPaymentTransactionId(builder.paymentTransactionId);
		setPaymentStatus(builder.paymentStatus);
		setRefundable(builder.refundable);
		setNonce(builder.nonce);
		setCancellationDueAt(builder.cancellationDueAt);
		setCancellationType(builder.cancellationType);
		setCancelReason(builder.cancelReason);
		setCanceledAt(builder.canceledAt);
		setExpiredAt(builder.expiredAt);
		setCreatedAt(builder.createdAt);
		setUpdatedAt(builder.updatedAt);
	}

	public void initialize() {
		setStatus(PreOrderStatus.PENDING);
		setCreatedAt(Instant.now());
		setUpdatedAt(Instant.now());
	}

	public void cancel(String reason, OrderCancellationType cancellationType, boolean isRefundable,
			boolean ignoreExpired) {
		if (PreOrderStatus.CANCELLED.equals(status)) {
			throw new PreOrderCancelException("PreOrder is already cancelled");
		}
		if (!ignoreExpired && Objects.nonNull(cancellationDueAt) && Instant.now()
				.isAfter(cancellationDueAt)) {
			throw new PreOrderCancelException(String.format("PreOrder %s is already expired", getId().getValue()));
		}
		if (!PreOrderStatus.PENDING.equals(status)) {
			throw new PreOrderCancelException(String.format("PreOrder %s is not able to cancel", getId().getValue()));
		}
		setStatus(PreOrderStatus.CANCELLED);
		setCancellationType(cancellationType);
		setCancelReason(reason);
		setCanceledAt(Instant.now());
		setPaymentStatus(OrderPaymentStatus.CANCELLED);
		for (Order subOrder : subOrders) {
			try {
				subOrder.cancel(reason, cancellationType, isRefundable, ignoreExpired);
			} catch (Exception ignored) {
			}
		}
	}

	public void markAsExpiration() {
		if (PreOrderStatus.CANCELLED.equals(status)) {
			return;
		}
		if (Instant.now()
				.isBefore(expiredAt)) {
			return;
		}
		if (!Objects.equals(PreOrderStatus.PENDING, status)) {
			throw new PreOrderExpireException(String.format("PreOrder %s is not able to expire", getId().getValue()));
		}
		setStatus(PreOrderStatus.CANCELLED);
		setCancellationType(OrderCancellationType.EXPIRED_ORDER);
		setCancelReason("PreOrder expired");
		setCanceledAt(Instant.now());
		setPaymentStatus(OrderPaymentStatus.CANCELLED);
		for (Order subOrder : subOrders) {
			try {
				subOrder.markAsExpiration();
			} catch (Exception ignored) {
			}
		}
	}

	public static Builder builder() {
		return new Builder();
	}

	public String getSystemSource() {
		return systemSource;
	}

	public void setSystemSource(String systemSource) {
		this.systemSource = systemSource;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public UserId getIssuerId() {
		return issuerId;
	}

	public void setIssuerId(UserId issuerId) {
		this.issuerId = issuerId;
	}

	public UserId getCustomerId() {
		return customerId;
	}

	public void setCustomerId(UserId customerId) {
		this.customerId = customerId;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getCustomerEmail() {
		return customerEmail;
	}

	public void setCustomerEmail(String customerEmail) {
		this.customerEmail = customerEmail;
	}

	public String getPreOrderNumber() {
		return preOrderNumber;
	}

	public void setPreOrderNumber(String preOrderNumber) {
		this.preOrderNumber = preOrderNumber;
	}

	public String getBusinessName() {
		return businessName;
	}

	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}

	public String getTaxRegNo() {
		return taxRegNo;
	}

	public void setTaxRegNo(String taxRegNo) {
		this.taxRegNo = taxRegNo;
	}

	public List<Order> getSubOrders() {
		return subOrders;
	}

	public void setSubOrders(List<Order> subOrders) {
		this.subOrders = subOrders;
	}

	public PreOrderStatus getStatus() {
		return status;
	}

	public void setStatus(PreOrderStatus status) {
		this.status = status;
	}

	public Money getSubtotalAmount() {
		return subtotalAmount;
	}

	public void setSubtotalAmount(Money subtotalAmount) {
		this.subtotalAmount = subtotalAmount;
	}

	public Money getServiceChargeAmount() {
		return serviceChargeAmount;
	}

	public void setServiceChargeAmount(Money serviceChargeAmount) {
		this.serviceChargeAmount = serviceChargeAmount;
	}

	public String getTaxName() {
		return taxName;
	}

	public void setTaxName(String taxName) {
		this.taxName = taxName;
	}

	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	public Boolean getTaxInclude() {
		return taxInclude;
	}

	public void setTaxInclude(Boolean taxInclude) {
		this.taxInclude = taxInclude;
	}

	public Money getTaxAmount() {
		return taxAmount;
	}

	public void setTaxAmount(Money taxAmount) {
		this.taxAmount = taxAmount;
	}

	public Money getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(Money discountAmount) {
		this.discountAmount = discountAmount;
	}

	public Money getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(Money totalAmount) {
		this.totalAmount = totalAmount;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public PaymentMethodId getPaymentMethodId() {
		return paymentMethodId;
	}

	public void setPaymentMethodId(PaymentMethodId paymentMethodId) {
		this.paymentMethodId = paymentMethodId;
	}

	public String getPaymentMethodName() {
		return paymentMethodName;
	}

	public void setPaymentMethodName(String paymentMethodName) {
		this.paymentMethodName = paymentMethodName;
	}

	public String getPaymentRef() {
		return paymentRef;
	}

	public void setPaymentRef(String paymentRef) {
		this.paymentRef = paymentRef;
	}

	public PaymentTransactionId getPaymentTransactionId() {
		return paymentTransactionId;
	}

	public void setPaymentTransactionId(PaymentTransactionId paymentTransactionId) {
		this.paymentTransactionId = paymentTransactionId;
	}

	public OrderPaymentStatus getPaymentStatus() {
		return paymentStatus;
	}

	public void setPaymentStatus(OrderPaymentStatus paymentStatus) {
		this.paymentStatus = paymentStatus;
	}

	public Boolean getRefundable() {
		return refundable;
	}

	public void setRefundable(Boolean refundable) {
		this.refundable = refundable;
	}

	public UUID getNonce() {
		return nonce;
	}

	public void setNonce(UUID nonce) {
		this.nonce = nonce;
	}

	public Instant getCancellationDueAt() {
		return cancellationDueAt;
	}

	public void setCancellationDueAt(Instant cancellationDueAt) {
		this.cancellationDueAt = cancellationDueAt;
	}

	public OrderCancellationType getCancellationType() {
		return cancellationType;
	}

	public void setCancellationType(OrderCancellationType cancellationType) {
		this.cancellationType = cancellationType;
	}

	public String getCancelReason() {
		return cancelReason;
	}

	public void setCancelReason(String cancelReason) {
		this.cancelReason = cancelReason;
	}

	public Instant getCanceledAt() {
		return canceledAt;
	}

	public void setCanceledAt(Instant canceledAt) {
		this.canceledAt = canceledAt;
	}

	public Instant getExpiredAt() {
		return expiredAt;
	}

	public void setExpiredAt(Instant expiredAt) {
		this.expiredAt = expiredAt;
	}

	public Instant getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Instant createdAt) {
		this.createdAt = createdAt;
	}

	public Instant getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Instant updatedAt) {
		this.updatedAt = updatedAt;
	}

	public static final class Builder {
		private PreOrderId id;
		private TenantId tenantId;
		private String systemSource;
		private String preOrderNumber;
		private String businessName;
		private Long version;
		private UserId issuerId;
		private UserId customerId;
		private String customerName;
		private String customerEmail;
		private List<Order> subOrders;
		private PreOrderStatus status;
		private Money subtotalAmount;
		private Money serviceChargeAmount;
		private String taxName;
		private String taxRegNo;
		private BigDecimal taxRate;
		private Boolean taxInclude;
		private Money taxAmount;
		private Money discountAmount;
		private Money totalAmount;
		private String currencyCode;
		private PaymentMethodId paymentMethodId;
		private String paymentMethodName;
		private String paymentRef;
		private PaymentTransactionId paymentTransactionId;
		private OrderPaymentStatus paymentStatus;
		private Boolean refundable;
		private UUID nonce;
		private Instant cancellationDueAt;
		private OrderCancellationType cancellationType;
		private String cancelReason;
		private Instant canceledAt;
		private Instant expiredAt;
		private Instant createdAt;
		private Instant updatedAt;

		private Builder() {
		}

		public Builder id(PreOrderId id) {
			this.id = id;
			return this;
		}

		public Builder tenantId(TenantId tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder systemSource(String systemSource) {
			this.systemSource = systemSource;
			return this;
		}

		public Builder preOrderNumber(String preOrderNumber) {
			this.preOrderNumber = preOrderNumber;
			return this;
		}

		public Builder businessName(String businessName) {
			this.businessName = businessName;
			return this;
		}

		public Builder version(Long version) {
			this.version = version;
			return this;
		}

		public Builder issuerId(UserId issuerId) {
			this.issuerId = issuerId;
			return this;
		}

		public Builder customerId(UserId customerId) {
			this.customerId = customerId;
			return this;
		}

		public Builder customerName(String customerName) {
			this.customerName = customerName;
			return this;
		}

		public Builder customerEmail(String customerEmail) {
			this.customerEmail = customerEmail;
			return this;
		}

		public Builder subOrders(List<Order> subOrders) {
			this.subOrders = subOrders;
			return this;
		}

		public Builder status(PreOrderStatus status) {
			this.status = status;
			return this;
		}

		public Builder subtotalAmount(Money subtotalAmount) {
			this.subtotalAmount = subtotalAmount;
			return this;
		}

		public Builder serviceChargeAmount(Money serviceChargeAmount) {
			this.serviceChargeAmount = serviceChargeAmount;
			return this;
		}

		public Builder taxName(String taxName) {
			this.taxName = taxName;
			return this;
		}

		public Builder taxRegNo(String taxRegNo) {
			this.taxRegNo = taxRegNo;
			return this;
		}

		public Builder taxRate(BigDecimal taxRate) {
			this.taxRate = taxRate;
			return this;
		}

		public Builder taxInclude(Boolean taxInclude) {
			this.taxInclude = taxInclude;
			return this;
		}

		public Builder taxAmount(Money taxAmount) {
			this.taxAmount = taxAmount;
			return this;
		}

		public Builder discountAmount(Money discountAmount) {
			this.discountAmount = discountAmount;
			return this;
		}

		public Builder totalAmount(Money totalAmount) {
			this.totalAmount = totalAmount;
			return this;
		}

		public Builder currencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
			return this;
		}

		public Builder paymentMethodId(PaymentMethodId paymentMethodId) {
			this.paymentMethodId = paymentMethodId;
			return this;
		}

		public Builder paymentMethodName(String paymentMethodName) {
			this.paymentMethodName = paymentMethodName;
			return this;
		}

		public Builder paymentRef(String paymentRef) {
			this.paymentRef = paymentRef;
			return this;
		}

		public Builder paymentTransactionId(PaymentTransactionId paymentTransactionId) {
			this.paymentTransactionId = paymentTransactionId;
			return this;
		}

		public Builder paymentStatus(OrderPaymentStatus paymentStatus) {
			this.paymentStatus = paymentStatus;
			return this;
		}

		public Builder refundable(Boolean refundable) {
			this.refundable = refundable;
			return this;
		}

		public Builder nonce(UUID nonce) {
			this.nonce = nonce;
			return this;
		}

		public Builder cancellationDueAt(Instant cancellationDueAt) {
			this.cancellationDueAt = cancellationDueAt;
			return this;
		}

		public Builder cancellationType(OrderCancellationType cancellationType) {
			this.cancellationType = cancellationType;
			return this;
		}

		public Builder cancelReason(String cancelReason) {
			this.cancelReason = cancelReason;
			return this;
		}

		public Builder canceledAt(Instant canceledAt) {
			this.canceledAt = canceledAt;
			return this;
		}

		public Builder expiredAt(Instant expiredAt) {
			this.expiredAt = expiredAt;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public PreOrder build() {
			return new PreOrder(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;

		if (!(o instanceof PreOrder preOrder))
			return false;

		return new EqualsBuilder().appendSuper(super.equals(o))
				.append(preOrderNumber, preOrder.preOrderNumber)
				.append(businessName, preOrder.businessName)
				.append(version, preOrder.version)
				.append(systemSource, preOrder.systemSource)
				.append(issuerId, preOrder.issuerId)
				.append(customerId, preOrder.customerId)
				.append(customerName, preOrder.customerName)
				.append(customerEmail, preOrder.customerEmail)
				.append(subOrders, preOrder.subOrders)
				.append(status, preOrder.status)
				.append(subtotalAmount, preOrder.subtotalAmount)
				.append(taxName, preOrder.taxName)
				.append(taxRegNo, preOrder.taxRegNo)
				.append(taxRate, preOrder.taxRate)
				.append(taxInclude, preOrder.taxInclude)
				.append(taxAmount, preOrder.taxAmount)
				.append(discountAmount, preOrder.discountAmount)
				.append(serviceChargeAmount, preOrder.serviceChargeAmount)
				.append(totalAmount, preOrder.totalAmount)
				.append(currencyCode, preOrder.currencyCode)
				.append(paymentMethodId, preOrder.paymentMethodId)
				.append(paymentMethodName, preOrder.paymentMethodName)
				.append(paymentRef, preOrder.paymentRef)
				.append(paymentTransactionId, preOrder.paymentTransactionId)
				.append(paymentStatus, preOrder.paymentStatus)
				.append(refundable, preOrder.refundable)
				.append(nonce, preOrder.nonce)
				.append(cancellationDueAt, preOrder.cancellationDueAt)
				.append(cancellationType, preOrder.cancellationType)
				.append(cancelReason, preOrder.cancelReason)
				.append(canceledAt, preOrder.canceledAt)
				.append(expiredAt, preOrder.expiredAt)
				.append(createdAt, preOrder.createdAt)
				.append(updatedAt, preOrder.updatedAt)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).appendSuper(super.hashCode())
				.append(preOrderNumber)
				.append(businessName)
				.append(version)
				.append(systemSource)
				.append(issuerId)
				.append(customerId)
				.append(customerName)
				.append(customerEmail)
				.append(subOrders)
				.append(status)
				.append(subtotalAmount)
				.append(taxName)
				.append(taxRegNo)
				.append(taxRate)
				.append(taxInclude)
				.append(taxAmount)
				.append(discountAmount)
				.append(serviceChargeAmount)
				.append(totalAmount)
				.append(currencyCode)
				.append(paymentMethodId)
				.append(paymentMethodName)
				.append(paymentRef)
				.append(paymentTransactionId)
				.append(paymentStatus)
				.append(refundable)
				.append(nonce)
				.append(cancellationDueAt)
				.append(cancellationType)
				.append(cancelReason)
				.append(canceledAt)
				.append(expiredAt)
				.append(createdAt)
				.append(updatedAt)
				.toHashCode();
	}
}
