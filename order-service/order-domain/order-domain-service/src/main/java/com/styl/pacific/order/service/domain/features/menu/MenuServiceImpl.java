/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.menu;

import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.StoreStatus;
import com.styl.pacific.domain.valueobject.MenuId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.menu.dto.command.CreateMenuCommand;
import com.styl.pacific.order.service.domain.features.menu.dto.command.UpdateMenuCommand;
import com.styl.pacific.order.service.domain.features.menu.dto.query.MenuFilter;
import com.styl.pacific.order.service.domain.features.menu.dto.query.MenuQuery;
import com.styl.pacific.order.service.domain.features.menu.dto.query.PaginationMenuQuery;
import com.styl.pacific.order.service.domain.features.menu.entity.Menu;
import com.styl.pacific.order.service.domain.features.menu.enums.MenuStatus;
import com.styl.pacific.order.service.domain.features.menu.exception.MenuNotFoundException;
import com.styl.pacific.order.service.domain.features.menu.id.generator.MenuIdGenerator;
import com.styl.pacific.order.service.domain.features.menu.mapper.MenuDataMapper;
import com.styl.pacific.order.service.domain.features.menu.ports.input.service.MenuService;
import com.styl.pacific.order.service.domain.features.menu.ports.output.repository.MenuRepository;
import com.styl.pacific.order.service.domain.features.store.dto.StoreDto;
import com.styl.pacific.order.service.domain.features.store.dto.query.PaginationStoreQuery;
import com.styl.pacific.order.service.domain.features.store.dto.query.StoreFilter;
import com.styl.pacific.order.service.domain.features.store.ports.output.repository.StoreRepository;
import com.styl.pacific.order.service.domain.features.tenant.dto.TenantDto;
import com.styl.pacific.order.service.domain.features.tenant.helper.TenantCheckHelper;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class MenuServiceImpl implements MenuService {
	private static final Logger log = LoggerFactory.getLogger(MenuServiceImpl.class);
	private final MenuRepository menuRepository;
	private final StoreRepository storeRepository;

	private final MenuIdGenerator menuIdGenerator;

	private final TenantCheckHelper tenantCheckHelper;

	private static final String MENU_NOT_FOUND_EXCEPTION_MESSAGE = "Menu {} not found";
	private static final String MENU_NOT_FOUND_EXCEPTION_MESSAGE_2 = "Menu %s not found";

	@Override
	@Transactional(readOnly = true)
	public Menu findById(TenantId tenantId, MenuId id) {
		return menuRepository.findById(tenantId, id)
				.orElseThrow(() -> {
					log.warn(MENU_NOT_FOUND_EXCEPTION_MESSAGE, id);
					return new MenuNotFoundException(String.format(MENU_NOT_FOUND_EXCEPTION_MESSAGE_2, id.getValue()));
				});
	}

	@Override
	@Transactional(readOnly = true)
	public Content<Menu> findAll(TenantId tenantId, MenuQuery query) {
		int page = -1;
		Integer totalPage = null;
		MenuFilter menuFilter = Optional.ofNullable(query.filter())
				.orElse(MenuFilter.builder()
						.build());
		List<Menu> result = new ArrayList<>();
		do {
			PaginationMenuQuery newQuery = new PaginationMenuQuery(menuFilter, 20, page++, query.sortDirection(), query
					.sortFields());
			Paging<Menu> paging = menuRepository.findAllPaging(tenantId, newQuery);
			List<Menu> content = paging.getContent();
			if (Objects.isNull(totalPage)) {
				totalPage = paging.getTotalPages();
			}
			if (content.isEmpty()) {
				break;
			}
			result.addAll(content);
		} while ((page + 1) < totalPage);
		if (Objects.nonNull(menuFilter.storeStatuses()) && !menuFilter.storeStatuses()
				.isEmpty()) {
			filterMenuByStoreStatus(result, menuFilter.storeStatuses());
		}

		return Content.<Menu>builder()
				.content(result)
				.build();
	}

	@Override
	@Transactional
	public Menu create(TenantId tenantId, CreateMenuCommand command) {
		TenantDto tenant = tenantCheckHelper.tenantCheck(tenantId);
		storeCheck(new StoreId(command.storeId()));
		Menu menu = MenuDataMapper.INSTANCE.createMenuCommandToMenu(command);
		menu.setId(menuIdGenerator.nextId());
		menu.setTenantId(tenantId);

		return menuRepository.create(menu);
	}

	@Override
	@Transactional
	public Menu update(TenantId tenantId, MenuId id, UpdateMenuCommand command) {
		Menu menu = menuCheck(tenantId, id);
		MenuDataMapper.INSTANCE.updateMenuCommandToMenu(menu, command);
		return menuRepository.update(menu);
	}

	@Override
	@Transactional
	public void deleteById(TenantId tenantId, MenuId id) {
		menuCheckExists(tenantId, id);
		menuRepository.deleteById(tenantId, id);
	}

	@Override
	@Transactional
	public void activeById(TenantId tenantId, MenuId id) {
		menuCheckExists(tenantId, id);
		menuRepository.changeStatus(tenantId, id, MenuStatus.ACTIVE);
	}

	@Override
	@Transactional
	public void deActiveById(TenantId tenantId, MenuId id) {
		menuCheckExists(tenantId, id);
		menuRepository.changeStatus(tenantId, id, MenuStatus.INACTIVE);
	}

	@Override
	@Transactional(readOnly = true)
	public Paging<Menu> findAllPaging(TenantId tenantId, PaginationMenuQuery query) {
		MenuFilter menuFilter = Optional.ofNullable(query.getFilter())
				.orElse(MenuFilter.builder()
						.build());
		Paging<Menu> paging = menuRepository.findAllPaging(tenantId, query);
		List<Menu> result = new ArrayList<>(paging.getContent());
		if (Objects.nonNull(menuFilter.storeStatuses()) && !menuFilter.storeStatuses()
				.isEmpty()) {
			filterMenuByStoreStatus(result, menuFilter.storeStatuses());
		}
		return Paging.<Menu>builder()
				.content(result)
				.page(paging.getPage())
				.totalElements(paging.getTotalElements())
				.totalPages(paging.getTotalPages())
				.sort(paging.getSort())
				.build();
	}

	private Menu menuCheck(TenantId tenantId, MenuId menuId) {
		return menuRepository.findById(tenantId, menuId)
				.orElseThrow(() -> {
					log.warn(MENU_NOT_FOUND_EXCEPTION_MESSAGE, menuId.getValue());
					return new MenuNotFoundException(String.format(MENU_NOT_FOUND_EXCEPTION_MESSAGE_2, menuId
							.getValue()));
				});
	}

	private void menuCheckExists(TenantId tenantId, MenuId menuId) {
		boolean isExist = menuRepository.existsById(tenantId, menuId);
		if (!isExist) {
			log.warn(MENU_NOT_FOUND_EXCEPTION_MESSAGE, menuId.getValue());
			throw new MenuNotFoundException(String.format(MENU_NOT_FOUND_EXCEPTION_MESSAGE_2, menuId.getValue()));
		}
	}

	private StoreDto storeCheck(StoreId storeId) {
		Optional<StoreDto> storeDtoOpt = storeRepository.findById(storeId);
		if (storeDtoOpt.isEmpty()) {
			log.warn("Store {} not found", storeId.getValue());
			throw new MenuNotFoundException(String.format("Store %s not found", storeId.getValue()));
		}
		return storeDtoOpt.get();
	}

	private void filterMenuByStoreStatus(List<Menu> menus, List<StoreStatus> storeStatuses) {
		Map<Long, List<Menu>> storeMenuMap = menus.stream()
				.collect(Collectors.groupingBy(menu -> menu.getStoreId()
						.getValue()));
		List<StoreId> storeIds = storeMenuMap.keySet()
				.stream()
				.filter(Objects::nonNull)
				.map(StoreId::new)
				.toList();
		StoreFilter storeFilter = StoreFilter.builder()
				.storeIds(storeIds.stream()
						.map(StoreId::getValue)
						.toList())
				.build();
		int pageStore = -1;
		Integer totalPageStore = null;
		do {
			Paging<StoreDto> storePaging = storeRepository.findAllPaging(new PaginationStoreQuery(storeFilter, 20,
					pageStore++, null, null));
			if (Objects.isNull(totalPageStore)) {
				totalPageStore = storePaging.getTotalPages();
			}
			List<StoreDto> storeDTOs = storePaging.getContent();
			for (StoreDto storeDTO : storeDTOs) {
				if (!storeStatuses.contains(storeDTO.status())) {
					List<Menu> menuStubDTOs = storeMenuMap.get(storeDTO.storeId()
							.getValue());
					if (menuStubDTOs != null) {
						menus.removeAll(menuStubDTOs);
					}
				}
			}
		} while ((pageStore + 1) < totalPageStore);
	}
}
