/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.assignment.handlers;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.PreOrderMenuAssignmentDto;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuAssignmentPagingQuery;
import com.styl.pacific.order.service.domain.features.preorder.assignment.ports.output.repository.PreOrderMenuAssignmentRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PreOrderMenuAssignmentQueryHandler {
	private final PreOrderMenuAssignmentRepository preOrderMenuAssignmentRepository;

	@Transactional(readOnly = true)
	public Paging<PreOrderMenuAssignmentDto> findAllPaging(TenantId tenantId, PreOrderMenuAssignmentPagingQuery query) {
		return preOrderMenuAssignmentRepository.findAllPaging(tenantId, query);
	}
}
