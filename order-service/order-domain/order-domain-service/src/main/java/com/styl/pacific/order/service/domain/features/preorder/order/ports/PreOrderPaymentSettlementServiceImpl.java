/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.ports;

import com.styl.pacific.domain.enums.PaymentTransactionStatus;
import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.domain.enums.order.OrderStatus;
import com.styl.pacific.domain.enums.order.OrderType;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.PreOrderId;
import com.styl.pacific.order.service.domain.features.common.exception.OrderCommonValidateException;
import com.styl.pacific.order.service.domain.features.common.exception.OrderDomainException;
import com.styl.pacific.order.service.domain.features.notification.NotificationPublisher;
import com.styl.pacific.order.service.domain.features.order.dto.command.payment.OrderPaymentSettlementCommand;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.order.exception.OrderNotFoundException;
import com.styl.pacific.order.service.domain.features.order.processor.OrderPaymentProcessor;
import com.styl.pacific.order.service.domain.features.preorder.order.entity.PreOrder;
import com.styl.pacific.order.service.domain.features.preorder.order.enums.PreOrderStatus;
import com.styl.pacific.order.service.domain.features.preorder.order.exception.PreOrderPaidFailedException;
import com.styl.pacific.order.service.domain.features.preorder.order.ports.input.service.PreOrderPaymentSettlementService;
import com.styl.pacific.order.service.domain.features.preorder.order.ports.output.repository.PreOrderRepository;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class PreOrderPaymentSettlementServiceImpl implements PreOrderPaymentSettlementService {
	private final OrderPaymentProcessor orderPaymentProcessor;
	private final PreOrderRepository preOrderRepository;
	private final NotificationPublisher notificationPublisher;

	@Override
	@Transactional
	public void settlePreOrderPayment(OrderPaymentSettlementCommand command) {
		Long preOrderId = orderPaymentProcessor.getOrderId(command.paymentReference());
		if (Objects.isNull(preOrderId)) {
			throw new OrderDomainException("Invalid payment reference");
		}
		PreOrderId orderId = new PreOrderId(preOrderId);
		Optional<PreOrder> orderOptional = preOrderRepository.findById(command.tenantId(), orderId);
		if (orderOptional.isEmpty()) {
			throw new OrderNotFoundException(String.format("Pre-order with id %s not found", orderId.getValue()));
		}
		PreOrder preOrder = orderOptional.get();
		if (!PreOrderStatus.PENDING.equals(preOrder.getStatus())) {
			return;
		}
		if (!orderPaymentProcessor.validate(orderId.getValue(), OrderType.PRE_ORDER, command.currencyCode(), new Money(
				command.netAmount()), preOrder.getNonce(), preOrder.getPaymentRef(), command.paymentReference())) {
			throw new OrderCommonValidateException(String.format("Payment reference is invalid from order %s", orderId
					.getValue()));
		}

		// Update customer order
		updateCustomerOrder(preOrder, command);

		// Update payment statuses
		updatePaymentStatus(preOrder, command);

		preOrderRepository.save(preOrder);

		notificationPublisher.publish(preOrder, command.paidAt());
	}

	private void updatePaymentStatus(PreOrder order, OrderPaymentSettlementCommand command) {
		order.setPaymentTransactionId(command.paymentTransactionId());
		if (PaymentTransactionStatus.SUCCEEDED.equals(command.transactionStatus())) {
			order.setStatus(PreOrderStatus.PAID);
			order.setPaymentStatus(OrderPaymentStatus.PAID);
			order.getSubOrders()
					.forEach(subOrder -> {
						subOrder.setPaymentTransactionId(command.paymentTransactionId());
						subOrder.setStatus(OrderStatus.PAID);
						subOrder.setPaymentStatus(OrderPaymentStatus.PAID);
					});
		} else {
			throw new PreOrderPaidFailedException("Pre-order payment failed", order, true);
		}
	}

	private void updateCustomerOrder(PreOrder order, OrderPaymentSettlementCommand command) {
		if (Objects.isNull(order.getCustomerId()) && StringUtils.isBlank(order.getCustomerEmail()) && StringUtils
				.isBlank(order.getCustomerName())) {
			order.setCustomerId(command.customerId());
			order.setCustomerName(command.customerName());
			order.setCustomerEmail(command.customerEmail());
			order.getSubOrders()
					.forEach(subOrder -> updateCustomerDetails(subOrder, command));
		}
	}

	private void updateCustomerDetails(Order order, OrderPaymentSettlementCommand command) {
		order.setCustomerId(command.customerId());
		order.setCustomerName(command.customerName());
		order.setCustomerEmail(command.customerEmail());
	}
}
