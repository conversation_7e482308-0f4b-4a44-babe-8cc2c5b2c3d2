/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.menu.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.menu.dto.command.arrangement.ArrangementCommand;
import com.styl.pacific.order.service.domain.features.menu.dto.common.arrangement.ArrangementDto;
import com.styl.pacific.order.service.domain.features.menu.entity.Arrangement;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.SubclassExhaustiveStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION, uses = {
		CommonDataMapper.class, MapstructCommonDomainMapper.class, MapstructCommonMapper.class })
public interface ArrangementDataMapper {

	ArrangementDataMapper INSTANCE = Mappers.getMapper(ArrangementDataMapper.class);

	@Mapping(target = "id", ignore = true)
	@Mapping(target = "startTime", source = "command.startTime", qualifiedByName = "stringToLocalTime")
	@Mapping(target = "endTime", source = "command.endTime", qualifiedByName = "stringToLocalTime")
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	Arrangement arrangementCommandToArrangement(ArrangementCommand command);

	ArrangementDto arrangementToArrangementResponse(Arrangement model);

	@Mapping(target = "id", ignore = true)
	@Mapping(target = "startTime", source = "command.startTime", qualifiedByName = "stringToLocalTime")
	@Mapping(target = "endTime", source = "command.endTime", qualifiedByName = "stringToLocalTime")
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	void arrangementCommandToArrangement(@MappingTarget Arrangement arrangement, ArrangementCommand command);

}
