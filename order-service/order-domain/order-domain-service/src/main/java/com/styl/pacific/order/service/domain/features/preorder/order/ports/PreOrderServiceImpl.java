/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.ports;

import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.enums.order.OrderCancellationType;
import com.styl.pacific.domain.valueobject.OrderId;
import com.styl.pacific.domain.valueobject.PreOrderId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.order.exception.UserAccessDomainForbiddenException;
import com.styl.pacific.order.service.domain.features.order.mediators.OrderReversalAsyncMediator;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.PreOrderLightDto;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.PreOrderSummaryDto;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.CancelPreOrderCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.CreatePreOrderCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.query.PreOrderFilter;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.query.PreOrderHistorySummaryFilter;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.query.PreOrderPagingQuery;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.query.PreOrderSummaryQuery;
import com.styl.pacific.order.service.domain.features.preorder.order.entity.PreOrder;
import com.styl.pacific.order.service.domain.features.preorder.order.exception.PreOrderNotFoundException;
import com.styl.pacific.order.service.domain.features.preorder.order.handler.PreOrderCommandHandler;
import com.styl.pacific.order.service.domain.features.preorder.order.handler.PreOrderQueryHandler;
import com.styl.pacific.order.service.domain.features.preorder.order.helper.PreOrderProcessDataHelper;
import com.styl.pacific.order.service.domain.features.preorder.order.helper.PreOrderValidationHelper;
import com.styl.pacific.order.service.domain.features.preorder.order.ports.input.service.PreOrderService;
import com.styl.pacific.order.service.domain.features.tenant.dto.TaxDto;
import com.styl.pacific.order.service.domain.features.tenant.dto.TenantDto;
import com.styl.pacific.order.service.domain.features.tenant.helper.TenantCheckHelper;
import com.styl.pacific.order.service.domain.utils.TokenClaimHelper;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class PreOrderServiceImpl implements PreOrderService {
	private static final Logger log = LoggerFactory.getLogger(PreOrderServiceImpl.class);

	private final PreOrderCommandHandler preOrderCommandHandler;
	private final PreOrderQueryHandler preOrderQueryHandler;

	private final PreOrderProcessDataHelper preOrderProcessDataHelper;
	private final PreOrderValidationHelper preOrderValidationHelper;

	private final OrderReversalAsyncMediator orderReversalAsyncMediator;
	private final TenantCheckHelper tenantCheckHelper;

	@Override
	public PreOrderLightDto create(TenantId tenantId, TokenClaim tokenClaim, CreatePreOrderCommand command) {
		if (Boolean.FALSE.equals(TokenClaimHelper.isCustomerToken(tokenClaim))) {
			throw new UserAccessDomainForbiddenException("User is not allowed to access this domain");
		}
		TenantDto tenant = tenantCheckHelper.tenantCheck(tenantId);
		TaxDto tax = tenantCheckHelper.getCurrentTaxOrNull();
		preOrderValidationHelper.validatePreOrderInput(tenant, tax, new UserId(NumberUtils.createLong(tokenClaim
				.getUserId())), command);
		PreOrder preOrder = preOrderProcessDataHelper.processPreOrder(tenant, tax, tokenClaim, command);
		return preOrderCommandHandler.place(tenantId, preOrder);
	}

	@Override
	public Paging<PreOrderSummaryDto> findSummary(TenantId tenantId, TokenClaim tokenClaim,
			PreOrderSummaryQuery query) {
		if (TokenClaimHelper.isCustomerToken(tokenClaim)) {
			UserId userId = new UserId(Long.parseLong(tokenClaim.getUserId()));
			Set<UserId> finalCustomerIds = Optional.ofNullable(query.getFilter())
					.flatMap(filter -> Optional.ofNullable(filter.customerIds())
							.map(longs -> longs.stream()
									.map(UserId::new)))
					.orElse(Stream.of(userId))
					.collect(Collectors.toSet());

			query = updateQueryWithUserId(query, finalCustomerIds.stream()
					.map(UserId::getValue)
					.collect(Collectors.toSet()));
		}

		return preOrderQueryHandler.findSummaryPaging(tenantId, query);
	}

	private PreOrderSummaryQuery updateQueryWithUserId(PreOrderSummaryQuery query, Set<Long> customerIds) {
		return Optional.ofNullable(query)
				.map(q -> {
					PreOrderHistorySummaryFilter filter = Optional.ofNullable(q.getFilter())
							.map(f -> f.withCustomerIds(customerIds))
							.orElseGet(() -> {
								final var newFilter = PreOrderHistorySummaryFilter.builder();
								if (!customerIds.isEmpty()) {
									newFilter.customerIds(customerIds);
								}
								return newFilter.build();
							});
					return new PreOrderSummaryQuery(filter, q.getSize(), q.getPage(), q.getSortDirection(), q
							.getSortFields());
				})
				.orElse(query);
	}

	@Override
	public Paging<PreOrderLightDto> findAllPaging(TenantId tenantId, TokenClaim tokenClaim, PreOrderPagingQuery query) {
		if (TokenClaimHelper.isCustomerToken(tokenClaim)) {
			UserId userId = new UserId(Long.parseLong(tokenClaim.getUserId()));

			Set<UserId> customerIds = Optional.ofNullable(query.getFilter())
					.map(PreOrderFilter::customerIds)
					.map(longs -> longs.stream()
							.map(UserId::new))
					.orElse(Stream.of(userId))
					.collect(Collectors.toSet());

			query = updatePagingQueryWithUserId(query, customerIds.stream()
					.map(UserId::getValue)
					.collect(Collectors.toSet()));

		}

		return preOrderQueryHandler.findDtoPaging(tenantId, query);
	}

	private PreOrderPagingQuery updatePagingQueryWithUserId(PreOrderPagingQuery query, Set<Long> customerIds) {
		PreOrderFilter filter = Optional.ofNullable(query.getFilter())
				.map(f -> f.withCustomerIds(customerIds))
				.orElseGet(() -> {
					final var newFilter = PreOrderFilter.builder();
					if (!customerIds.isEmpty()) {
						newFilter.customerIds(customerIds);
					}
					return newFilter.build();
				});

		return new PreOrderPagingQuery(filter, query.getSize(), query.getPage(), query.getSortDirection(), query
				.getSortFields());
	}

	@Override
	public PreOrderLightDto findById(TenantId tenantId, TokenClaim tokenClaim, PreOrderId id) {
		UserId userId = Optional.ofNullable(tokenClaim)
				.filter(t -> UserType.CUSTOMER.equals(t.getUserType()))
				.map(t -> new UserId(Long.parseLong(tokenClaim.getUserId())))
				.orElse(null);
		return preOrderQueryHandler.findPreOrderLightDtoByUserAndId(userId, tenantId, id);
	}

	@Override
	public void cancelById(TenantId tenantId, TokenClaim tokenClaim, PreOrderId preOrderId,
			CancelPreOrderCommand command) {
		Optional<PreOrder> preOrderOptional = preOrderCommandHandler.findById(tenantId, preOrderId);
		if (preOrderOptional.isEmpty()) {
			log.info("PreOrder {} not found", preOrderId);
			throw new PreOrderNotFoundException(String.format("PreOrder %s not found", preOrderId.getValue()));
		}
		PreOrder preOrder = preOrderOptional.get();
		preOrderCommandHandler.cancel(preOrder, Optional.ofNullable(tokenClaim)
				.map(TokenClaimHelper::isCustomerToken)
				.filter(c -> c)
				.map(c -> OrderCancellationType.CUSTOMER_REQUEST)
				.orElse(OrderCancellationType.OTHER), command, !TokenClaimHelper.isCustomerToken(tokenClaim));
		List<OrderId> orderIds = preOrder.getSubOrders()
				.stream()
				.map(Order::getId)
				.toList();
		orderReversalAsyncMediator.restoreInventoryOnCancelOrder(tenantId, orderIds);
	}
}
