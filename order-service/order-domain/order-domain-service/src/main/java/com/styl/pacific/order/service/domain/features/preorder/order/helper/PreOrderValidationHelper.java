/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.helper;

import com.styl.pacific.domain.enums.PlatformOrderType;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.PaymentMethodId;
import com.styl.pacific.domain.valueobject.PreOrderMenuId;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.ProductOptionId;
import com.styl.pacific.domain.valueobject.ProductOptionItemId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.OrderServiceChargeCommand;
import com.styl.pacific.order.service.domain.features.order.exception.InvalidOrderException;
import com.styl.pacific.order.service.domain.features.order.exception.InvalidOrderLineItemInfoException;
import com.styl.pacific.order.service.domain.features.order.exception.InvalidOrderPaymentException;
import com.styl.pacific.order.service.domain.features.order.exception.OrderPaymentMethodRequiredException;
import com.styl.pacific.order.service.domain.features.order.exception.PreOrderCutOffTimeException;
import com.styl.pacific.order.service.domain.features.order.exception.PreOrderItemCapacityInSufficientException;
import com.styl.pacific.order.service.domain.features.order.exception.UserAccessDomainForbiddenException;
import com.styl.pacific.order.service.domain.features.order.ports.output.repository.OrderQueryRepository;
import com.styl.pacific.order.service.domain.features.payment.dto.PaymentMethodDto;
import com.styl.pacific.order.service.domain.features.payment.helpers.PaymentCheckHelper;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemLightDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.enums.PreOrderMenuItemStatus;
import com.styl.pacific.order.service.domain.features.preorder.menu.enums.PreOrderMenuStatus;
import com.styl.pacific.order.service.domain.features.preorder.menu.exception.PreOrderMenuUnavailableException;
import com.styl.pacific.order.service.domain.features.preorder.menu.ports.output.repository.PreOrderMenuItemRepository;
import com.styl.pacific.order.service.domain.features.preorder.menu.ports.output.repository.PreOrderMenuRepository;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemId;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.CreatePreOrderCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.CreatePreOrderLineItemCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.PreOrderDetailCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.PreOrderPaymentCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.SubOrderDetailCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.exception.PreOrderItemExceededMaxQuantity;
import com.styl.pacific.order.service.domain.features.product.dto.ProductBriefDto;
import com.styl.pacific.order.service.domain.features.product.entity.ProductOption;
import com.styl.pacific.order.service.domain.features.product.entity.ProductOptionItem;
import com.styl.pacific.order.service.domain.features.product.enums.ProductStatus;
import com.styl.pacific.order.service.domain.features.product.ports.output.repository.ProductOptionRepository;
import com.styl.pacific.order.service.domain.features.product.ports.output.repository.ProductRepository;
import com.styl.pacific.order.service.domain.features.servicecharge.entity.ServiceCharge;
import com.styl.pacific.order.service.domain.features.servicecharge.ports.output.repository.ServiceChargeRepository;
import com.styl.pacific.order.service.domain.features.servicecharge.valueobjects.ServiceChargeId;
import com.styl.pacific.order.service.domain.features.tenant.dto.TaxDto;
import com.styl.pacific.order.service.domain.features.tenant.dto.TenantDto;
import com.styl.pacific.order.service.domain.features.tenant.dto.TenantSettingDto;
import com.styl.pacific.order.service.domain.features.tenant.helper.TenantCheckHelper;
import com.styl.pacific.order.service.domain.features.user.port.output.repository.UserRepository;
import com.styl.pacific.order.service.domain.features.user.port.output.repository.UserSubAccountRepository;
import com.styl.pacific.order.service.domain.utils.TenantHelper;
import com.styl.pacific.order.service.domain.utils.TimeHelper;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PreOrderValidationHelper {
	private final ProductRepository productRepository;
	private final ProductOptionRepository productOptionRepository;
	private final PreOrderMenuRepository preOrderMenuRepository;
	private final PreOrderMenuItemRepository preOrderMenuItemRepository;
	private final OrderQueryRepository orderQueryRepository;
	private final ServiceChargeRepository serviceChargeRepository;

	private final PaymentCheckHelper paymentCheckHelper;
	private final TenantCheckHelper tenantCheckHelper;
	private final TimeHelper timeHelper;
	private final UserSubAccountRepository userSubAccountRepository;
	private final UserRepository userRepository;

	@Transactional
	public void validatePreOrderInput(TenantDto tenant, TaxDto tax, UserId userId, CreatePreOrderCommand command) {
		PreOrderDetailCommand preOrder = command.getOrder();
		PreOrderPaymentCommand payment = command.getPayment();
		validatePreOrderDetail(tenant, tax, userId, preOrder);
		validatePaymentMethod(payment, preOrder);
	}

	private void validatePreOrderDetail(TenantDto tenant, TaxDto tax, UserId userId, PreOrderDetailCommand preOrder) {
		validateUser(userId, preOrder);
		validateSubOrders(tenant, tax, preOrder);
		validateTenantCurrency(tenant, preOrder);
		validatePreOrderTax(tax, preOrder);
	}

	private void validatePreOrderTax(TaxDto tax, PreOrderDetailCommand preOrder) {
		if (Objects.nonNull(tax) && tax.enabled()) {
			validateTaxConfiguration(tax, preOrder);
		} else {
			validateNoTaxInformation(preOrder);
		}
	}

	private void validateTaxConfiguration(TaxDto tax, PreOrderDetailCommand command) {
		if (Objects.isNull(command.getTaxName()) || Objects.isNull(command.getTaxRate()) || Objects.isNull(command
				.getTaxInclude()) || Objects.isNull(command.getTaxAmount())) {
			throw new InvalidOrderException("Tax information must be fully provided");
		}
		validateTaxValues(tax, command);
	}

	private void validateTaxValues(TaxDto tax, PreOrderDetailCommand command) {
		if (!tax.name()
				.equals(command.getTaxName())) {
			throw new InvalidOrderException("Invalid tax name");
		}
		if (!tax.rate()
				.equals(command.getTaxRate()
						.setScale(4, RoundingMode.UNNECESSARY))) {
			throw new InvalidOrderException("Invalid tax rate");
		}
		if (!tax.includeInPrice()
				.equals(command.getTaxInclude())) {
			throw new InvalidOrderException("Invalid tax include");
		}
	}

	private void validateNoTaxInformation(PreOrderDetailCommand command) {
		if (Objects.nonNull(command.getTaxName()) || Objects.nonNull(command.getTaxRate()) || Objects.nonNull(command
				.getTaxInclude()) || (Objects.nonNull(command.getTaxAmount()) && (!Objects.equals(command
						.getTaxAmount(), BigInteger.ZERO)))) {
			throw new InvalidOrderException("Order are not required to have tax information");
		}
	}

	private void validateUser(UserId userId, PreOrderDetailCommand preOrder) {
		if (userId.equals(new UserId(preOrder.getCustomerId()))) {
			userRepository.findById(userId)
					.orElseThrow(() -> new InvalidOrderException(String.format("User %s not found", userId
							.getValue())));
		} else {
			userSubAccountRepository.findSubAccountByParentUserIdAndSubAccountIds(userId, List.of(new UserId(preOrder
					.getCustomerId())))
					.getContent()
					.stream()
					.findFirst()
					.orElseThrow(() -> new UserAccessDomainForbiddenException(String.format(
							"User %s don't have permission to query access sub account %s", userId.getValue(), preOrder
									.getCustomerId())));
		}
	}

	private void validateSubOrders(TenantDto tenant, TaxDto tax, PreOrderDetailCommand preOrder) {
		for (SubOrderDetailCommand order : preOrder.getOrders()) {
			validatePreOrderItemInfo(tenant, preOrder, order);
			validateServiceCharge(tenant, order);
			validateOrderTax(tax, order);
		}
	}

	private void validatePreOrderItemInfo(TenantDto tenant, PreOrderDetailCommand preOrder,
			SubOrderDetailCommand order) {
		Set<ProductId> productIds = order.getLineItems()
				.stream()
				.map(lineItem -> new ProductId(lineItem.getProductId()))
				.collect(Collectors.toSet());
		Map<ProductId, ProductBriefDto> productBriefs = productRepository.findAllBriefByTenantIdAndIds(new TenantId(
				tenant.getTenantId()), productIds)
				.stream()
				.collect(Collectors.toMap(ProductBriefDto::id, productBriefDto -> productBriefDto));
		Set<PreOrderMenuItemId> preOrderMenuItemIds = order.getLineItems()
				.stream()
				.map(lineItem -> new PreOrderMenuItemId(lineItem.getPreOrderMenuItemId()))
				.collect(Collectors.toSet());
		Map<PreOrderMenuItemId, PreOrderMenuItemLightDto> preOrderMenuItems = preOrderMenuItemRepository
				.findLightDtoAllByIds(new TenantId(tenant.getTenantId()), preOrderMenuItemIds.stream()
						.toList())
				.stream()
				.collect(Collectors.toMap(PreOrderMenuItemLightDto::id, preOrderMenuItemDto -> preOrderMenuItemDto));
		Set<PreOrderMenuId> preOrderMenuIds = preOrderMenuItems.values()
				.stream()
				.map(PreOrderMenuItemLightDto::preOrderMenuId)
				.collect(Collectors.toSet());
		Map<PreOrderMenuId, PreOrderMenuDto> preOrderMenus = preOrderMenuRepository.findAllByIds(new TenantId(tenant
				.getTenantId()), preOrderMenuIds.stream()
						.toList())
				.stream()
				.collect(Collectors.toMap(PreOrderMenuDto::id, preOrderMenuDto -> preOrderMenuDto));
		if (productBriefs.size() != productIds.size()) {
			var productIdsNotFound = productIds.stream()
					.filter(productId -> !productBriefs.containsKey(productId))
					.map(ProductId::getValue)
					.toList();
			throw new InvalidOrderLineItemInfoException(String.format("Product(s) %s not found", StringUtils.join(
					productIdsNotFound, ",")));
		}
		Set<PreOrderMenuItemId> itemNotAvailableForOrder = new HashSet<>();
		Set<PreOrderMenuItemId> cutoffExpiredItems = new HashSet<>();
		Set<PreOrderMenuItemId> insufficientCapacityItems = new HashSet<>();

		order.getLineItems()
				.forEach(lineItem -> {
					validatePreOrderLineItemInfo(lineItem, productBriefs);
					validatePreOrderLineItemAvailable(tenant, lineItem, preOrderMenus, preOrderMenuItems, productBriefs,
							itemNotAvailableForOrder, cutoffExpiredItems);
				});

		Map<PreOrderMenuItemId, List<CreatePreOrderLineItemCommand>> lineItemsGroup = order.getLineItems()
				.stream()
				.collect(Collectors.groupingBy(command -> new PreOrderMenuItemId(command.getPreOrderMenuItemId())));
		lineItemsGroup.entrySet()
				.forEach(entry -> {
					validatePreOrderLineItemStockAvailable(tenant, new UserId(preOrder.getCustomerId()), entry,
							preOrderMenus, preOrderMenuItems, insufficientCapacityItems);
				});
		throwValidationErrorsIfPresent(itemNotAvailableForOrder, cutoffExpiredItems, insufficientCapacityItems);
		validatePreOrderItemOption(preOrder, order, productBriefs);
	}

	private void throwValidationErrorsIfPresent(Set<PreOrderMenuItemId> invalidStatusItems,
			Set<PreOrderMenuItemId> cutoffExpiredItems, Set<PreOrderMenuItemId> insufficientCapacityItems) {
		if (!invalidStatusItems.isEmpty()) {
			throw new PreOrderMenuUnavailableException(String.format("Items %s are not available for order",
					invalidStatusItems.stream()
							.map(PreOrderMenuItemId::getValue)
							.map(String::valueOf)
							.collect(Collectors.joining(","))));
		}
		if (!cutoffExpiredItems.isEmpty()) {
			throw new PreOrderCutOffTimeException(cutoffExpiredItems.stream()
					.toList());
		}
		if (!insufficientCapacityItems.isEmpty()) {
			throw PreOrderItemCapacityInSufficientException.fromPreOrderMenuItemIds(insufficientCapacityItems.stream()
					.toList());
		}
	}

	private void validatePreOrderLineItemStockAvailable(TenantDto tenant, UserId customerId,
			Map.Entry<PreOrderMenuItemId, List<CreatePreOrderLineItemCommand>> groupItem,
			Map<PreOrderMenuId, PreOrderMenuDto> preOrderMenus,
			Map<PreOrderMenuItemId, PreOrderMenuItemLightDto> preOrderMenuItems,
			Set<PreOrderMenuItemId> insufficientCapacityItems) {
		PreOrderMenuItemLightDto preOrderMenuItem = preOrderMenuItems.get(groupItem.getKey());
		PreOrderMenuDto preOrderMenu = preOrderMenus.get(preOrderMenuItem.preOrderMenuId());
		Long totalQuantity = groupItem.getValue()
				.stream()
				.reduce(0L, (acc, item) -> item.getQuantity() + acc, Long::sum);
		if (Objects.nonNull(preOrderMenuItem.capacity()) && preOrderMenuItem.capacity() < preOrderMenuItem.ordered()
				+ totalQuantity.intValue()) {
			insufficientCapacityItems.add(preOrderMenuItem.id());
		}
		if (Objects.isNull(preOrderMenu.maxItemOrderPerDay())) {
			return;
		}
		Long quantityAllowToOrder = Long.valueOf(preOrderMenu.maxItemOrderPerDay()) - orderQueryRepository
				.countItemPlaceByDateAndMealTime(preOrderMenu.tenantId(), customerId, preOrderMenuItem.date(),
						preOrderMenuItem.mealTimeId(), preOrderMenu.id());
		if (quantityAllowToOrder == 0) {
			log.warn("Items quantity exceeded for menu: {} on {}", preOrderMenu.id(), preOrderMenuItem.date());
			throw new PreOrderItemExceededMaxQuantity(String.format("Item %s isn't available for orders on %s",
					preOrderMenu.id()
							.getValue(), preOrderMenuItem.date()));
		} else if (totalQuantity > quantityAllowToOrder) {
			log.warn("Items quantity only allowed for menu: {} on {} is {}", preOrderMenu.id(), preOrderMenuItem.date(),
					quantityAllowToOrder);
			throw new PreOrderItemExceededMaxQuantity(String.format(
					"Items quantity only allowed for menu %s on %s is %d but actually %d", preOrderMenu.id()
							.getValue(), preOrderMenuItem.date(), Math.max(quantityAllowToOrder, 0), totalQuantity));
		}
	}

	private void validatePreOrderLineItemAvailable(TenantDto tenant, CreatePreOrderLineItemCommand lineItem,
			Map<PreOrderMenuId, PreOrderMenuDto> preOrderMenus,
			Map<PreOrderMenuItemId, PreOrderMenuItemLightDto> preOrderMenuItems,
			Map<ProductId, ProductBriefDto> productBriefs, Set<PreOrderMenuItemId> itemNotAvailableForOrder,
			Set<PreOrderMenuItemId> cutoffExpiredItems) {
		ProductBriefDto productBriefDto = productBriefs.get(new ProductId(lineItem.getProductId()));
		PreOrderMenuItemLightDto preOrderMenuItem = preOrderMenuItems.get(new PreOrderMenuItemId(lineItem
				.getPreOrderMenuItemId()));
		PreOrderMenuDto preOrderMenu = preOrderMenus.get(preOrderMenuItem.preOrderMenuId());
		if (!PreOrderMenuStatus.ACTIVE.equals(preOrderMenu.status()) || !PreOrderMenuItemStatus.ACTIVE.equals(
				preOrderMenuItem.status()) || !ProductStatus.ACTIVE.equals(productBriefDto.status())) {
			itemNotAvailableForOrder.add(new PreOrderMenuItemId(lineItem.getPreOrderMenuItemId()));
		}
		ZoneId zoneId = TenantHelper.getZoneId(tenant);
		ZonedDateTime validDate = ZonedDateTime.of(preOrderMenuItem.date()
				.minusDays(Optional.ofNullable(preOrderMenu.cutOffDays())
						.orElse(0)), preOrderMenu.cutOffTime(), zoneId);
		ZonedDateTime currentTime = timeHelper.getCurrentTime()
				.atZone(zoneId);
		if (currentTime.isAfter(validDate)) {
			cutoffExpiredItems.add(preOrderMenuItem.id());
		}

	}

	private void validatePreOrderLineItemInfo(CreatePreOrderLineItemCommand lineItem,
			Map<ProductId, ProductBriefDto> productBriefs) {
		if (!preOrderMenuItemRepository.existsByIdAndProductId(new PreOrderMenuItemId(lineItem.getPreOrderMenuItemId()),
				new ProductId(lineItem.getProductId()))) {
			log.warn("PreOrderMenuItem {} not found", lineItem.getProductName());
			throw new InvalidOrderLineItemInfoException(String.format("PreOrderMenuItem %s not found", lineItem
					.getProductName()));
		}
		ProductBriefDto productBriefDto = productBriefs.get(new ProductId(lineItem.getProductId()));
		if (!lineItem.getProductName()
				.trim()
				.equalsIgnoreCase(productBriefDto.name())) {
			log.warn("Product {} is not correct", lineItem.getProductName());
			throw new InvalidOrderLineItemInfoException(String.format("Product %s is not correct", lineItem
					.getProductName()));
		}
		if (!productBriefDto.openPrice() && !new Money(lineItem.getUnitPrice()).compareWithAcceptedTolerance(
				productBriefDto.unitPrice())) {
			log.warn("Product {} has incorrect unit price", lineItem.getProductName());
			throw new InvalidOrderLineItemInfoException(String.format("Product %s has incorrect unit price", lineItem
					.getProductName()));
		}
	}

	private void validatePreOrderItemOption(PreOrderDetailCommand preOrder, SubOrderDetailCommand order,
			Map<ProductId, ProductBriefDto> productBriefs) {
		Set<ProductId> productIds = order.getLineItems()
				.stream()
				.map(lineItem -> new ProductId(lineItem.getProductId()))
				.collect(Collectors.toSet());
		Map<ProductId, List<ProductOption>> productOptions = productOptionRepository.findAllByProductIds(productIds
				.stream()
				.toList())
				.stream()
				.collect(Collectors.groupingBy(ProductOption::getProductId));
		order.getLineItems()
				.forEach(lineItem -> {
					if (Objects.nonNull(lineItem.getOptions()) && Boolean.FALSE.equals(lineItem.getOptions()
							.isEmpty())) {
						ProductBriefDto productBriefDto = productBriefs.get(new ProductId(lineItem.getProductId()));
						var options = Optional.ofNullable(productOptions.get(new ProductId(lineItem.getProductId())))
								.orElse(List.of())
								.stream()
								.collect(Collectors.toMap(ProductOption::getId, productOptionDto -> productOptionDto));
						lineItem.getOptions()
								.forEach(option -> {
									boolean existOption = options.containsKey(new ProductOptionId(option
											.getOptionId()));
									if (!existOption) {
										log.info("Option {} is not available for Product {}", option.getOptionId(),
												lineItem.getProductId());
										throw new InvalidOrderLineItemInfoException(String.format(
												"Option %s is not available for Product %s", option.getOptionId(),
												lineItem.getProductId()));
									}
									ProductOption productOption = options.get(new ProductOptionId(option
											.getOptionId()));
									if (Objects.nonNull(productOption.getMinimum()) && option.getItems()
											.size() < productOption.getMinimum()) {
										log.info("Option {} for Product {} has minimum quantity of {}", option
												.getOptionId(), lineItem.getProductId(), productOption.getMinimum());
										throw new InvalidOrderLineItemInfoException(String.format(
												"Option %s for Product %s has minimum quantity of %d", option
														.getOptionId(), lineItem.getProductId(), productOption
																.getMinimum()));
									}
									if (Objects.nonNull(productOption.getMaximum()) && option.getItems()
											.size() > productOption.getMaximum()) {
										log.info("Option {} for Product {} has maximum quantity of {}", option
												.getOptionId(), lineItem.getProductId(), productOption.getMaximum());
										throw new InvalidOrderLineItemInfoException(String.format(
												"Option %s for Product %s has maximum quantity of %d", option
														.getOptionId(), lineItem.getProductId(), productOption
																.getMaximum()));
									}
									Map<ProductOptionItemId, ProductOptionItem> productOptionItems = productOption
											.getItems()
											.stream()
											.collect(Collectors.toMap(ProductOptionItem::getId,
													productOptionItemDto -> productOptionItemDto));
									boolean isInvalidData = option.getItems()
											.stream()
											.anyMatch(itemOption -> {
												boolean existOptionItem = productOptionItems.containsKey(
														new ProductOptionItemId(itemOption.getOptionItemId()));
												if (!existOptionItem) {
													return true;
												}
												ProductOptionItem productOptionItem = productOptionItems.get(
														new ProductOptionItemId(itemOption.getOptionItemId()));
												if (!productOptionItem.isActive()) {
													return true;
												}
												return !productOptionItem.getName()
														.equalsIgnoreCase(itemOption.getName()) || (!productBriefDto
																.openPrice() && !productOptionItem.getAdditionPrice()
																		.compareWithAcceptedTolerance(new Money(
																				itemOption.getAdditionalPrice())));
											});
									if (isInvalidData) {
										log.info("Option {} for Product {} has invalid option items", option
												.getOptionId(), lineItem.getProductId());
										throw new InvalidOrderLineItemInfoException(String.format(
												"Option %s for Product %s has invalid option items", option
														.getOptionId(), lineItem.getProductId()));
									}
								});
					}
				});
	}

	private void validatePaymentMethod(PreOrderPaymentCommand payment, PreOrderDetailCommand preOrder) {
		if (Objects.isNull(Optional.ofNullable(payment)
				.map(PreOrderPaymentCommand::getPaymentMethodId)
				.orElse(null))) {
			if (!preOrder.getTotalAmount()
					.equals(BigInteger.ZERO)) {
				throw new OrderPaymentMethodRequiredException("Payment method is required");
			}
			return;
		}
		PaymentMethodDto paymentMethod = Optional.of(payment)
				.map(PreOrderPaymentCommand::getPaymentMethodId)
				.map(PaymentMethodId::new)
				.map(paymentCheckHelper::paymentMethodCheck)
				.orElseThrow(() -> new InvalidOrderPaymentException("Payment method not found"));
		if (!paymentMethod.isActive()) {
			throw new InvalidOrderPaymentException("Payment method is not active");
		}
	}

	private void validateServiceCharge(TenantDto tenant, SubOrderDetailCommand preOrder) {
		List<OrderServiceChargeCommand> commands = Optional.ofNullable(preOrder.getServiceCharges())
				.orElse(Collections.emptyList());
		List<ServiceCharge> serviceCharges = serviceChargeRepository.findAllByPlatform(new TenantId(tenant
				.getTenantId()), PlatformOrderType.PRE_ORDER, true);
		if (serviceCharges.size() != commands.size()) {
			throw new InvalidOrderException("Service charge information is not valid");
		}
		if (commands.isEmpty()) {
			return;
		}
		Map<ServiceChargeId, ServiceCharge> serviceChargeMap = serviceCharges.stream()
				.collect(Collectors.toMap(ServiceCharge::getId, serviceCharge -> serviceCharge));
		for (OrderServiceChargeCommand command : commands) {
			ServiceCharge serviceCharge = serviceChargeMap.get(new ServiceChargeId(command.serviceChargeId()));
			if (Objects.isNull(serviceCharge)) {
				throw new InvalidOrderException("Service charge information is not valid");
			}
			if (!serviceCharge.getName()
					.equals(command.name())) {
				throw new InvalidOrderException("Service charge name is not valid");
			}
		}
	}

	private void validateOrderTax(TaxDto tax, SubOrderDetailCommand order) {
		if (Objects.nonNull(tax) && tax.enabled()) {
			validateTaxConfiguration(tax, order);
		} else {
			validateNoTaxInformation(order);
		}
	}

	private void validateTaxConfiguration(TaxDto tax, SubOrderDetailCommand command) {
		if (Objects.isNull(command.getTaxName()) || Objects.isNull(command.getTaxRate()) || Objects.isNull(command
				.getTaxInclude()) || Objects.isNull(command.getTaxAmount())) {
			throw new InvalidOrderException("Tax information must be fully provided");
		}
		validateTaxValues(tax, command);
	}

	private void validateTaxValues(TaxDto tax, SubOrderDetailCommand command) {
		if (!tax.name()
				.equals(command.getTaxName())) {
			throw new InvalidOrderException("Invalid tax name");
		}
		if (!tax.rate()
				.equals(command.getTaxRate()
						.setScale(4, RoundingMode.UNNECESSARY))) {
			throw new InvalidOrderException("Invalid tax rate");
		}
		if (!tax.includeInPrice()
				.equals(command.getTaxInclude())) {
			throw new InvalidOrderException("Invalid tax include");
		}
	}

	private void validateNoTaxInformation(SubOrderDetailCommand command) {
		if (Objects.nonNull(command.getTaxName()) || Objects.nonNull(command.getTaxRate()) || Objects.nonNull(command
				.getTaxInclude()) || (Objects.nonNull(command.getTaxAmount()) && (!Objects.equals(command
						.getTaxAmount(), BigInteger.ZERO)))) {
			throw new InvalidOrderException("Order are not required to have tax information");
		}
	}

	private void validateTenantCurrency(TenantDto tenant, PreOrderDetailCommand command) {
		Optional.ofNullable(tenant.getSettings())
				.map(TenantSettingDto::getCurrency)
				.map(TenantSettingDto.CurrencyDto::getCurrencyCode)
				.filter(currencyCode -> currencyCode.equals(command.getCurrencyCode()))
				.orElseThrow(() -> new InvalidOrderException("Currency code is not valid"));
	}
}
