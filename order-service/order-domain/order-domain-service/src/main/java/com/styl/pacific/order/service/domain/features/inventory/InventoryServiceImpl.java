/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.inventory;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.common.exception.OrderDomainException;
import com.styl.pacific.order.service.domain.features.inventory.dto.command.UpdateInventoryCommand;
import com.styl.pacific.order.service.domain.features.inventory.dto.query.PaginationInventoryQuery;
import com.styl.pacific.order.service.domain.features.inventory.entity.Inventory;
import com.styl.pacific.order.service.domain.features.inventory.mapper.InventoryDataMapper;
import com.styl.pacific.order.service.domain.features.inventory.ports.input.service.InventoryService;
import com.styl.pacific.order.service.domain.features.inventory.ports.output.repository.InventoryRepository;
import com.styl.pacific.order.service.domain.features.product.entity.Product;
import com.styl.pacific.order.service.domain.features.product.ports.output.repository.ProductRepository;
import com.styl.pacific.order.service.domain.features.tenant.dto.TenantDto;
import com.styl.pacific.order.service.domain.features.tenant.output.repository.TenantRepository;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
@RequiredArgsConstructor
public class InventoryServiceImpl implements InventoryService {
	private final InventoryRepository inventoryRepository;
	private final ProductRepository productRepository;
	private final TenantRepository tenantRepository;

	@Override
	@Transactional(readOnly = true)
	public Paging<Inventory> findAllPaging(TenantId tenantId, PaginationInventoryQuery query) {
		return inventoryRepository.findAllPaging(tenantId, query);
	}

	@Override
	@Transactional(readOnly = true)
	public Inventory findById(TenantId tenantId, ProductId id) {
		Optional<Inventory> inventoryOptional = inventoryRepository.findById(tenantId, id);
		return inventoryOptional.orElseGet(() -> Inventory.defaultValue(tenantId, id));
	}

	@Override
	@Transactional
	public Inventory update(TenantId tenantId, ProductId id, UpdateInventoryCommand command) {
		// Check valid command
		updateInventoryCheck(command);

		TenantDto tenant = tenantCheck(tenantId);
		Product product = productCheck(tenantId, id);
		Optional<Inventory> inventoryOptional = inventoryRepository.findById(tenantId, id);
		Inventory result;
		if (inventoryOptional.isPresent()) {
			Inventory inventory = inventoryOptional.get();
			InventoryDataMapper.INSTANCE.updateModel(inventory, command);
			result = inventoryRepository.update(inventory);
		} else {
			Inventory inventory = InventoryDataMapper.INSTANCE.toModel(tenantId, product.getId(), command);
			result = inventoryRepository.save(inventory);
		}
		return result;
	}

	private void updateInventoryCheck(UpdateInventoryCommand command) {
		if (command.tracking()) {
			if (Objects.isNull(command.quantity())) {
				throw new OrderDomainException("quantity must not be null");
			} else if (command.quantity() < 0) {
				throw new OrderDomainException("quantity must be greater than or equal to 0");
			}
		}
		if (Objects.nonNull(command.maximumQuantityOrder())) {
			if (command.maximumQuantityOrder() < 1) {
				throw new OrderDomainException("maximumQuantityOrder must be greater than or equal to 1");
			} else if (command.maximumQuantityOrder() < command.minimumQuantityOrder()) {
				throw new OrderDomainException(
						"maximumQuantityOrder must be greater than or equal to minimumQuantityOrder");
			}

		}
	}

	private Product productCheck(TenantId tenantId, ProductId productId) {
		return productRepository.findById(tenantId, productId)
				.orElseThrow(() -> {
					log.warn("Product {} not found", productId);
					return new OrderDomainException(String.format("Product %s not found", productId.getValue()));
				});
	}

	private TenantDto tenantCheck(TenantId tenantId) {
		return tenantRepository.findById(tenantId.getValue())
				.orElseThrow(() -> {
					log.warn("Tenant {} not found", tenantId);
					return new OrderDomainException(String.format("Tenant %s not found", tenantId.getValue()));
				});
	}
}
