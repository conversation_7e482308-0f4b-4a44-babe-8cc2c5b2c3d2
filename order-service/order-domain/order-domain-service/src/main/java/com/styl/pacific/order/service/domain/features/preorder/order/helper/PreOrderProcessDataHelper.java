/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.helper;

import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.domain.enums.PlatformOrderType;
import com.styl.pacific.domain.enums.StoreStatus;
import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.domain.enums.order.OrderStatus;
import com.styl.pacific.domain.enums.order.OrderType;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.OrderLineItemId;
import com.styl.pacific.domain.valueobject.PaymentMethodId;
import com.styl.pacific.domain.valueobject.PreOrderMenuId;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.order.service.domain.features.mealtime.valueobjects.MealTimeId;
import com.styl.pacific.order.service.domain.features.order.OrderWithDiscountDomainCore;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.OrderServiceChargeCommand;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLineItem;
import com.styl.pacific.order.service.domain.features.order.entity.OrderServiceCharge;
import com.styl.pacific.order.service.domain.features.order.exception.InvalidOrderException;
import com.styl.pacific.order.service.domain.features.order.exception.InvalidOrderLineItemAmountException;
import com.styl.pacific.order.service.domain.features.order.exception.UserAccessDomainForbiddenException;
import com.styl.pacific.order.service.domain.features.order.processor.OrderAmountProcessor;
import com.styl.pacific.order.service.domain.features.order.processor.OrderPaymentProcessor;
import com.styl.pacific.order.service.domain.features.order.valueobjects.OrderIdGenerator;
import com.styl.pacific.order.service.domain.features.order.valueobjects.OrderLineItemIdGenerator;
import com.styl.pacific.order.service.domain.features.order.valueobjects.ProductMetadata;
import com.styl.pacific.order.service.domain.features.payment.dto.PaymentMethodDto;
import com.styl.pacific.order.service.domain.features.payment.helpers.PaymentCheckHelper;
import com.styl.pacific.order.service.domain.features.preorder.PreOrderDomainCore;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.ports.output.repository.PreOrderMenuItemRepository;
import com.styl.pacific.order.service.domain.features.preorder.menu.ports.output.repository.PreOrderMenuRepository;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemId;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.PreOrderConfig;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.CreatePreOrderCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.CreatePreOrderLineItemCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.PreOrderDetailCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.PreOrderLineItemOptionCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.PreOrderLineItemOptionItemCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.PreOrderPaymentCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.SubOrderDetailCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.entity.PreOrder;
import com.styl.pacific.order.service.domain.features.preorder.order.enums.PreOrderStatus;
import com.styl.pacific.order.service.domain.features.preorder.order.exception.InvalidPreOrderAmountException;
import com.styl.pacific.order.service.domain.features.preorder.order.exception.InvalidPreOrderException;
import com.styl.pacific.order.service.domain.features.preorder.order.id.generator.PreOrderIdGenerator;
import com.styl.pacific.order.service.domain.features.preorder.order.ports.output.repository.PreOrderConfigureRepository;
import com.styl.pacific.order.service.domain.features.preorder.order.processor.PreOrderNumberProcessor;
import com.styl.pacific.order.service.domain.features.product.mapper.ProductDataMapper;
import com.styl.pacific.order.service.domain.features.servicecharge.entity.ServiceCharge;
import com.styl.pacific.order.service.domain.features.servicecharge.ports.output.repository.ServiceChargeRepository;
import com.styl.pacific.order.service.domain.features.servicecharge.valueobjects.ServiceChargeId;
import com.styl.pacific.order.service.domain.features.store.dto.StoreDto;
import com.styl.pacific.order.service.domain.features.store.helpers.StoreCheckHelper;
import com.styl.pacific.order.service.domain.features.tenant.dto.TaxDto;
import com.styl.pacific.order.service.domain.features.tenant.dto.TenantDto;
import com.styl.pacific.order.service.domain.features.user.dto.UserDto;
import com.styl.pacific.order.service.domain.features.user.port.output.repository.UserRepository;
import com.styl.pacific.order.service.domain.features.user.port.output.repository.UserSubAccountRepository;
import com.styl.pacific.order.service.domain.utils.TenantHelper;
import com.styl.pacific.order.service.domain.utils.UserNameHelper;
import com.styl.pacific.utils.string.StringRandomUtils;
import java.math.BigInteger;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Currency;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@Component
@RequiredArgsConstructor
public class PreOrderProcessDataHelper {
	private static final Logger log = LoggerFactory.getLogger(PreOrderProcessDataHelper.class);
	private final StoreCheckHelper storeCheckHelper;
	private final PaymentCheckHelper paymentCheckHelper;

	private final UserSubAccountRepository userSubAccountRepository;
	private final UserRepository userRepository;
	private final PreOrderMenuRepository preOrderMenuRepository;
	private final PreOrderMenuItemRepository preOrderMenuItemRepository;
	private final PreOrderConfigureRepository orderConfigureRepository;
	private final ServiceChargeRepository serviceChargeRepository;

	private final PreOrderIdGenerator preOrderIdGenerator;
	private final OrderIdGenerator orderIdGenerator;
	private final OrderLineItemIdGenerator orderLineItemIdGenerator;

	private final OrderPaymentProcessor orderPaymentProcessor;
	private final OrderAmountProcessor orderAmountProcessor;
	private final PreOrderNumberProcessor preOrderNumberProcessor;

	private final OrderWithDiscountDomainCore orderDomainCore;
	private final PreOrderDomainCore preOrderDomainCore;
	private static final int PRE_ORDER_NUMBER_PREFIX_LENGTH = 6;

	@Transactional
	public PreOrder processPreOrder(TenantDto tenant, TaxDto tax, TokenClaim tokenClaim,
			CreatePreOrderCommand command) {
		Map<Integer, OrderLineItemId> trackingItem = new HashMap<>();
		PreOrder preOrder = initializePreOrder(tenant, tax, Optional.of(tokenClaim)
				.map(TokenClaim::getUserId)
				.filter(NumberUtils::isCreatable)
				.map(NumberUtils::createLong)
				.map(UserId::new)
				.orElse(null), command, trackingItem);
		validateInputPreOrder(command, preOrder, trackingItem);
		return preOrder;
	}

	private void validateInputPreOrder(CreatePreOrderCommand command, PreOrder preOrder,
			Map<Integer, OrderLineItemId> trackingItem) {
		List<Order> orders = preOrder.getSubOrders();
		Map<OrderLineItemId, OrderLineItem> lineItems = orders.stream()
				.flatMap(order -> order.getLineItems()
						.stream())
				.collect(Collectors.toMap(OrderLineItem::getId, Function.identity()));
		for (CreatePreOrderLineItemCommand item : Optional.ofNullable(command)
				.map(CreatePreOrderCommand::getOrder)
				.map(PreOrderDetailCommand::getOrders)
				.map(subOrders -> subOrders.stream()
						.map(SubOrderDetailCommand::getLineItems)
						.flatMap(List::stream)
						.toList())
				.orElse(Collections.emptyList())) {
			OrderLineItemId lineItemId = trackingItem.get(item.hashCode());
			if (Objects.isNull(lineItemId)) {
				continue;
			}
			OrderLineItem lineItem = lineItems.get(lineItemId);
			if (Objects.isNull(lineItem)) {
				continue;
			}
			Money inputTotalDiscount = Optional.of(item)
					.map(CreatePreOrderLineItemCommand::getTotalDiscount)
					.map(Money::new)
					.orElse(new Money(BigInteger.ZERO));
			Money inputTotalAmount = Optional.of(item)
					.map(CreatePreOrderLineItemCommand::getTotalAmount)
					.map(Money::new)
					.orElse(new Money(BigInteger.ZERO));

			if (!lineItem.getTotalDiscount()
					.compareWithAcceptedTolerance(inputTotalDiscount)) {
				throw new InvalidOrderLineItemAmountException(String.format(
						"Invalid order line item total discount amount expect %s but found %s", lineItem
								.getTotalDiscount()
								.getAmount(), inputTotalDiscount.getAmount()));
			}
			if (!lineItem.getTotalAmount()
					.compareWithAcceptedTolerance(inputTotalAmount)) {
				throw new InvalidOrderLineItemAmountException(String.format(
						"Invalid order line item total amount expect %s but found %s", lineItem.getTotalAmount()
								.getAmount(), inputTotalAmount.getAmount()));
			}
		}
		PreOrderDetailCommand inputOrder = Optional.ofNullable(command)
				.map(CreatePreOrderCommand::getOrder)
				.orElse(null);
		Money inputTaxAmount = Optional.ofNullable(inputOrder)
				.map(PreOrderDetailCommand::getTaxAmount)
				.map(Money::new)
				.orElse(new Money(BigInteger.ZERO));
		Money inputSubtotalAmount = Optional.ofNullable(inputOrder)
				.map(PreOrderDetailCommand::getSubtotalAmount)
				.map(Money::new)
				.orElse(new Money(BigInteger.ZERO));
		Money inputServiceChargeAmount = Optional.ofNullable(inputOrder)
				.map(PreOrderDetailCommand::getServiceChargeAmount)
				.map(Money::new)
				.orElse(new Money(BigInteger.ZERO));
		Money inputDiscountAmount = Optional.ofNullable(inputOrder)
				.map(PreOrderDetailCommand::getDiscountAmount)
				.map(Money::new)
				.orElse(new Money(BigInteger.ZERO));
		Money inputTotalAmount = Optional.ofNullable(inputOrder)
				.map(PreOrderDetailCommand::getTotalAmount)
				.map(Money::new)
				.orElse(new Money(BigInteger.ZERO));

		if (!preOrder.getSubtotalAmount()
				.compareWithAcceptedTolerance(inputSubtotalAmount)) {
			throw new InvalidPreOrderAmountException(String.format(
					"Invalid order subtotal amount expect %s but found %s", preOrder.getSubtotalAmount()
							.getAmount(), inputSubtotalAmount.getAmount()));
		}
		if (!preOrder.getServiceChargeAmount()
				.compareWithAcceptedTolerance(inputServiceChargeAmount)) {
			throw new InvalidPreOrderAmountException(String.format(
					"Invalid order service charge amount expect %s but found %s", preOrder.getServiceChargeAmount()
							.getAmount(), inputServiceChargeAmount.getAmount()));
		}
		if (!preOrder.getDiscountAmount()
				.compareWithAcceptedTolerance(inputDiscountAmount)) {
			throw new InvalidPreOrderAmountException(String.format(
					"Invalid order discount amount expect %s but found %s", preOrder.getDiscountAmount()
							.getAmount(), inputDiscountAmount.getAmount()));
		}
		if (!preOrder.getTaxAmount()
				.compareWithAcceptedTolerance(inputTaxAmount)) {
			throw new InvalidPreOrderAmountException(String.format("Invalid order tax amount expect %s but found %s",
					preOrder.getTaxAmount()
							.getAmount(), inputTaxAmount.getAmount()));
		}
		if (!preOrder.getTotalAmount()
				.compareWithAcceptedTolerance(inputTotalAmount)) {
			throw new InvalidPreOrderAmountException(String.format("Invalid order total amount expect %s but found %s",
					preOrder.getTotalAmount()
							.getAmount(), inputTotalAmount.getAmount()));
		}

		for (Order order : orders) {
			orderDomainCore.validateOrder(order);
		}
		preOrderDomainCore.validatePreOrder(preOrder);
	}

	private PreOrder initializePreOrder(TenantDto tenant, TaxDto tax, UserId userId, CreatePreOrderCommand command,
			Map<Integer, OrderLineItemId> trackingItem) {
		PreOrderDetailCommand preOrderCommand = command.getOrder();
		PreOrderPaymentCommand payment = command.getPayment();
		UserDto userInfo = getUserInfo(userId, new UserId(preOrderCommand.getCustomerId()));
		UUID nonce = UUID.randomUUID();
		PaymentMethodDto paymentMethod = Optional.ofNullable(payment)
				.map(PreOrderPaymentCommand::getPaymentMethodId)
				.map(PaymentMethodId::new)
				.map(paymentCheckHelper::paymentMethodCheck)
				.orElse(null);

		// Map data
		PreOrder preOrder = PreOrder.builder()
				.id(preOrderIdGenerator.nextId())
				.tenantId(new TenantId(tenant.getTenantId()))
				.systemSource(preOrderCommand.getSystemSource())
				.issuerId(userId)
				.customerId(userInfo.id())
				.customerName(UserNameHelper.formatFullName(userInfo.firstName(), userInfo.lastName()))
				.customerEmail(userInfo.email())
				.taxName(preOrderCommand.getTaxName())
				.taxRegNo(Optional.ofNullable(tax)
						.map(TaxDto::taxRegNo)
						.orElse(null))
				.businessName(Optional.ofNullable(tax)
						.map(TaxDto::businessName)
						.orElse(null))
				.taxRate(preOrderCommand.getTaxRate())
				.taxInclude(preOrderCommand.getTaxInclude())
				.taxAmount(Optional.ofNullable(preOrderCommand.getTaxAmount())
						.map(Money::new)
						.orElse(Money.ZERO))
				.discountAmount(new Money(preOrderCommand.getDiscountAmount()))
				.subtotalAmount(Optional.ofNullable(preOrderCommand.getSubtotalAmount())
						.map(Money::new)
						.orElse(null))
				.serviceChargeAmount(Optional.ofNullable(preOrderCommand.getServiceChargeAmount())
						.map(Money::new)
						.orElse(null))
				.totalAmount(Optional.ofNullable(preOrderCommand.getTotalAmount())
						.map(Money::new)
						.orElse(null))
				.currencyCode(preOrderCommand.getCurrencyCode())
				.paymentMethodId(Optional.ofNullable(paymentMethod)
						.map(PaymentMethodDto::id)
						.orElse(null))
				.paymentMethodName(Optional.ofNullable(paymentMethod)
						.map(PaymentMethodDto::displayName)
						.orElse(null))
				.build();
		// Process data
		preOrder.initialize();
		preOrder.setNonce(nonce);
		preOrder.setPaymentStatus(OrderPaymentStatus.PENDING);
		preOrder.setPaymentRef(orderPaymentProcessor.generatePaymentReference(preOrder.getId()
				.getValue(), OrderType.PRE_ORDER, preOrder.getCurrencyCode(), preOrder.getTotalAmount(), nonce));
		if (Objects.nonNull(paymentMethod)) {
			preOrder.setPaymentMethodName(paymentMethod.displayName());
			preOrder.setRefundable(paymentMethod.isTransactionReversible());
		} else {
			preOrder.setRefundable(false);
		}
		setUpPreOrderExpireOption(preOrder);
		String orderNumberPrefix = StringRandomUtils.generateRandomString(PRE_ORDER_NUMBER_PREFIX_LENGTH)
				.toUpperCase();
		preOrder.setPreOrderNumber(orderNumberPrefix);
		preOrder.setSubOrders(initializeOrders(tenant, userInfo, preOrder, preOrderCommand.getOrders(), trackingItem,
				orderNumberPrefix));
		processStatusPreOrder(preOrder);
		return preOrder;
	}

	private void processStatusPreOrder(PreOrder order) {
		if (order.getTotalAmount()
				.equals(Money.ZERO)) {
			order.setStatus(PreOrderStatus.PAID);
			order.setPaymentStatus(OrderPaymentStatus.PAID);
		}
	}

	private UserDto getUserInfo(UserId userId, UserId customerId) {
		UserDto userInfo;
		if (userId.equals(customerId)) {
			userInfo = userRepository.findById(userId)
					.orElseThrow(() -> new InvalidOrderException(String.format("User %s not found", userId
							.getValue())));
		} else {
			userInfo = userSubAccountRepository.findSubAccountByParentUserIdAndSubAccountIds(userId, List.of(
					customerId))
					.getContent()
					.stream()
					.findFirst()
					.orElseThrow(() -> new UserAccessDomainForbiddenException(String.format(
							"User %s don't have permission to query access sub account %s", userId.getValue(),
							customerId.getValue())))
					.subUser();
		}
		return userInfo;
	}

	private List<Order> initializeOrders(TenantDto tenant, UserDto user, PreOrder preOrder,
			List<SubOrderDetailCommand> orders, Map<Integer, OrderLineItemId> trackingItem, String orderNumberPrefix) {
		List<Order> result = new ArrayList<>();
		Map<PreOrderMenuItemId, PreOrderMenuItemDto> menuItems = new HashMap<>();
		Map<ServiceChargeId, ServiceCharge> serviceCharges = serviceChargeRepository.findAllByPlatform(new TenantId(
				tenant.getTenantId()), PlatformOrderType.PRE_ORDER, true)
				.stream()
				.collect(Collectors.toMap(ServiceCharge::getId, Function.identity()));

		AtomicInteger orderCounter = new AtomicInteger();
		for (SubOrderDetailCommand order : orders) {
			Set<PreOrderMenuItemId> menuItemIds = order.getLineItems()
					.stream()
					.map(CreatePreOrderLineItemCommand::getPreOrderMenuItemId)
					.map(PreOrderMenuItemId::new)
					.collect(Collectors.toSet());

			List<PreOrderMenuItemId> idNotExists = menuItemIds.stream()
					.filter(id -> !menuItems.containsKey(id))
					.toList();

			Map<PreOrderMenuItemId, PreOrderMenuItemDto> menuItemNotExists = preOrderMenuItemRepository.findDtoAllByIds(
					new TenantId(tenant.getTenantId()), idNotExists.stream()
							.toList())
					.stream()
					.collect(Collectors.toMap(PreOrderMenuItemDto::id, Function.identity()));

			menuItems.putAll(menuItemNotExists);

			List<PreOrderMenuItemDto> menuItemExists = menuItemIds.stream()
					.filter(menuItems::containsKey)
					.map(menuItems::get)
					.toList();

			if (menuItemIds.size() != menuItemExists.size()) {
				log.warn("Pre-order invalid item in sub order");
				throw new InvalidPreOrderException("Pre-order invalid item in sub order");
			}

			PreOrderMenuItemDto firstItem = menuItemExists.stream()
					.findFirst()
					.orElse(null);
			if (Objects.isNull(firstItem)) {
				log.warn("Pre-order invalid item in sub order, item not found");
				throw new InvalidPreOrderException("Pre-order invalid item in sub order, item not found");
			}

			PreOrderMenuId preOrderMenuId = firstItem.preOrderMenuId();
			MealTimeId mealTimeId = firstItem.mealTimeId();
			LocalDate date = firstItem.date();

			menuItemExists.forEach(preOrderMenuItemDto -> {
				if (Boolean.FALSE.equals(preOrderMenuItemDto.preOrderMenuId()
						.equals(preOrderMenuId))) {
					log.warn("Pre-order invalid item in sub order, menu not match");
					throw new InvalidPreOrderException("Pre-order invalid item in sub order, menu not match");
				}
				if (Boolean.FALSE.equals(preOrderMenuItemDto.mealTimeId()
						.equals(mealTimeId))) {
					log.warn("Pre-order invalid item in sub order, meal time not match");
					throw new InvalidPreOrderException("Pre-order invalid item in sub order, meal time not match");
				}
				if (Boolean.FALSE.equals(preOrderMenuItemDto.date()
						.equals(date))) {
					log.warn("Pre-order invalid item in sub order, date not match");
					throw new InvalidPreOrderException("Pre-order invalid item in sub order, date not match");
				}

			});

			PreOrderMenuDto menu = preOrderMenuRepository.findDtoById(new TenantId(tenant.getTenantId()),
					preOrderMenuId)
					.orElseThrow(() -> new InvalidPreOrderException("Menu not found"));

			if (Boolean.FALSE.equals(order.getStoreId()
					.equals(menu.storeId()
							.getValue()))) {
				log.warn("Pre-order invalid item in sub order, store not match");
				throw new InvalidPreOrderException("Pre-order invalid item in sub order, store not match");
			}
			if (Boolean.FALSE.equals(order.getPreOrderMenuType()
					.equals(menu.type()))) {
				log.warn("Pre-order invalid item in sub order, menu type not match");
				throw new InvalidPreOrderException("Pre-order invalid item in sub order, menu type not match");
			}
			if (Boolean.FALSE.equals(order.getMealTimeId()
					.equals(mealTimeId.getValue()))) {
				log.warn("Pre-order invalid item in sub order, meal time not match");
				throw new InvalidPreOrderException("Pre-order invalid item in sub order, meal time not match");
			}
			if (Boolean.FALSE.equals(order.getCollectionDate()
					.equals(date))) {
				log.warn("Pre-order invalid item in sub order, date not match");
				throw new InvalidPreOrderException("Pre-order invalid item in sub order, date not match");
			}
			String orderNumber = preOrderNumberProcessor.generateOrderNumber(orderNumberPrefix, orderCounter
					.incrementAndGet());
			Order initializedOrder = initializeOrder(tenant, user, preOrder, order, menu, date, mealTimeId, menuItems,
					serviceCharges, trackingItem, orderNumber);
			result.add(initializedOrder);
		}
		return result;
	}

	private Order initializeOrder(TenantDto tenant, UserDto user, PreOrder preOrder, SubOrderDetailCommand orderCommand,
			PreOrderMenuDto menu, LocalDate date, MealTimeId mealTimeId,
			Map<PreOrderMenuItemId, PreOrderMenuItemDto> dataSetItem,
			Map<ServiceChargeId, ServiceCharge> dataSetServiceCharge, Map<Integer, OrderLineItemId> trackingItem,
			String orderNumber) {
		ZoneId tenantZoneId = TenantHelper.getZoneId(tenant);
		StoreDto store = Optional.of(menu)
				.map(PreOrderMenuDto::storeId)
				.map(storeCheckHelper::storeCheck)
				.orElseThrow(() -> new InvalidOrderException("Store not found"));
		if (!store.status()
				.equals(StoreStatus.ACTIVE)) {
			throw new InvalidOrderException(String.format("Store %s is inactive", store.storeId()
					.getValue()));
		}

		Order order = Order.builder()
				.id(orderIdGenerator.nextId())
				.orderNumber(orderNumber)
				.tenantId(preOrder.getTenantId())
				.systemSource(preOrder.getSystemSource())
				.eventVersion(0L)
				.storeId(Optional.ofNullable(orderCommand.getStoreId())
						.map(StoreId::new)
						.orElse(null))
				.preOrderId(preOrder.getId())
				.preOrderGroupId(user.userGroup()
						.getId())
				.preOrderMenuType(orderCommand.getPreOrderMenuType())
				.mealTimeId(mealTimeId)
				.collectionDate(date)
				.collectionDateTime(ZonedDateTime.of(date, LocalTime.MIN, tenantZoneId)
						.toInstant())
				.issuerId(preOrder.getIssuerId())
				.customerId(preOrder.getCustomerId())
				.customerName(preOrder.getCustomerName())
				.customerEmail(preOrder.getCustomerEmail())
				.type(OrderType.PRE_ORDER)
				.refundable(preOrder.getRefundable())
				.paymentMethodId(preOrder.getPaymentMethodId())
				.paymentMethodName(preOrder.getPaymentMethodName())
				.paymentStatus(preOrder.getPaymentStatus())
				.paymentRef(preOrder.getPaymentRef())
				.nonce(preOrder.getNonce())
				.refundable(preOrder.getRefundable())
				.taxName(orderCommand.getTaxName())
				.taxRate(orderCommand.getTaxRate())
				.taxInclude(orderCommand.getTaxInclude())
				.taxAmount(Optional.ofNullable(orderCommand.getTaxAmount())
						.map(Money::new)
						.orElse(null))
				.subtotalAmount(new Money(orderCommand.getSubtotalAmount()))
				.serviceChargeAmount(new Money(orderCommand.getServiceChargeAmount()))
				.discountAmount(new Money(orderCommand.getDiscountAmount()))
				.totalAmount(new Money(orderCommand.getTotalAmount()))
				.currencyCode(preOrder.getCurrencyCode())
				.build();
		// Process data
		order.initiate();
		order.setStatus(OrderStatus.PENDING);
		order.setLineItems(orderCommand.getLineItems()
				.stream()
				.map(item -> initializeOrderLineItem(order, item, dataSetItem, trackingItem))
				.toList());
		order.setServiceCharges(initializeOrderServiceCharges(order, orderCommand.getServiceCharges(),
				dataSetServiceCharge));
		setUpOrderExpireOption(tenant, order, menu);
		orderAmountProcessor.adjustAmount(order);
		validateOrderData(preOrder, order);
		processStatusOrder(preOrder, order);
		return order;
	}

	private List<OrderServiceCharge> initializeOrderServiceCharges(Order order,
			List<OrderServiceChargeCommand> serviceCharges, Map<ServiceChargeId, ServiceCharge> dataSetServiceCharge) {
		return Optional.ofNullable(serviceCharges)
				.orElse(Collections.emptyList())
				.stream()
				.map(preOrderServiceCharge -> {
					ServiceCharge serviceCharge = dataSetServiceCharge.get(new ServiceChargeId(preOrderServiceCharge
							.serviceChargeId()));
					return OrderServiceCharge.builder()
							.orderId(order.getId())
							.serviceChargeId(serviceCharge.getId())
							.name(serviceCharge.getName())
							.description(serviceCharge.getDescription())
							.chargeFixedAmount(serviceCharge.getChargeFixedAmount())
							.chargeRate(serviceCharge.getChargeRate())
							.amount(new Money(preOrderServiceCharge.amount()))
							.currency(Currency.getInstance(serviceCharge.getCurrencyCode()))
							.build();
				})
				.toList();
	}

	private void validateOrderData(PreOrder preOrder, Order order) {
		if (preOrder.getTotalAmount()
				.equals(Money.ZERO)) {
			order.setDiscountAmount(new Money(order.getSubtotalAmount()));
		}
	}

	private void processStatusOrder(PreOrder preOrder, Order order) {
		if (preOrder.getTotalAmount()
				.equals(Money.ZERO)) {
			order.setStatus(OrderStatus.PAID);
			order.setPaymentStatus(OrderPaymentStatus.PAID);
		}
	}

	private OrderLineItem initializeOrderLineItem(Order order, CreatePreOrderLineItemCommand item,
			Map<PreOrderMenuItemId, PreOrderMenuItemDto> dataSetItem, Map<Integer, OrderLineItemId> trackingItem) {
		PreOrderMenuItemDto menuItem = dataSetItem.get(new PreOrderMenuItemId(item.getPreOrderMenuItemId()));
		OrderLineItem result = OrderLineItem.builder()
				.id(orderLineItemIdGenerator.nextId())
				.tenantId(order.getTenantId())
				.orderId(order.getId())
				.mealTimeId(order.getMealTimeId())
				.preOrderMenuId(menuItem.preOrderMenuId())
				.preOrderMenuItemId(menuItem.id())
				.quantity(item.getQuantity())
				.reversible(Objects.nonNull(menuItem.capacity()))
				.metadata(menuItemToMetadata(menuItem, item.getOptions()))
				.productId(new ProductId(item.getProductId()))
				.productName(item.getProductName())
				.collectionDate(order.getCollectionDate())
				.collectionDateTime(order.getCollectionDateTime())
				.option(Optional.ofNullable(item.getOptions())
						.map(options -> options.stream()
								.map(option -> {
									String title = option.getTitle();
									return title + ": "
											+ option.getItems()
													.stream()
													.map(PreOrderLineItemOptionItemCommand::getName)
													.collect(Collectors.joining(", "));
								})
								.toList())
						.map(option -> StringUtils.join(option, ", "))
						.orElse(null))
				.optionPrice(Optional.ofNullable(item.getOptions())
						.map(options -> options.stream()
								.map(option -> option.getItems()
										.stream()
										.map(PreOrderLineItemOptionItemCommand::getAdditionalPrice)
										.map(Money::new)
										.reduce(new Money(0), Money::add))
								.reduce(new Money(0), Money::add))
						.orElse(null))
				.unitPrice(Optional.ofNullable(item.getUnitPrice())
						.map(Money::new)
						.orElse(null))
				.note(item.getNote())
				.totalDiscount(Optional.ofNullable(item.getTotalDiscount())
						.map(Money::new)
						.orElse(null))
				.totalAmount(Optional.ofNullable(item.getTotalAmount())
						.map(Money::new)
						.orElse(null))
				.build();
		trackingItem.put(item.hashCode(), result.getId());
		return result;
	}

	private ProductMetadata menuItemToMetadata(PreOrderMenuItemDto menuItem,
			List<PreOrderLineItemOptionCommand> items) {
		return ProductDataMapper.INSTANCE.toMetadataPreOrder(menuItem.product(), items);
	}

	private void setUpOrderExpireOption(TenantDto tenant, Order order, PreOrderMenuDto menu) {
		int cutOffDays = Optional.ofNullable(menu.cutOffDays())
				.orElse(0);
		LocalTime cutOffTime = menu.cutOffTime();
		ZonedDateTime validDate = ZonedDateTime.of(order.getCollectionDate()
				.minusDays(cutOffDays), cutOffTime, TenantHelper.getZoneId(tenant));
		Instant cutoffInstant = validDate.toInstant();
		order.setExpiredAt(order.getCreatedAt()
				.plus(orderConfigureRepository.getPreOrderConfig()
						.getExpirationTime()));
		order.setCancellationDueAt(cutoffInstant);
	}

	private void setUpPreOrderExpireOption(PreOrder preOrder) {
		PreOrderConfig config = orderConfigureRepository.getPreOrderConfig();
		Instant createdAt = preOrder.getCreatedAt();
		preOrder.setExpiredAt(createdAt.plus(config.getExpirationTime()));
		preOrder.setCancellationDueAt(createdAt.plus(config.getCancellationDueTime()));
	}

}
