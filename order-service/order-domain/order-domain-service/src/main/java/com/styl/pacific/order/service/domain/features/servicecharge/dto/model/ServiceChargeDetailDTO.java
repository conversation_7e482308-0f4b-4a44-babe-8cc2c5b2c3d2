/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.servicecharge.dto.model;

import com.styl.pacific.domain.enums.PlatformOrderType;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.order.service.domain.features.servicecharge.valueobjects.ServiceChargeId;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record ServiceChargeDetailDTO(ServiceChargeId id,
		TenantId tenantId,
		Boolean isActive,
		String name,
		String description,
		Money chargeFixedAmount,
		BigDecimal chargeRate,
		String currencyCode,
		List<PlatformOrderType> platformOrderTypes,
		Instant createdAt,
		Instant updatedAt,
		UserId createdBy,
		UserId updatedBy) {
}
