/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.servicecharge.dto.command;

import com.styl.pacific.domain.enums.PlatformOrderType;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
public class UpdateServiceChargeCommand {
	@NotNull(message = "isActive must not be null")
	private Boolean isActive;
	@NotBlank(message = "name must not be blank")
	@Size(max = 80, message = "name must not exceed 80 characters")
	private String name;
	@Size(max = 255, message = "description must not exceed 255 characters")
	private String description;
	@NotNull(message = "chargeFixedAmount must not be null")
	@Min(value = 0, message = "chargeFixedAmount must not be less than 0")
	private BigInteger chargeFixedAmount;
	@NotNull(message = "chargeRate must not be null")
	@Min(value = 0, message = "chargeRate must not be less than 0")
	private BigDecimal chargeRate;
	@NotNull(message = "platformOrderTypes must not be null")
	@Size(min = 1, message = "platformOrderTypes must not be empty")
	private List<PlatformOrderType> platformOrderTypes;
}
