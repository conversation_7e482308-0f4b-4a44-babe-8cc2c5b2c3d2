/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.processor;

import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.order.service.domain.features.order.processor.OrderAmountProcessor;
import com.styl.pacific.order.service.domain.features.preorder.order.entity.PreOrder;
import java.math.BigInteger;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PreOrderAmountProcessorImpl implements PreOrderAmountProcessor {
	private final OrderAmountProcessor orderAmountProcessor;

	@Override
	public PreOrder adjustAmount(PreOrder preOrder) {
		if (Objects.isNull(preOrder.getTaxAmount())) {
			preOrder.setTaxAmount(new Money(BigInteger.ZERO));
		}
		if (Objects.isNull(preOrder.getSubtotalAmount())) {
			preOrder.setSubtotalAmount(new Money(BigInteger.ZERO));
		}
		if (Objects.isNull(preOrder.getDiscountAmount())) {
			preOrder.setDiscountAmount(new Money(BigInteger.ZERO));
		}
		if (Objects.isNull(preOrder.getTotalAmount())) {
			preOrder.setTotalAmount(new Money(BigInteger.ZERO));
		}
		preOrder.getSubOrders()
				.forEach(orderAmountProcessor::adjustAmount);
		return preOrder;
	}
}
