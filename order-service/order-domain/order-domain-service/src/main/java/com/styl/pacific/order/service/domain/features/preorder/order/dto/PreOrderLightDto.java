/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.dto;

import com.styl.pacific.domain.enums.order.OrderCancellationType;
import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.PaymentMethodId;
import com.styl.pacific.domain.valueobject.PaymentTransactionId;
import com.styl.pacific.domain.valueobject.PreOrderId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.order.service.domain.features.preorder.order.enums.PreOrderStatus;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.With;

/**
 * <AUTHOR>
 */
@Builder
@Data
@With
@AllArgsConstructor
public class PreOrderLightDto {
	private final PreOrderId id;
	private final TenantId tenantId;
	private final String preOrderNumber;
	private final String businessName;
	private final String systemSource;
	private final Long version;

	private final UserId issuerId;
	private final UserId customerId;
	private final String customerName;
	private final String customerEmail;

	private final PreOrderStatus status;

	private final Money subtotalAmount;
	private final Money serviceChargeAmount;
	private final String taxName;
	private final String taxRegNo;
	private final BigDecimal taxRate;
	private final Boolean taxInclude;
	private final Money taxAmount;
	private final Money discountAmount;
	private final Money totalAmount;
	private final String currencyCode;

	private final PaymentMethodId paymentMethodId;
	private final String paymentMethodName;
	private final String paymentRef;
	private final PaymentTransactionId paymentTransactionId;
	private final OrderPaymentStatus paymentStatus;
	private final Boolean refundable;
	private final UUID nonce;

	private final Instant cancellationDueAt;
	private final OrderCancellationType cancellationType;
	private final String cancelReason;
	private final Instant canceledAt;
	private final Instant expiredAt;

	private final Instant createdAt;
	private final Instant updatedAt;
}
