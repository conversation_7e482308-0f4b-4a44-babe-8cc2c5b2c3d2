/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.menuboard.ports.input.service;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.menuboard.dto.MenuBoardSessionDetailDto;
import com.styl.pacific.order.service.domain.features.menuboard.dto.command.ActivateSessionMenuBoardCommand;
import com.styl.pacific.order.service.domain.features.menuboard.dto.command.GenerateActivationCodeCommand;
import com.styl.pacific.order.service.domain.features.menuboard.dto.query.PaginationMenuBoardSessionQuery;
import com.styl.pacific.order.service.domain.features.menuboard.entity.MenuBoardActivationCodeSession;
import com.styl.pacific.order.service.domain.features.menuboard.entity.MenuBoardSession;
import com.styl.pacific.order.service.domain.features.menuboard.valueobjects.MenuBoardSessionId;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
public interface MenuBoardSessionService {
	MenuBoardSessionDetailDto findById(TenantId tenantId,
			@NotNull(message = "id must not be null") MenuBoardSessionId id);

	Paging<MenuBoardSession> findAllPaging(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@Valid PaginationMenuBoardSessionQuery query);

	MenuBoardActivationCodeSession generateActivateSessionCode(
			@NotNull(message = "command must not be null") @Valid GenerateActivationCodeCommand command);

	MenuBoardSessionDetailDto verifyActivationCodeMenuBoard(TenantId tenantId,
			@NotNull(message = "command must not be null") @Valid ActivateSessionMenuBoardCommand command);

	void revokeSession(TenantId tenantId, @NotNull(message = "id must not be null") MenuBoardSessionId id);
}
