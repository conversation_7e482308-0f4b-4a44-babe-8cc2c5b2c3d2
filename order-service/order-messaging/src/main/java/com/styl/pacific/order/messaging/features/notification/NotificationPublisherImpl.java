/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.messaging.features.notification;

import com.styl.pacific.domain.enums.ModelConverterId;
import com.styl.pacific.kafka.notification.avro.model.NotificationAvroChannel;
import com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent;
import com.styl.pacific.notification.service.constant.Action;
import com.styl.pacific.notification.service.constant.params.NotificationCommonKey;
import com.styl.pacific.notification.service.constant.params.OrderStatusUpdateTemplateKey;
import com.styl.pacific.notification.service.constant.params.PreOrderStatusUpdateTemplateKey;
import com.styl.pacific.notification.service.requests.SubOrderCollectionRequest;
import com.styl.pacific.order.messaging.features.notification.kafka.NotificationCreatedEventKafkaPublisher;
import com.styl.pacific.order.service.domain.features.notification.NotificationPublisher;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.preorder.order.entity.PreOrder;
import com.styl.pacific.utils.json.JsonFormatUtil;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.text.WordUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class NotificationPublisherImpl implements NotificationPublisher {
	private final NotificationCreatedEventKafkaPublisher kafkaPublisher;
	private DateTimeFormatter collectionDateTimeFormatter = DateTimeFormatter.ofPattern("EEEE, dd/MM/yyyy");
	private static final String NOTIFICATION_SOURCE = "order-service";

	@Override
	public void publish(Order order, Instant instant) {
		if (Optional.ofNullable(order.getCustomerId())
				.isPresent()) {
			NotificationCreatedAvroEvent event = NotificationCreatedAvroEvent.newBuilder()
					.setId(UUID.randomUUID())
					.setUserId(order.getCustomerId()
							.getValue())
					.setTenantId(order.getTenantId()
							.getValue())
					.setAction(Action.UPDATE_ORDER_STATUS)
					.setSource(NOTIFICATION_SOURCE)
					.setData(Map.of(OrderStatusUpdateTemplateKey.ORDER_NUMBER, order.getOrderNumber(),
							OrderStatusUpdateTemplateKey.ORDER_STATUS, order.getStatus()
									.name(), OrderStatusUpdateTemplateKey.UPDATED_DATE, String.valueOf(instant
											.toEpochMilli())))
					.setCreatedAt(Instant.now()
							.toEpochMilli())
					.build();
			kafkaPublisher.publish(event);
		}
	}

	@Override
	public void publish(PreOrder preOrder, Instant instant) {
		if (Objects.nonNull(preOrder.getCustomerId())) {
			Map<String, List<SubOrderCollectionRequest>> convertedSubOrderMap = preOrder.getSubOrders()
					.stream()
					.collect(Collectors.groupingBy(Order::getCollectionDate, TreeMap::new, Collectors.collectingAndThen(
							Collectors.toList(), list -> list.stream()
									.sorted(Comparator.comparing(o -> o.getMealTime()
											.getStartTime()))
									.map(order -> SubOrderCollectionRequest.builder()
											.mealTimeName(order.getMealTime()
													.getName())
											.totalAmount(order.getTotalAmount()
													.getAmount())
											.build())
									.toList())))
					.entrySet()
					.stream()
					.collect(Collectors.toMap(e -> e.getKey()
							.format(collectionDateTimeFormatter), Map.Entry::getValue, (a, b) -> a,
							LinkedHashMap::new));

			NotificationCreatedAvroEvent event = NotificationCreatedAvroEvent.newBuilder()
					.setId(UUID.randomUUID())
					.setUserId(preOrder.getCustomerId()
							.getValue())
					.setTenantId(preOrder.getTenantId()
							.getValue())
					.setAction(Action.CONFIRM_PRE_ORDER)
					.setSource(NOTIFICATION_SOURCE)
					.setChannels(List.of(NotificationAvroChannel.EMAIL))
					.setData(Map.ofEntries(Map.entry(NotificationCommonKey.MODEL_CONVERTER_ID,
							ModelConverterId.PRE_ORDER_CONVERTER.name()), Map.entry(
									PreOrderStatusUpdateTemplateKey.BUSINESS_NAME, preOrder.getBusinessName()), Map
											.entry(PreOrderStatusUpdateTemplateKey.TAX_REG_NO, preOrder.getTaxRegNo()),
							Map.entry(PreOrderStatusUpdateTemplateKey.PRE_ORDER_ID, preOrder.getId()
									.getValue()
									.toString()), Map.entry(PreOrderStatusUpdateTemplateKey.PRE_ORDER_NUMBER, preOrder
											.getPreOrderNumber()), Map.entry(
													PreOrderStatusUpdateTemplateKey.DATE_OF_ISSUE, String.valueOf(
															instant.toEpochMilli())), Map.entry(
																	PreOrderStatusUpdateTemplateKey.PAYMENT_STATUS,
																	WordUtils.capitalizeFully(preOrder
																			.getPaymentStatus()
																			.name())), Map.entry(
																					PreOrderStatusUpdateTemplateKey.PAYMENT_METHOD,
																					preOrder.getPaymentMethodName()),
							Map.entry(PreOrderStatusUpdateTemplateKey.SUB_ORDER_COLLECTIONS, JsonFormatUtil
									.convertToString(convertedSubOrderMap)), Map.entry(
											PreOrderStatusUpdateTemplateKey.SUB_TOTAL_AMOUNT, String.valueOf(preOrder
													.getSubtotalAmount()
													.getAmount())), Map.entry(
															PreOrderStatusUpdateTemplateKey.DISCOUNT_AMOUNT, String
																	.valueOf(preOrder.getDiscountAmount()
																			.getAmount())), Map.entry(
																					PreOrderStatusUpdateTemplateKey.TAX_NAME,
																					preOrder.getTaxName()), Map.entry(
																							PreOrderStatusUpdateTemplateKey.TAX_AMOUNT,
																							String.valueOf(preOrder
																									.getTaxAmount()
																									.getAmount())), Map
																											.entry(PreOrderStatusUpdateTemplateKey.TOTAL_AMOUNT,
																													String.valueOf(
																															preOrder.getTotalAmount()
																																	.getAmount()))))
					.setCreatedAt(Instant.now()
							.toEpochMilli())
					.build();

			kafkaPublisher.publish(event);
		}
	}
}