CREATE
    TABLE
        IF NOT EXISTS tb_order(
            id BIGINT PRIMARY KEY NOT NULL,
            tenant_id BIGINT NOT NULL,
            store_id BIGINT NOT NULL,
            order_number VARCHAR(255) NOT NULL,
            version INT NOT NULL DEFAULT 0,
            staff_code VARCHAR(255),
            staff_name VARCHAR(255),
            issuer_id BIGINT,
            customer_id BIGINT,
            customer_email VARCHAR(120),
            customer_name VA<PERSON>HA<PERSON>(80),
            status VARCHAR(255) NOT NULL,
            TYPE VARCHAR(255) NOT NULL,
            pre_order_id BIGINT,
            meal_time_id BIGINT,
            note VARCHAR(255),
            tax_name VARCHAR(80),
            tax_rate NUMERIC(
                5,
                4
            ),
            tax_include BOOLEAN,
            tax_amount NUMERIC(
                38,
                0
            ),
            subtotal_amount NUMERIC(
                38,
                0
            ) NOT NULL,
            discount_amount NUMERIC(
                38,
                0
            ) NOT NULL,
            total_amount NUMERIC(
                38,
                0
            ) NOT NULL,
            currency_code VARCHAR(3) NOT NULL,
            payment_method_id BIGINT NOT NULL,
            payment_id BIGINT,
            payment_status VARCHAR(255) NOT NULL,
            transaction_ref VARCHAR(255),
            terminal_id VARCHAR(255),
            payment_ref VARCHAR(64),
            nonce VARCHAR(255),
            cancellation_due_at TIMESTAMP(6) WITH TIME ZONE,
            cancel_reason VARCHAR(255),
            canceled_at TIMESTAMP(6) WITH TIME ZONE,
            expired_at TIMESTAMP(6) WITH TIME ZONE,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE
        );

CREATE
    INDEX IF NOT EXISTS idx_tb_order_tenant_id_store_id_id ON
    tb_order(
        tenant_id,
        store_id,
        id
    );

CREATE
    TABLE
        IF NOT EXISTS tb_order_line_item(
            id BIGINT PRIMARY KEY NOT NULL,
            order_id BIGINT NOT NULL,
            product_id BIGINT NOT NULL,
            product_name VARCHAR(255) NOT NULL,
            metadata JSONB NOT NULL,
            OPTION VARCHAR(255),
            quantity INTEGER NOT NULL,
            note VARCHAR(255),
            unit_price NUMERIC(
                38,
                0
            ) NOT NULL,
            option_price NUMERIC(
                38,
                0
            ) NOT NULL,
            total_discount NUMERIC(
                38,
                0
            ) NOT NULL,
            total_amount NUMERIC(
                38,
                0
            ) NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            CONSTRAINT fk_tb_order_line_item_order_id_tb_order FOREIGN KEY(order_id) REFERENCES tb_order(id)
        );