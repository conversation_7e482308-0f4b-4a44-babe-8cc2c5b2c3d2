/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.order.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.domain.enums.order.OrderCancellationType;
import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.domain.enums.order.OrderStatus;
import com.styl.pacific.domain.enums.order.OrderType;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.OrderEntity;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.OrderLineItemEntity;
import com.styl.pacific.order.service.data.access.jpa.features.product.entity.ProductEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 */
@Builder
public class OrderSpecifications {

	public static Specification<OrderEntity> countItemPlaceByDateAndMealTime(Long tenantId, Long userId, LocalDate date,
			Long mealTimeId) {
		List<Specification<OrderEntity>> specs = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specs.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(userId)) {
			specs.add(withCustomerId(userId));
		}
		if (Objects.nonNull(date)) {
			specs.add(withCollectionDate(date));
		}
		if (Objects.nonNull(mealTimeId)) {
			specs.add(withMealTimeId(mealTimeId));
		}
		specs.add(withTypes(List.of(OrderType.PRE_ORDER)));
		specs.add(withStatuses(List.of(OrderStatus.PENDING, OrderStatus.PAID, OrderStatus.COMPLETED,
				OrderStatus.COLLECTED)));
		return specs.stream()
				.reduce(Specification::and)
				.orElseGet(() -> (root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<OrderEntity> withTenantIdAndIdempotencyKey(Long tenantId, String idempotencyKey) {
		List<Specification<OrderEntity>> specs = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specs.add(withTenantId(tenantId));
		}
		if (StringUtils.isNotBlank(idempotencyKey)) {
			specs.add(withIdempotencyKey(idempotencyKey));
		}
		return specs.stream()
				.reduce(Specification::and)
				.orElseGet(() -> (root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<OrderEntity> withMultipleCriteria(String matchesId, Long tenantId, List<Long> orderIds,
			List<Long> preOrderIds, List<Long> preOrderGroupIds, Long storeId, String orderNumber,
			List<OrderStatus> statuses, List<OrderCancellationType> cancellationTypes,
			List<OrderPaymentStatus> paymentStatuses, List<OrderType> types, Set<Long> customerIds, String customerName,
			String customerEmail, Long paymentMethodId, String staffName, Boolean refundable, Boolean expired,
			List<Long> mealTimeIds, Boolean isOffline, Long paymentTransactionId, List<Long> categoryIds,
			LocalDate fromCollectionDate, LocalDate toCollectionDate, Instant fromTime, Instant toTime,
			Instant fromOrderedTime, Instant toOrderedTime, Instant fromUpdatedTime, Instant toUpdatedTime) {
		List<Specification<OrderEntity>> specs = new ArrayList<>();
		if (StringUtils.isNotBlank(matchesId)) {
			specs.add(withMatchesId(matchesId));
		}
		if (Objects.nonNull(tenantId)) {
			specs.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(orderIds) && !orderIds.isEmpty()) {
			specs.add(withOrderIds(orderIds));
		}
		if (Objects.nonNull(preOrderIds) && !preOrderIds.isEmpty()) {
			specs.add(withPreOrderIds(preOrderIds));
		}
		if (Objects.nonNull(preOrderGroupIds) && !preOrderGroupIds.isEmpty()) {
			specs.add(withPreOrderGroupIds(preOrderGroupIds));
		}
		if (Objects.nonNull(storeId)) {
			specs.add(withStoreId(storeId));
		}
		if (StringUtils.isNotBlank(orderNumber)) {
			specs.add(withOrderNumber(orderNumber));
		}
		if (Objects.nonNull(statuses) && !statuses.isEmpty()) {
			specs.add(withStatuses(statuses));
		}
		if (Objects.nonNull(cancellationTypes) && !cancellationTypes.isEmpty()) {
			specs.add(withCancellationTypes(cancellationTypes));
		}
		if (Objects.nonNull(paymentStatuses) && !paymentStatuses.isEmpty()) {
			specs.add(withPaymentStatuses(paymentStatuses));
		}
		if (Objects.nonNull(types) && !types.isEmpty()) {
			specs.add(withTypes(types));
		}
		if (Objects.nonNull(customerIds) && !customerIds.isEmpty()) {
			specs.add(withCustomerIds(customerIds));
		}
		if (StringUtils.isNotBlank(customerName)) {
			specs.add(withCustomerName(customerName));
		}
		if (StringUtils.isNotBlank(customerEmail)) {
			specs.add(withCustomerEmail(customerEmail));
		}
		if (Objects.nonNull(paymentMethodId)) {
			specs.add(withPaymentMethodId(paymentMethodId));
		}
		if (StringUtils.isNotBlank(staffName)) {
			specs.add(withStaffName(staffName));
		}
		if (Objects.nonNull(mealTimeIds) && !mealTimeIds.isEmpty()) {
			specs.add(withMealTimeIds(mealTimeIds));
		}
		if (Objects.nonNull(isOffline)) {
			specs.add(withIsOffline(isOffline));
		}
		if (Objects.nonNull(paymentTransactionId)) {
			specs.add(withPaymentTransactionId(paymentTransactionId));
		}
		if (Objects.nonNull(categoryIds) && !categoryIds.isEmpty()) {
			specs.add(withCategoryIds(categoryIds));
		}
		if (Objects.nonNull(refundable)) {
			specs.add(withRefundable(refundable));
		}
		if (Objects.nonNull(expired)) {
			specs.add(withExpired(expired));
		}
		if (Objects.nonNull(fromCollectionDate) || Objects.nonNull(toCollectionDate)) {
			List<Specification<OrderEntity>> collectionTimePredicates = new ArrayList<>();
			if (Objects.nonNull(fromCollectionDate)) {
				collectionTimePredicates.add(withFromCollectionDate(fromCollectionDate));
			}
			if (Objects.nonNull(toCollectionDate)) {
				collectionTimePredicates.add(withToCollectionDate(toCollectionDate));
			}
			collectionTimePredicates.add(withNotNullCollectionTime());
			specs.add(collectionTimePredicates.stream()
					.reduce(Specification::and)
					.orElseGet(() -> (root, query, criteriaBuilder) -> criteriaBuilder.conjunction()));
		}
		if (Objects.nonNull(fromTime)) {
			specs.add(withFromTime(fromTime));
		}
		if (Objects.nonNull(toTime)) {
			specs.add(withToTime(toTime));
		}

		if (Objects.nonNull(fromOrderedTime)) {
			specs.add(withFromOrderedTime(fromOrderedTime));
		}
		if (Objects.nonNull(toOrderedTime)) {
			specs.add(withToOrderedTime(toOrderedTime));
		}
		if (Objects.nonNull(fromUpdatedTime)) {
			specs.add(withFromUpdatedTime(fromUpdatedTime));
		}
		if (Objects.nonNull(toUpdatedTime)) {
			specs.add(withToUpdatedTime(toUpdatedTime));
		}
		return specs.stream()
				.reduce(Specification::and)
				.orElseGet(() -> (root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<OrderEntity> withTenantIdAndId(Long tenantId, Long orderId) {
		return withTenantId(tenantId).and(withOrderId(orderId));
	}

	public static Specification<OrderEntity> withMatchesId(String matchesId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return like(criteriaBuilder, criteriaBuilder.concat(root.get(OrderEntity.FIELD_ID)
						.as(String.class), ""), String.valueOf(matchesId));
			}
		};
	}

	public static Specification<OrderEntity> withOrderId(Long orderId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderEntity.FIELD_ID), orderId);
			}
		};
	}

	public static Specification<OrderEntity> withTenantId(Long tenantId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderEntity.FIELD_TENANT_ID), tenantId);
			}
		};
	}

	public static Specification<OrderEntity> withOrderIds(List<Long> orderIds) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(OrderEntity.FIELD_ID), new ArrayList<>(orderIds));
			}
		};
	}

	public static Specification<OrderEntity> withPreOrderIds(List<Long> preOrderIds) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(OrderEntity.FIELD_PRE_ORDER_ID), new ArrayList<>(preOrderIds));
			}
		};
	}

	public static Specification<OrderEntity> withPreOrderGroupIds(List<Long> preOrderGroupIds) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(OrderEntity.FIELD_PRE_ORDER_GROUP_ID), new ArrayList<>(preOrderGroupIds));
			}
		};
	}

	public static Specification<OrderEntity> withStoreId(Long storeId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderEntity.FIELD_STORE_ID), storeId);
			}
		};
	}

	public static Specification<OrderEntity> withOrderNumber(String orderNumber) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return like(criteriaBuilder, root.get(OrderEntity.FIELD_ORDER_NUMBER), orderNumber);
			}
		};
	}

	public static Specification<OrderEntity> withStatuses(List<OrderStatus> statuses) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(OrderEntity.FIELD_STATUS), new ArrayList<>(statuses));
			}
		};
	}

	public static Specification<OrderEntity> withCancellationTypes(List<OrderCancellationType> cancellationTypes) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(OrderEntity.FIELD_CANCELLATION_TYPE), new ArrayList<>(cancellationTypes));
			}
		};
	}

	public static BaseSpecification<OrderEntity> withPaymentStatuses(List<OrderPaymentStatus> paymentStatuses) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(OrderEntity.FIELD_PAYMENT_STATUS), new ArrayList<>(paymentStatuses));
			}
		};
	}

	public static Specification<OrderEntity> withTypes(List<OrderType> types) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(OrderEntity.FIELD_TYPE), new ArrayList<>(types));
			}
		};
	}

	public static Specification<OrderEntity> withCustomerIds(Set<Long> customerIds) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				if (customerIds.size() == 1) {
					return equals(criteriaBuilder, root.get(OrderEntity.FIELD_CUSTOMER_ID), customerIds.stream()
							.findFirst()
							.orElseThrow());
				} else {
					return and(criteriaBuilder, List.of(root.get(OrderEntity.FIELD_CUSTOMER_ID)
							.isNotNull(), in(root.get(OrderEntity.FIELD_CUSTOMER_ID), new ArrayList<>(customerIds))));
				}
			}
		};
	}

	public static Specification<OrderEntity> withCustomerId(Long customerId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderEntity.FIELD_CUSTOMER_ID), customerId);
			}
		};
	}

	public static Specification<OrderEntity> withCustomerName(String customerName) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return like(criteriaBuilder, root.get(OrderEntity.FIELD_CUSTOMER_NAME), customerName);
			}
		};
	}

	public static Specification<OrderEntity> withCustomerEmail(String customerEmail) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return like(criteriaBuilder, root.get(OrderEntity.FIELD_CUSTOMER_EMAIL), customerEmail);
			}
		};
	}

	public static Specification<OrderEntity> withPaymentMethodId(Long paymentMethodId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderEntity.FIELD_PAYMENT_METHOD_ID), paymentMethodId);
			}
		};
	}

	public static Specification<OrderEntity> withStaffName(String staffName) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderEntity.FIELD_STAFF_NAME), staffName);
			}
		};
	}

	public static Specification<OrderEntity> withMealTimeId(Long mealTimeId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderEntity.FIELD_MEAL_TIME_ID), mealTimeId);
			}
		};
	}

	public static Specification<OrderEntity> withMealTimeIds(List<Long> mealTimeIds) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(OrderEntity.FIELD_MEAL_TIME_ID), new ArrayList<>(mealTimeIds));
			}
		};
	}

	public static Specification<OrderEntity> withCategoryIds(List<Long> categoryIds) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(OrderEntity.FIELD_LINE_ITEMS)
						.get(OrderLineItemEntity.FIELD_PRODUCT)
						.get(ProductEntity.FIELD_CATEGORY_ID), new ArrayList<>(categoryIds));
			}
		};
	}

	public static Specification<OrderEntity> withRefundable(Boolean refundable) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderEntity.FIELD_REFUNDABLE), refundable);
			}
		};
	}

	public static Specification<OrderEntity> withExpired(Boolean expired) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				if (Boolean.TRUE.equals(expired)) {
					return lessThanOrEqualTo(criteriaBuilder, root.get(OrderEntity.FIELD_EXPIRED_AT), Instant.now());
				} else {
					return greaterThan(criteriaBuilder, root.get(OrderEntity.FIELD_EXPIRED_AT), Instant.now());
				}
			}
		};
	}

	public static Specification<OrderEntity> withFromCollectionDate(LocalDate fromCollectionDate) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return greaterThanOrEqualTo(criteriaBuilder, root.get(OrderEntity.FIELD_COLLECTION_DATE),
						fromCollectionDate);
			}
		};
	}

	public static BaseSpecification<OrderEntity> withToCollectionDate(LocalDate toCollectionDate) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return lessThanOrEqualTo(criteriaBuilder, root.get(OrderEntity.FIELD_COLLECTION_DATE),
						toCollectionDate);
			}
		};
	}

	public static Specification<OrderEntity> withNotNullCollectionTime() {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(OrderEntity.FIELD_COLLECTION_DATETIME)
						.isNotNull();
			}
		};
	}

	public static Specification<OrderEntity> withCollectionDate(LocalDate date) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderEntity.FIELD_COLLECTION_DATE), date);
			}
		};
	}

	public static BaseSpecification<OrderEntity> withFromTime(Instant fromTime) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return greaterThanOrEqualTo(criteriaBuilder, root.get(OrderEntity.FIELD_CREATED_AT), fromTime);
			}
		};
	}

	public static Specification<OrderEntity> withToTime(Instant toTime) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return lessThanOrEqualTo(criteriaBuilder, root.get(OrderEntity.FIELD_CREATED_AT), toTime);
			}
		};
	}

	public static BaseSpecification<OrderEntity> withFromOrderedTime(Instant fromOrderedTime) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return greaterThanOrEqualTo(criteriaBuilder, root.get(OrderEntity.FIELD_ORDERED_AT), fromOrderedTime);
			}
		};
	}

	public static Specification<OrderEntity> withToOrderedTime(Instant toOrderedTime) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return lessThanOrEqualTo(criteriaBuilder, root.get(OrderEntity.FIELD_ORDERED_AT), toOrderedTime);
			}
		};
	}

	public static BaseSpecification<OrderEntity> withFromUpdatedTime(Instant fromUpdatedTime) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return greaterThanOrEqualTo(criteriaBuilder, root.get(OrderEntity.FIELD_UPDATED_AT), fromUpdatedTime);
			}
		};
	}

	public static Specification<OrderEntity> withToUpdatedTime(Instant toUpdatedTime) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return lessThanOrEqualTo(criteriaBuilder, root.get(OrderEntity.FIELD_UPDATED_AT), toUpdatedTime);
			}
		};
	}

	public static Specification<OrderEntity> withIdempotencyKey(String idempotencyKey) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderEntity.FIELD_IDEMPOTENCY_KEY), idempotencyKey);
			}
		};
	}

	public static Specification<OrderEntity> withIsOffline(Boolean isOffline) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderEntity.FIELD_IS_OFFLINE), isOffline);
			}
		};
	}

	public static Specification<OrderEntity> withPaymentTransactionId(Long paymentTransactionId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderEntity.FIELD_PAYMENT_TRANSACTION_ID),
						paymentTransactionId);
			}
		};
	}

}
