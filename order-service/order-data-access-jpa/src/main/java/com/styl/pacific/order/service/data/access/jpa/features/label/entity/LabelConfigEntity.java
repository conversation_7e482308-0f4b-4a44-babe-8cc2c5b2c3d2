/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.label.entity;

import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.time.Instant;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "tb_label_config")
@DynamicUpdate
@EntityListeners(AuditingEntityListener.class)
public class LabelConfigEntity {
	public static final String FIELD_ID = "id";
	public static final String FIELD_PAGE_SETUP = "pageSetup";
	public static final String FIELD_CELL_FORMAT = "cellFormat";
	public static final String FIELD_LABEL_LAYOUT = "labelLayout";
	public static final String FIELD_TENANT_ID = "tenantId";

	public static final Set<String> SORTABLE_FIELDS = Set.of(FIELD_ID, FIELD_TENANT_ID);

	@Id
	private Long id;

	@Column(nullable = false)
	private Long tenantId;

	@Type(JsonBinaryType.class)
	@Column(columnDefinition = "jsonb")
	private CellFormatPropertiesEntity cellFormat;

	@Type(JsonBinaryType.class)
	@Column(columnDefinition = "jsonb")
	private PageSetupPropertiesEntity pageSetup;

	@Type(JsonBinaryType.class)
	@Column(columnDefinition = "jsonb")
	private LabelLayoutPropertiesEntity labelLayout;

	@CreatedDate
	@Temporal(TemporalType.TIMESTAMP)
	@Column(nullable = false, updatable = false)
	protected Instant createdAt;

	@LastModifiedDate
	@Temporal(TemporalType.TIMESTAMP)
	protected Instant updatedAt;

	private LabelConfigEntity(Builder builder) {
		setId(builder.id);
		setTenantId(builder.tenantId);
		setCellFormat(builder.cellFormat);
		setPageSetup(builder.pageSetup);
		setLabelLayout(builder.labelLayout);
		setCreatedAt(builder.createdAt);
		setUpdatedAt(builder.updatedAt);
	}

	public static Builder builder() {
		return new Builder();
	}

	public static final class Builder {
		private Long id;
		private Long tenantId;
		private CellFormatPropertiesEntity cellFormat;
		private PageSetupPropertiesEntity pageSetup;
		private LabelLayoutPropertiesEntity labelLayout;
		private Instant createdAt;
		private Instant updatedAt;

		private Builder() {
		}

		public Builder id(Long id) {
			this.id = id;
			return this;
		}

		public Builder tenantId(Long tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder cellFormat(CellFormatPropertiesEntity cellFormat) {
			this.cellFormat = cellFormat;
			return this;
		}

		public Builder pageSetup(PageSetupPropertiesEntity pageSetup) {
			this.pageSetup = pageSetup;
			return this;
		}

		public Builder labelLayout(LabelLayoutPropertiesEntity labelLayout) {
			this.labelLayout = labelLayout;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public LabelConfigEntity build() {
			return new LabelConfigEntity(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;

		if (!(o instanceof LabelConfigEntity entity))
			return false;

		return new EqualsBuilder().append(id, entity.id)
				.append(tenantId, entity.tenantId)
				.append(cellFormat, entity.cellFormat)
				.append(pageSetup, entity.pageSetup)
				.append(labelLayout, entity.labelLayout)
				.append(createdAt, entity.createdAt)
				.append(updatedAt, entity.updatedAt)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).append(id)
				.append(tenantId)
				.append(cellFormat)
				.append(pageSetup)
				.append(labelLayout)
				.append(createdAt)
				.append(updatedAt)
				.toHashCode();
	}
}
