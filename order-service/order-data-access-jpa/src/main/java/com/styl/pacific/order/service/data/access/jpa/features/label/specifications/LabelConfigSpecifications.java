/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.label.specifications;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.order.service.data.access.jpa.features.label.entity.LabelConfigEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 */
public class LabelConfigSpecifications {

	public static Specification<LabelConfigEntity> withTenantIdAndId(Long tenantId, Long id) {
		List<Specification<LabelConfigEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(id)) {
			specifications.add(withId(id));
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<LabelConfigEntity> withMultipleCriteria(Long tenantId, String name,
			Integer numberOfColumns) {
		List<Specification<LabelConfigEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (StringUtils.isNotBlank(name)) {
			specifications.add(withLikeName(name));
		}
		if (Objects.nonNull(numberOfColumns)) {
			specifications.add(withNumberOfColumns(numberOfColumns));
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<LabelConfigEntity> withId(Long id) {
		return new BaseSpecification<LabelConfigEntity>() {
			@Override
			public Predicate toPredicate(@NonNull Root<LabelConfigEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(LabelConfigEntity.FIELD_ID), id);
			}
		};
	}

	public static Specification<LabelConfigEntity> withTenantId(Long tenantId) {
		return new BaseSpecification<LabelConfigEntity>() {
			@Override
			public Predicate toPredicate(@NonNull Root<LabelConfigEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(LabelConfigEntity.FIELD_TENANT_ID), tenantId);
			}
		};
	}

	public static Specification<LabelConfigEntity> withLikeName(String name) {
		return new BaseSpecification<LabelConfigEntity>() {
			@Override
			public Predicate toPredicate(@NonNull Root<LabelConfigEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				Expression<?> pageSetup = root.get(LabelConfigEntity.FIELD_PAGE_SETUP);
				Expression<String> nameExpression = criteriaBuilder.function("jsonb_extract_path_text", String.class,
						pageSetup, criteriaBuilder.literal("pageName"));
				return like(criteriaBuilder, nameExpression, name);
			}
		};
	}

	public static Specification<LabelConfigEntity> withNumberOfColumns(Integer numberOfColumns) {
		return new BaseSpecification<LabelConfigEntity>() {
			@Override
			public Predicate toPredicate(@NonNull Root<LabelConfigEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				Expression<?> pageSetup = root.get(LabelConfigEntity.FIELD_LABEL_LAYOUT);
				Expression<String> numberOfColumnsExpression = criteriaBuilder.function("jsonb_extract_path_text",
						String.class, pageSetup, criteriaBuilder.literal("dimensionNumberOfColumns"));
				return equals(criteriaBuilder, numberOfColumnsExpression, numberOfColumns);
			}
		};
	}
}
