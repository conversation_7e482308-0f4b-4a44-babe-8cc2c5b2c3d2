/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.preorder.order.entity;

import com.styl.pacific.data.access.entity.AuditableEntity;
import com.styl.pacific.domain.enums.order.OrderCancellationType;
import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.OrderEntity;
import com.styl.pacific.order.service.domain.features.preorder.order.enums.PreOrderStatus;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.DynamicUpdate;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "tb_pre_order")
@AllArgsConstructor
@NoArgsConstructor
@DynamicUpdate
public class PreOrderEntity extends AuditableEntity {
	public static final String FIELD_ID = "id";
	public static final String FIELD_STORE_ID = "storeId";
	public static final String FIELD_TENANT_ID = "tenantId";
	public static final String FIELD_STATUS = "status";
	public static final String FIELD_CANCELLATION_TYPE = "cancellationType";
	public static final String FIELD_ISSUER_ID = "issuerId";
	public static final String FIELD_CUSTOMER_ID = "customerId";
	public static final String FIELD_CREATED_AT = "createdAt";
	public static final String FIELD_PAYMENT_STATUS = "paymentStatus";
	public static final String FIELD_PAYMENT_METHOD_ID = "paymentMethodId";
	public static final String FIELD_CUSTOMER_NAME = "customerName";
	public static final String FIELD_TOTAL_AMOUNT = "totalAmount";
	public static final String FIELD_SUBORDERS = "subOrders";
	public static final String FIELD_EXPIRED_AT = "expiredAt";

	public static final Set<String> SORTABLE_FIELDS = Set.of(FIELD_ID, FIELD_STATUS, FIELD_CUSTOMER_ID,
			FIELD_PAYMENT_STATUS, FIELD_TOTAL_AMOUNT, FIELD_CREATED_AT);

	@Id
	private Long id;
	@Version
	private Long version;
	@Column(nullable = false)
	private Long tenantId;
	private String preOrderNumber;
	private String businessName;
	private String systemSource;
	@Column(nullable = false)
	private Long issuerId;
	private Long customerId;
	private String customerName;
	private String customerEmail;

	@OneToMany(cascade = CascadeType.ALL)
	@JoinColumn(name = "pre_order_id", referencedColumnName = "id")
	private List<OrderEntity> subOrders;

	@Column(nullable = false)
	@Enumerated(EnumType.STRING)
	private PreOrderStatus status;

	@Column(nullable = false, columnDefinition = "numeric(38,0) default 0")
	private BigInteger subtotalAmount;
	private BigInteger serviceChargeAmount;

	private String taxName;
	private String taxRegNo;
	private BigDecimal taxRate;
	private Boolean taxInclude;
	private BigInteger taxAmount;

	private BigInteger discountAmount;
	@Column(nullable = false)
	private BigInteger totalAmount;
	@Column(nullable = false)
	private String currencyCode;

	private Long paymentMethodId;
	private String paymentMethodName;
	private String paymentRef;
	private Long paymentTransactionId;
	@Column(nullable = false)
	@Enumerated(EnumType.STRING)
	private OrderPaymentStatus paymentStatus;
	@Column(nullable = false, columnDefinition = "boolean default false")
	private Boolean refundable;
	private String nonce;

	private Instant cancellationDueAt;
	@Enumerated(EnumType.STRING)
	private OrderCancellationType cancellationType;
	private String cancelReason;
	private Instant canceledAt;

	private Instant expiredAt;

	private PreOrderEntity(Builder builder) {
		setDeletedAt(builder.deletedAt);
		setCreatedAt(builder.createdAt);
		setUpdatedAt(builder.updatedAt);
		setId(builder.id);
		setVersion(builder.version);
		setTenantId(builder.tenantId);
		setPreOrderNumber(builder.preOrderNumber);
		setBusinessName(builder.businessName);
		setSystemSource(builder.systemSource);
		setIssuerId(builder.issuerId);
		setCustomerId(builder.customerId);
		setCustomerName(builder.customerName);
		setCustomerEmail(builder.customerEmail);
		setSubOrders(builder.subOrders);
		setStatus(builder.status);
		setSubtotalAmount(builder.subtotalAmount);
		setServiceChargeAmount(builder.serviceChargeAmount);
		setTaxName(builder.taxName);
		setTaxRegNo(builder.taxRegNo);
		setTaxRate(builder.taxRate);
		setTaxInclude(builder.taxInclude);
		setTaxAmount(builder.taxAmount);
		setDiscountAmount(builder.discountAmount);
		setTotalAmount(builder.totalAmount);
		setCurrencyCode(builder.currencyCode);
		setPaymentMethodId(builder.paymentMethodId);
		setPaymentMethodName(builder.paymentMethodName);
		setPaymentRef(builder.paymentRef);
		setPaymentTransactionId(builder.paymentTransactionId);
		setPaymentStatus(builder.paymentStatus);
		setRefundable(builder.refundable);
		setNonce(builder.nonce);
		setCancellationDueAt(builder.cancellationDueAt);
		setCancellationType(builder.cancellationType);
		setCancelReason(builder.cancelReason);
		setCanceledAt(builder.canceledAt);
		setExpiredAt(builder.expiredAt);
	}

	public static Builder builder() {
		return new Builder();
	}

	public static final class Builder {
		private Instant deletedAt;
		private Instant createdAt;
		private Instant updatedAt;
		private Long id;
		private Long version;
		private Long tenantId;
		private String preOrderNumber;
		private String businessName;
		private String systemSource;
		private Long issuerId;
		private Long customerId;
		private String customerName;
		private String customerEmail;
		private List<OrderEntity> subOrders;
		private PreOrderStatus status;
		private BigInteger subtotalAmount;
		private BigInteger serviceChargeAmount;
		private String taxName;
		private String taxRegNo;
		private BigDecimal taxRate;
		private Boolean taxInclude;
		private BigInteger taxAmount;
		private BigInteger discountAmount;
		private BigInteger totalAmount;
		private String currencyCode;
		private Long paymentMethodId;
		private String paymentMethodName;
		private String paymentRef;
		private Long paymentTransactionId;
		private OrderPaymentStatus paymentStatus;
		private Boolean refundable;
		private String nonce;
		private Instant cancellationDueAt;
		private OrderCancellationType cancellationType;
		private String cancelReason;
		private Instant canceledAt;
		private Instant expiredAt;

		private Builder() {
		}

		public Builder deletedAt(Instant deletedAt) {
			this.deletedAt = deletedAt;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public Builder id(Long id) {
			this.id = id;
			return this;
		}

		public Builder version(Long version) {
			this.version = version;
			return this;
		}

		public Builder tenantId(Long tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder preOrderNumber(String preOrderNumber) {
			this.preOrderNumber = preOrderNumber;
			return this;
		}

		public Builder businessName(String businessName) {
			this.businessName = businessName;
			return this;
		}

		public Builder systemSource(String systemSource) {
			this.systemSource = systemSource;
			return this;
		}

		public Builder issuerId(Long issuerId) {
			this.issuerId = issuerId;
			return this;
		}

		public Builder customerId(Long customerId) {
			this.customerId = customerId;
			return this;
		}

		public Builder customerName(String customerName) {
			this.customerName = customerName;
			return this;
		}

		public Builder customerEmail(String customerEmail) {
			this.customerEmail = customerEmail;
			return this;
		}

		public Builder subOrders(List<OrderEntity> subOrders) {
			this.subOrders = subOrders;
			return this;
		}

		public Builder status(PreOrderStatus status) {
			this.status = status;
			return this;
		}

		public Builder subtotalAmount(BigInteger subtotalAmount) {
			this.subtotalAmount = subtotalAmount;
			return this;
		}

		public Builder serviceChargeAmount(BigInteger serviceChargeAmount) {
			this.serviceChargeAmount = serviceChargeAmount;
			return this;
		}

		public Builder taxName(String taxName) {
			this.taxName = taxName;
			return this;
		}

		public Builder taxRegNo(String taxRegNo) {
			this.taxRegNo = taxRegNo;
			return this;
		}

		public Builder taxRate(BigDecimal taxRate) {
			this.taxRate = taxRate;
			return this;
		}

		public Builder taxInclude(Boolean taxInclude) {
			this.taxInclude = taxInclude;
			return this;
		}

		public Builder taxAmount(BigInteger taxAmount) {
			this.taxAmount = taxAmount;
			return this;
		}

		public Builder discountAmount(BigInteger discountAmount) {
			this.discountAmount = discountAmount;
			return this;
		}

		public Builder totalAmount(BigInteger totalAmount) {
			this.totalAmount = totalAmount;
			return this;
		}

		public Builder currencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
			return this;
		}

		public Builder paymentMethodId(Long paymentMethodId) {
			this.paymentMethodId = paymentMethodId;
			return this;
		}

		public Builder paymentMethodName(String paymentMethodName) {
			this.paymentMethodName = paymentMethodName;
			return this;
		}

		public Builder paymentRef(String paymentRef) {
			this.paymentRef = paymentRef;
			return this;
		}

		public Builder paymentTransactionId(Long paymentTransactionId) {
			this.paymentTransactionId = paymentTransactionId;
			return this;
		}

		public Builder paymentStatus(OrderPaymentStatus paymentStatus) {
			this.paymentStatus = paymentStatus;
			return this;
		}

		public Builder refundable(Boolean refundable) {
			this.refundable = refundable;
			return this;
		}

		public Builder nonce(String nonce) {
			this.nonce = nonce;
			return this;
		}

		public Builder cancellationDueAt(Instant cancellationDueAt) {
			this.cancellationDueAt = cancellationDueAt;
			return this;
		}

		public Builder cancellationType(OrderCancellationType cancellationType) {
			this.cancellationType = cancellationType;
			return this;
		}

		public Builder cancelReason(String cancelReason) {
			this.cancelReason = cancelReason;
			return this;
		}

		public Builder canceledAt(Instant canceledAt) {
			this.canceledAt = canceledAt;
			return this;
		}

		public Builder expiredAt(Instant expiredAt) {
			this.expiredAt = expiredAt;
			return this;
		}

		public PreOrderEntity build() {
			return new PreOrderEntity(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;

		if (!(o instanceof PreOrderEntity that))
			return false;

		return new EqualsBuilder().appendSuper(super.equals(o))
				.append(id, that.id)
				.append(version, that.version)
				.append(tenantId, that.tenantId)
				.append(preOrderNumber, that.preOrderNumber)
				.append(businessName, that.businessName)
				.append(systemSource, that.systemSource)
				.append(issuerId, that.issuerId)
				.append(customerId, that.customerId)
				.append(customerName, that.customerName)
				.append(customerEmail, that.customerEmail)
				.append(subOrders, that.subOrders)
				.append(status, that.status)
				.append(subtotalAmount, that.subtotalAmount)
				.append(serviceChargeAmount, that.serviceChargeAmount)
				.append(taxName, that.taxName)
				.append(taxRegNo, that.taxRegNo)
				.append(taxRate, that.taxRate)
				.append(taxInclude, that.taxInclude)
				.append(taxAmount, that.taxAmount)
				.append(discountAmount, that.discountAmount)
				.append(totalAmount, that.totalAmount)
				.append(currencyCode, that.currencyCode)
				.append(paymentMethodId, that.paymentMethodId)
				.append(paymentMethodName, that.paymentMethodName)
				.append(paymentRef, that.paymentRef)
				.append(paymentTransactionId, that.paymentTransactionId)
				.append(paymentStatus, that.paymentStatus)
				.append(refundable, that.refundable)
				.append(nonce, that.nonce)
				.append(cancellationDueAt, that.cancellationDueAt)
				.append(cancellationType, that.cancellationType)
				.append(cancelReason, that.cancelReason)
				.append(canceledAt, that.canceledAt)
				.append(expiredAt, that.expiredAt)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).appendSuper(super.hashCode())
				.append(id)
				.append(version)
				.append(tenantId)
				.append(preOrderNumber)
				.append(businessName)
				.append(systemSource)
				.append(issuerId)
				.append(customerId)
				.append(customerName)
				.append(customerEmail)
				.append(subOrders)
				.append(status)
				.append(subtotalAmount)
				.append(serviceChargeAmount)
				.append(taxName)
				.append(taxRegNo)
				.append(taxRate)
				.append(taxInclude)
				.append(taxAmount)
				.append(discountAmount)
				.append(totalAmount)
				.append(currencyCode)
				.append(paymentMethodId)
				.append(paymentMethodName)
				.append(paymentRef)
				.append(paymentTransactionId)
				.append(paymentStatus)
				.append(refundable)
				.append(nonce)
				.append(cancellationDueAt)
				.append(cancellationType)
				.append(cancelReason)
				.append(canceledAt)
				.append(expiredAt)
				.toHashCode();
	}
}
