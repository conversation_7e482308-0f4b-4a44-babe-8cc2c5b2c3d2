/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.scheduling.features.orders;

import com.styl.pacific.order.service.domain.features.order.ports.input.service.OrderReversalService;
import com.styl.pacific.order.service.scheduling.features.orders.config.OrderReservationJobConfig;
import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.LockAssert;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@ConditionalOnProperty(name = "pacific.schedule.order.order-reservation-job.enabled", havingValue = "true")
@DependsOn("schedulingConfiguration")
@RequiredArgsConstructor
@Component
public class OrderReservationJobScheduler {
	private final OrderReservationJobConfig orderReservationJobConfig;
	private final OrderReversalService orderReversalService;

	@PostConstruct
	public void initial() {
		log.info("OrderReservationJobScheduler started");
	}

	@Scheduled(cron = "${pacific.schedule.order.order-reservation-job.cron-expression}")
	@SchedulerLock(name = "Order.OrderReservationJob", lockAtLeastFor = "${pacific.schedule.order.order-reservation-job.lock-at-least-for:PT30S}", lockAtMostFor = "${pacific.schedule.order.order-reservation-job.lock-at-most-for:PT1M}")
	public void scheduleOrderReservationCheckJob() {
		LockAssert.assertLocked();
		Instant now = Instant.now();
		log.info("OrderReservationCheckJob is running at {}", now);
		int orderBatchSize = Optional.ofNullable(orderReservationJobConfig.getOrderBatchSize())
				.orElse(100);
		Instant timeout = now.plus(orderReservationJobConfig.getLockAtMostFor());
		Duration orderReservationTimeout = Optional.ofNullable(orderReservationJobConfig.getOrderReservationTimeout())
				.orElse(Duration.ofMinutes(1));
		try {
			orderReversalService.processExpiredReservation(orderBatchSize, timeout, orderReservationTimeout);
		} finally {
			log.info("OrderReservationCheckJob is finished by {}", Duration.between(now, Instant.now()));
		}

	}
}
