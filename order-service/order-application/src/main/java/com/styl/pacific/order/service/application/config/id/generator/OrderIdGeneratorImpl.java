/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.application.config.id.generator;

import com.styl.pacific.domain.valueobject.OrderId;
import com.styl.pacific.order.service.domain.features.order.valueobjects.OrderIdGenerator;
import com.styl.pacific.utils.snowflake.id.Snowflake;

public class OrderIdGeneratorImpl implements OrderIdGenerator {
	private final Snowflake snowflake;

	public OrderIdGeneratorImpl() {
		this.snowflake = new Snowflake();
	}

	@Override
	public OrderId nextId() {
		return new OrderId(snowflake.nextId());
	}
}
