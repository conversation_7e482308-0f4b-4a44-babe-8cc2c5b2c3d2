/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.menu.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.data.access.jpa.features.menu.entity.MenuEntity;
import com.styl.pacific.order.service.data.access.relations.features.common.mapper.CommonDataAccessMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.menu.entity.Menu;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { MenuItemDataAccessMapper.class, CommonDataAccessMapper.class,
		CommonDataMapper.class, MapstructCommonDomainMapper.class, MapstructCommonMapper.class })
public interface MenuDataAccessMapper {

	MenuDataAccessMapper INSTANCE = Mappers.getMapper(MenuDataAccessMapper.class);

	@Mapping(target = "id", source = "menu.id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", source = "menu.tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "storeId", source = "menu.storeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "deletedAt", ignore = true)
	MenuEntity toEntity(Menu menu);

	@Mapping(target = "id", source = "menu.id", qualifiedByName = "longToMenuId")
	@Mapping(target = "tenantId", source = "menu.tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "storeId", source = "menu.storeId", qualifiedByName = "longToStoreId")
	Menu toModel(MenuEntity menu);

}
