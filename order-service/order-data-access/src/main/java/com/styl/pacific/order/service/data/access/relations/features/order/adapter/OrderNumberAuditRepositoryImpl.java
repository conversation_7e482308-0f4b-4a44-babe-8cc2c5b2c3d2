/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.order.adapter;

import com.styl.pacific.order.service.data.access.jpa.features.order.entity.OrderNumberAuditSequenceEntity;
import com.styl.pacific.order.service.data.access.jpa.features.order.repository.OrderNumberAuditJpaRepository;
import com.styl.pacific.order.service.data.access.relations.features.order.mapper.OrderDataAccessMapper;
import com.styl.pacific.order.service.domain.features.order.entity.OrderNumberAuditSequence;
import com.styl.pacific.order.service.domain.features.order.ports.output.repository.OrderNumberAuditRepository;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderNumberAuditRepositoryImpl implements OrderNumberAuditRepository {
	private final OrderNumberAuditJpaRepository orderNumberAuditJpaRepository;

	@Override
	public Optional<OrderNumberAuditSequence> findById(String id) {
		return orderNumberAuditJpaRepository.findById(id)
				.map(OrderDataAccessMapper.INSTANCE::toModel);
	}

	@Override
	public OrderNumberAuditSequence save(OrderNumberAuditSequence model) {
		OrderNumberAuditSequenceEntity entity = OrderDataAccessMapper.INSTANCE.toEntity(model);
		return OrderDataAccessMapper.INSTANCE.toModel(orderNumberAuditJpaRepository.save(entity));
	}

	@Override
	public OrderNumberAuditSequence update(OrderNumberAuditSequence orderNumberAuditSequence) {
		OrderNumberAuditSequenceEntity entity = OrderDataAccessMapper.INSTANCE.toEntity(orderNumberAuditSequence);
		return OrderDataAccessMapper.INSTANCE.toModel(orderNumberAuditJpaRepository.saveAndFlush(entity));
	}

	@Override
	public Optional<OrderNumberAuditSequence> nextSequence(String id) {
		return orderNumberAuditJpaRepository.nextSequence(id)
				.stream()
				.findFirst()
				.map(OrderDataAccessMapper.INSTANCE::toModel);
	}
}
