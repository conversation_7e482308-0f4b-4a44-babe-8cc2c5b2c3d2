/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.inventory.request;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record UpdateInventoryRequest(@NotNull(message = "tracking must not be null") Boolean tracking,
		@Min(value = 0, message = "quantity must not lower than 0") Long quantity,
		@NotNull(message = "minimumQuantityOrder must not be null") @Min(value = 1, message = "minimumQuantityOrder must not lower than 1") Long minimumQuantityOrder,
		@Min(value = 0, message = "maximumQuantityOrder must not lower than 0") Long maximumQuantityOrder,
		@NotNull(message = "step must not be null") @Min(value = 1, message = "step must not lower than 1") Long step) {
}
