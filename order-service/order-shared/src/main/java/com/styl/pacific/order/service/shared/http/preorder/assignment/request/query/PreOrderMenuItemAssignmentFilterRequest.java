/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.preorder.assignment.request.query;

import com.styl.pacific.common.validator.dateformat.Date;
import com.styl.pacific.order.service.shared.http.preorder.menu.enums.PreOrderMenuItemStatus;
import com.styl.pacific.order.service.shared.http.preorder.menu.enums.PreOrderMenuStatus;
import com.styl.pacific.order.service.shared.http.product.enums.ProductStatus;
import jakarta.validation.constraints.Digits;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record PreOrderMenuItemAssignmentFilterRequest(
		@Digits(integer = 21, fraction = 0, message = "userId must be valid number") String userId,
		@Date(message = "fromDate must be valid date format") String fromDate,
		@Date(message = "toDate must be valid date format") String toDate,
		@Digits(integer = 21, fraction = 0, message = "mealTimeId must be valid number") String mealTimeId,
		String name,
		@Digits(integer = 21, fraction = 0, message = "categoryId must be valid number") String categoryId,
		List<ProductStatus> productStatuses,
		List<PreOrderMenuItemStatus> menuItemStatuses,
		List<PreOrderMenuStatus> menuStatuses) {
}
