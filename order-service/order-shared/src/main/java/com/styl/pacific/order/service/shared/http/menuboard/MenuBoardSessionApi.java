/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.menuboard;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.order.service.shared.http.menuboard.request.ActiveSessionMenuBoardRequest;
import com.styl.pacific.order.service.shared.http.menuboard.request.GenerateActivationCodeRequest;
import com.styl.pacific.order.service.shared.http.menuboard.request.query.PaginationMenuBoardSessionQueryRequest;
import com.styl.pacific.order.service.shared.http.menuboard.response.MenuBoardActivationCodeSessionResponse;
import com.styl.pacific.order.service.shared.http.menuboard.response.MenuBoardSessionDetailResponse;
import com.styl.pacific.order.service.shared.http.menuboard.response.MenuBoardSessionResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.hibernate.validator.constraints.UUID;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 */
public interface MenuBoardSessionApi {
	@PostMapping("/api/order/menu-boards/sessions/verification")
	@ResponseStatus(HttpStatus.CREATED)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.PUBLIC)
	MenuBoardActivationCodeSessionResponse generateActivateSessionCode(
			@RequestBody @Valid GenerateActivationCodeRequest request);

	@PostMapping("/api/order/menu-boards/sessions/verification-check")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.MENU_BOARD_MGMT_AUTHORIZE)
	MenuBoardSessionDetailResponse verifyActivationCodeMenuBoard(
			@RequestBody @Valid ActiveSessionMenuBoardRequest request);

	@PostMapping("/api/order/menu-boards/sessions/{id}/revoke")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.MENU_BOARD_MGMT_REVOKE)
	void revokeSession(
			@PathVariable @NotNull(message = "id must not be null") @UUID(message = "id is invalid") String id);

	@GetMapping("/api/order/menu-boards/sessions/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.PUBLIC)
	MenuBoardSessionDetailResponse findById(
			@PathVariable @NotNull(message = "id must not be null") @UUID(message = "id is invalid") String id);

	@GetMapping("/api/order/menu-boards/sessions")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.MENU_BOARD_MGMT_LIST_SESSIONS)
	Paging<MenuBoardSessionResponse> findAllPaging(
			@SpringQueryMap @Valid PaginationMenuBoardSessionQueryRequest request);

}
