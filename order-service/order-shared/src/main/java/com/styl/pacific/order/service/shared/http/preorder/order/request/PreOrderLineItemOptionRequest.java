/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.preorder.order.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
public class PreOrderLineItemOptionRequest {
	@NotNull(message = "optionId must not be null")
	@Digits(integer = 21, fraction = 0, message = "optionId must be a valid number")
	private String optionId;
	@Size(max = 255, message = "title must be less than or equal to 255 characters")
	@NotBlank(message = "title must not be empty")
	private String title;
	@Valid
	@Size(min = 1, message = "optionItemIds must not be empty")
	@NotNull(message = "optionItemIds must not be null")
	private List<PreOrderLineItemOptionItemRequest> items;

}
