/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.preorder.assignment;

import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.PreOrderMenuAssignmentRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.DeletePreOrderMenuAssignmentQueryRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.PaginationPreOrderMenuAssignmentRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.response.PreOrderMenuAssignmentResponse;
import com.styl.pacific.order.service.shared.http.preorder.assignment.response.PreOrderMenuAssignmentStubResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 */
public interface PreOrderMenuAssignmentApi {
	@GetMapping("/api/order/assignments/menus")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Paging<PreOrderMenuAssignmentResponse> findAllPaging(
			@Valid @SpringQueryMap @ModelAttribute PaginationPreOrderMenuAssignmentRequest request);

	@PutMapping("/api/order/assignments/menus")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRE_ORDER_MENU_MGMT_ASSIGNMENT_ASSIGN)
	Content<PreOrderMenuAssignmentStubResponse> assignGroup(@Valid @RequestBody PreOrderMenuAssignmentRequest request);

	@DeleteMapping("/api/order/assignments/menus")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRE_ORDER_MENU_MGMT_ASSIGNMENT_ASSIGN)
	void delete(@Valid @SpringQueryMap @ModelAttribute DeletePreOrderMenuAssignmentQueryRequest queryRequest);

}
