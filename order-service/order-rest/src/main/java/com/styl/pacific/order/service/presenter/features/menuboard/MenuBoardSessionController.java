/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.presenter.features.menuboard;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.menuboard.dto.MenuBoardSessionDetailDto;
import com.styl.pacific.order.service.domain.features.menuboard.dto.command.GenerateActivationCodeCommand;
import com.styl.pacific.order.service.domain.features.menuboard.dto.query.PaginationMenuBoardSessionQuery;
import com.styl.pacific.order.service.domain.features.menuboard.entity.MenuBoardSession;
import com.styl.pacific.order.service.domain.features.menuboard.ports.input.service.MenuBoardSessionService;
import com.styl.pacific.order.service.domain.features.menuboard.valueobjects.MenuBoardSessionId;
import com.styl.pacific.order.service.presenter.features.menuboard.mapper.MenuBoardRestMapper;
import com.styl.pacific.order.service.shared.http.menuboard.MenuBoardSessionApi;
import com.styl.pacific.order.service.shared.http.menuboard.request.ActiveSessionMenuBoardRequest;
import com.styl.pacific.order.service.shared.http.menuboard.request.GenerateActivationCodeRequest;
import com.styl.pacific.order.service.shared.http.menuboard.request.query.PaginationMenuBoardSessionQueryRequest;
import com.styl.pacific.order.service.shared.http.menuboard.response.MenuBoardActivationCodeSessionResponse;
import com.styl.pacific.order.service.shared.http.menuboard.response.MenuBoardSessionDetailResponse;
import com.styl.pacific.order.service.shared.http.menuboard.response.MenuBoardSessionResponse;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Validated
@RequiredArgsConstructor
public class MenuBoardSessionController implements MenuBoardSessionApi {
	private final MenuBoardSessionService menuBoardSessionService;
	private final RequestContext requestContext;

	@Override
	public MenuBoardActivationCodeSessionResponse generateActivateSessionCode(GenerateActivationCodeRequest request) {
		GenerateActivationCodeCommand command = MenuBoardRestMapper.INSTANCE.toCommand(request);
		return MenuBoardRestMapper.INSTANCE.toResponse(menuBoardSessionService.generateActivateSessionCode(command));
	}

	@Override
	public MenuBoardSessionDetailResponse verifyActivationCodeMenuBoard(ActiveSessionMenuBoardRequest request) {
		MenuBoardSessionDetailDto sessionDetailDto = menuBoardSessionService.verifyActivationCodeMenuBoard(new TenantId(
				requestContext.getTenantId()), MenuBoardRestMapper.INSTANCE.toCommand(request));
		return MenuBoardRestMapper.INSTANCE.toResponse(sessionDetailDto);
	}

	@Override
	public void revokeSession(String id) {
		menuBoardSessionService.revokeSession(new TenantId(requestContext.getTenantId()), new MenuBoardSessionId(UUID
				.fromString(id)));
	}

	@Override
	public MenuBoardSessionDetailResponse findById(String id) {
		MenuBoardSessionDetailDto sessionDetailDto = menuBoardSessionService.findById(Optional.ofNullable(requestContext
				.getTenantId())
				.map(TenantId::new)
				.orElse(null), new MenuBoardSessionId(UUID.fromString(id)));
		return MenuBoardRestMapper.INSTANCE.toResponse(sessionDetailDto);
	}

	@Override
	public Paging<MenuBoardSessionResponse> findAllPaging(PaginationMenuBoardSessionQueryRequest request) {
		PaginationMenuBoardSessionQuery query = MenuBoardRestMapper.INSTANCE.toQuery(request);
		Paging<MenuBoardSession> paging = menuBoardSessionService.findAllPaging(new TenantId(requestContext
				.getTenantId()), query);
		return Paging.<MenuBoardSessionResponse>builder()
				.content(paging.getContent()
						.stream()
						.map(MenuBoardRestMapper.INSTANCE::toResponse)
						.toList())
				.page(paging.getPage())
				.totalElements(paging.getTotalElements())
				.totalPages(paging.getTotalPages())
				.sort(paging.getSort())
				.build();
	}
}
