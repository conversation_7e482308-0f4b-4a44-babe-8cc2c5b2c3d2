/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.acces.dynamodb.features.order.entity;

import java.math.BigInteger;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbImmutable;

@Builder
@AllArgsConstructor
@DynamoDbImmutable(builder = ProductOptionItemDocument.ProductOptionItemDocumentBuilder.class)
@EqualsAndHashCode
public class ProductOptionItemDocument {
	@Getter(onMethod_ = @__({ @DynamoDbAttribute("name") }))
	private final String name;
	@Getter(onMethod_ = @__({ @DynamoDbAttribute("additional_price") }))
	private final BigInteger additionalPrice;
}
