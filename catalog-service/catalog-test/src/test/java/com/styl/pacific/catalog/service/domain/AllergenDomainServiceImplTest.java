/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.styl.pacific.catalog.service.domain.allergen.AllergenServiceImpl;
import com.styl.pacific.catalog.service.domain.allergen.dto.command.CreateAllergenCommand;
import com.styl.pacific.catalog.service.domain.allergen.dto.command.UpdateAllergenCommand;
import com.styl.pacific.catalog.service.domain.allergen.dto.query.AllergenQuery;
import com.styl.pacific.catalog.service.domain.allergen.entity.Allergen;
import com.styl.pacific.catalog.service.domain.allergen.exception.AllergenNotFoundException;
import com.styl.pacific.catalog.service.domain.allergen.id.generator.AllergenIdGenerator;
import com.styl.pacific.catalog.service.domain.allergen.output.repository.AllergenRepository;
import com.styl.pacific.catalog.service.domain.tenant.dto.TenantDto;
import com.styl.pacific.catalog.service.domain.tenant.output.repository.TenantRepository;
import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.AllergenId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class AllergenDomainServiceImplTest {
	private static final Long ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final String NAME = "name";
	private static final String DESCRIPTION = "description";
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();
	@Mock
	private AllergenRepository allergenRepository;
	@Mock
	private TenantRepository tenantRepository;
	@Mock
	private AllergenIdGenerator allergenIdGenerator;
	@InjectMocks
	private AllergenServiceImpl allergenService;

	@Test
	void shouldReturnAllergen_whenGetById() {
		// Arrange
		Allergen allergenMock = getAllergen();
		when(allergenRepository.findById(any(), any())).thenReturn(Optional.of(allergenMock));
		// Act
		Allergen allergen = allergenService.findById(new TenantId(TENANT_ID), new AllergenId(ID));
		// Assert
		verify(allergenRepository, times(1)).findById(any(), eq(new AllergenId(ID)));
		assertEquals(allergenMock, allergen);
	}

	@Test
	void shouldThrowNotFoundException_whenGetById() {
		// Arrange
		String expectMessage = String.format("Allergen %s not found", ID);
		when(allergenRepository.findById(any(), any())).thenReturn(Optional.empty());
		// Act
		AllergenNotFoundException exception = assertThrows(AllergenNotFoundException.class, () -> allergenService
				.findById(new TenantId(TENANT_ID), new AllergenId(ID)));
		// Assert
		assertEquals(expectMessage, exception.getMessage());
	}

	@Test
	void shouldReturnListAllergen_whenGetAll() {
		// Arrange
		Allergen allergenMock = getAllergen();
		List<Allergen> allergensMock = List.of(allergenMock);
		AllergenQuery queryMock = AllergenQuery.builder()
				.build();
		when(allergenRepository.findAll(any(), any())).thenReturn(allergensMock);
		// Act
		Content<Allergen> allergens = allergenService.findAll(new TenantId(TENANT_ID), queryMock);
		// Assert
		verify(allergenRepository, times(1)).findAll(any(), any());
		assertEquals(allergensMock, allergens.getContent());
	}

	@Test
	void shouldReturnAllergen_whenCreate() {
		// Arrange
		TenantDto tenantDtoMock = TenantDto.builder()
				.tenantId(TENANT_ID)
				.build();
		CreateAllergenCommand commandMock = CreateAllergenCommand.builder()
				.name(NAME)
				.description(DESCRIPTION)
				.build();
		Allergen allergenMock = getAllergen();
		Paging<Allergen> pagingMock = Paging.<Allergen>builder()
				.content(List.of())
				.totalPages(1)
				.page(0)
				.build();
		when(tenantRepository.findById(anyLong())).thenReturn(Optional.of(tenantDtoMock));
		when(allergenRepository.create(any())).thenReturn(allergenMock);
		when(allergenRepository.existsByNameExcludingId(any(), any(), any())).thenReturn(false);
		when(allergenIdGenerator.nextId()).thenReturn(new AllergenId(ID));
		// Act
		Allergen allergen = allergenService.create(new TenantId(TENANT_ID), commandMock);
		// Assert
		ArgumentCaptor<Allergen> commandArgumentCaptor = ArgumentCaptor.forClass(Allergen.class);
		verify(allergenRepository, times(1)).create(commandArgumentCaptor.capture());
		Allergen allergenValue = commandArgumentCaptor.getValue();
		assertEquals(ID, allergenValue.getId()
				.getValue());
		assertEquals(commandMock.name(), allergenValue.getName());
		assertEquals(commandMock.description(), allergenValue.getDescription());
		assertEquals(allergenMock, allergen);
	}

	@Test
	void shouldReturnAllergen_whenUpdate() {
		// Arrange
		String name = "newName";
		String description = "newDescription";
		UpdateAllergenCommand commandMock = UpdateAllergenCommand.builder()
				.name(name)
				.description(description)
				.build();
		Allergen allergenMock = getAllergen();
		Paging<Allergen> pagingMock = Paging.<Allergen>builder()
				.content(List.of())
				.totalPages(1)
				.page(0)
				.build();
		when(allergenRepository.findById(any(), any())).thenReturn(Optional.of(allergenMock));
		when(allergenRepository.existsByNameExcludingId(any(), any(), any())).thenReturn(false);
		when(allergenRepository.update(any())).thenReturn(allergenMock);
		// Act
		Allergen allergen = allergenService.update(new TenantId(TENANT_ID), new AllergenId(ID), commandMock);
		// Assert
		ArgumentCaptor<Allergen> commandArgumentCaptor = ArgumentCaptor.forClass(Allergen.class);
		verify(allergenRepository, times(1)).findById(any(), eq(new AllergenId(ID)));
		verify(allergenRepository, times(1)).update(commandArgumentCaptor.capture());
		Allergen allergenValue = commandArgumentCaptor.getValue();
		assertEquals(ID, allergenValue.getId()
				.getValue());
		assertEquals(commandMock.name(), allergenValue.getName());
		assertEquals(commandMock.description(), allergenValue.getDescription());
		assertEquals(allergenMock, allergen);
	}

	@Test
    void shouldInvolve_whenDelete() {
        // Arrange
        when(allergenRepository.existById(any(), any())).thenReturn(true);
        doNothing().when(allergenRepository).deleteById(any(), any());
        // Act
        allergenService.deleteById(new TenantId(TENANT_ID), new AllergenId(ID));
        // Assert
        verify(allergenRepository, times(1)).deleteById(any(), eq(new AllergenId(ID)));
    }

	private Allergen getAllergen() {
		return Allergen.builder()
				.id(new AllergenId(ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.description(DESCRIPTION)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}
}
