/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.rest.controller.category.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.aws.s3.config.PresignerConfiguration;
import com.styl.pacific.aws.s3.mapper.PresignerContextProvider;
import com.styl.pacific.catalog.service.config.S3Configuration;
import com.styl.pacific.catalog.service.domain.category.dto.command.CreateCategoryCommand;
import com.styl.pacific.catalog.service.domain.category.dto.command.UpdateCategoryCommand;
import com.styl.pacific.catalog.service.domain.category.entity.Category;
import com.styl.pacific.catalog.service.presenter.rest.category.mapper.CategoryRestMapper;
import com.styl.pacific.catalog.service.shared.http.category.request.CreateCategoryRequest;
import com.styl.pacific.catalog.service.shared.http.category.request.UpdateCategoryRequest;
import com.styl.pacific.catalog.service.shared.http.category.response.CategoryResponse;
import com.styl.pacific.catalog.service.shared.http.category.response.SubCategoryResponse;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(SpringExtension.class)
@Import({ PresignerConfiguration.class, PresignerContextProvider.class })
@ContextConfiguration(classes = { S3Configuration.class })
public class CategoryRestMapperTest {

	private static final Long CATEGORY_ID = 1L;
	private static final Long SUB_CATEGORY_ID = 2L;
	private static final Long CATEGORY_PARENT_ID = 3L;
	private static final String CATEGORY_NAME = "Category";
	private static final String CATEGORY_DESCRIPTION = "Description";
	private static final String CATEGORY_IMAGE_URL = "bucket:image/category/image";
	private static final Instant CATEGORY_CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant CATEGORY_UPDATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Long TENANT_ID = 1L;

	@Test
	void shouldMapCategoryResponse_whenMapFromModel() {
		// Arrange
		Category categoryMock = getCategoryArrange();
		// Act
		CategoryResponse response = CategoryRestMapper.INSTANCE.toResponse(categoryMock);
		// Assert
		assertEquals(categoryMock.getId()
				.getValue()
				.toString(), response.id());
		assertEquals(categoryMock.getName(), response.name());
		assertEquals(categoryMock.getSubCategories()
				.getFirst()
				.getName(), response.subCategories()
						.getFirst()
						.name());
		assertEquals(categoryMock.getSubCategories()
				.getFirst()
				.getIconPath(), response.subCategories()
						.getFirst()
						.icon()
						.path());
		assertEquals(categoryMock.getSubCategories()
				.getFirst()
				.getDescription(), response.subCategories()
						.getFirst()
						.description());
		assertEquals(categoryMock.getSubCategories()
				.getFirst()
				.getCreatedAt()
				.toEpochMilli(), response.subCategories()
						.getFirst()
						.createdAt());
		assertEquals(categoryMock.getSubCategories()
				.getFirst()
				.getUpdatedAt()
				.toEpochMilli(), response.subCategories()
						.getFirst()
						.updatedAt());
		assertEquals(categoryMock.getTenantId()
				.getValue()
				.toString(), response.tenantId());
		assertEquals(categoryMock.getIconPath(), response.icon()
				.path());
		assertEquals(categoryMock.getDescription(), response.description());
		assertEquals(categoryMock.getCreatedAt()
				.toEpochMilli(), response.createdAt());
		assertEquals(categoryMock.getUpdatedAt()
				.toEpochMilli(), response.updatedAt());
	}

	@Test
	void shouldMapSubCategoryResponse_whenMapFromModel() {
		// Arrange
		Category categoryMock = getCategoryArrange();
		// Act
		SubCategoryResponse response = CategoryRestMapper.INSTANCE.toSubResponse(categoryMock);
		// Assert
		assertEquals(categoryMock.getId()
				.getValue()
				.toString(), response.id());
		assertEquals(categoryMock.getName(), response.name());
		assertEquals(categoryMock.getTenantId()
				.getValue()
				.toString(), response.tenantId());
		assertEquals(categoryMock.getIconPath(), response.icon()
				.path());
		assertEquals(categoryMock.getDescription(), response.description());
		assertEquals(categoryMock.getCreatedAt()
				.toEpochMilli(), response.createdAt());
		assertEquals(categoryMock.getUpdatedAt()
				.toEpochMilli(), response.updatedAt());
	}

	@Test
	void shouldMapCreateCategoryCommand_whenMapFromRequest() {
		// Arrange
		CreateCategoryRequest request = CreateCategoryRequest.builder()
				.name(CATEGORY_NAME)
				.description(CATEGORY_DESCRIPTION)
				.iconPath(CATEGORY_IMAGE_URL)
				.parentId(CATEGORY_PARENT_ID.toString())
				.build();
		// Act
		CreateCategoryCommand command = CategoryRestMapper.INSTANCE.toCommand(request);
		// Assert
		assertEquals(request.name(), command.name());
		assertEquals(request.description(), command.description());
		assertEquals(request.parentId(), command.parentId()
				.toString());
		assertEquals(request.iconPath(), command.iconPath());
	}

	@Test
	void shouldMapUpdateCategoryCommand_whenMapFromRequest() {
		// Arrange
		UpdateCategoryRequest request = new UpdateCategoryRequest(CATEGORY_NAME, CATEGORY_DESCRIPTION,
				CATEGORY_IMAGE_URL, CATEGORY_PARENT_ID.toString());
		// Act
		UpdateCategoryCommand command = CategoryRestMapper.INSTANCE.toCommand(request);
		// Assert
		assertEquals(request.name(), command.name());
		assertEquals(request.description(), command.description());
		assertEquals(request.parentId(), command.parentId()
				.toString());
		assertEquals(request.iconPath(), command.iconPath());
	}

	private Category getCategoryArrange() {
		Category child = Category.builder()

				.id(new CategoryId(SUB_CATEGORY_ID))

				.tenantId(new TenantId(TENANT_ID))

				.name(CATEGORY_NAME)

				.iconPath(CATEGORY_IMAGE_URL)

				.description(CATEGORY_DESCRIPTION)

				.parentId(new CategoryId(CATEGORY_ID))

				.createdAt(CATEGORY_CREATED_AT)

				.updatedAt(CATEGORY_UPDATED_AT)
				.build();

		return Category.builder()

				.id(new CategoryId(CATEGORY_ID))

				.tenantId(new TenantId(TENANT_ID))

				.name(CATEGORY_NAME)

				.iconPath(CATEGORY_IMAGE_URL)

				.description(CATEGORY_DESCRIPTION)

				.parentId(new CategoryId(CATEGORY_PARENT_ID))

				.subCategories(List.of(child))

				.createdAt(CATEGORY_CREATED_AT)

				.updatedAt(CATEGORY_UPDATED_AT)
				.build();
	}
}
