/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.allergen.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.querydsl.core.types.Predicate;
import com.styl.pacific.catalog.service.data.access.relational.allergen.entity.AllergenEntity;
import com.styl.pacific.catalog.service.data.access.relational.allergen.mapper.AllergenDataAccessMapper;
import com.styl.pacific.catalog.service.data.access.relational.allergen.repository.AllergenJpaRepository;
import com.styl.pacific.catalog.service.domain.allergen.dto.query.AllergenQuery;
import com.styl.pacific.catalog.service.domain.allergen.entity.Allergen;
import com.styl.pacific.domain.valueobject.AllergenId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class AllergenRepositoryImplTest {
	private static final Long ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final String NAME = "name";
	private static final String DESCRIPTION = "description";
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();
	@Mock
	private AllergenJpaRepository allergenJpaRepository;
	@InjectMocks
	private AllergenRepositoryImpl allergenRepository;

	@Test
	void shouldReturnAllergen_whenFindById() {
		// Arrange
		AllergenEntity entityMock = getAllergen();
		when(allergenJpaRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(entityMock));
		// Act
		Optional<Allergen> allergenOptional = allergenRepository.findById(new TenantId(TENANT_ID), new AllergenId(ID));
		// Assert
		assertTrue(allergenOptional.isPresent());
		Allergen allergen = allergenOptional.get();
		verify(allergenJpaRepository, times(1)).findOne(any(Predicate.class));
		assertEqualsEntityAndModel(entityMock, allergen);
	}

	@Test
	void shouldReturnAllergen_whenSave() {
		// Arrange
		Allergen allergenMock = getAllergenModel();
		AllergenEntity entityMock = getAllergen();
		when(allergenJpaRepository.save(any())).thenReturn(entityMock);
		// Act
		Allergen allergen = allergenRepository.create(allergenMock);
		// Assert
		assertEquals(allergenMock, allergen);
		ArgumentCaptor<AllergenEntity> allergenEntityArgumentCaptor = ArgumentCaptor.forClass(AllergenEntity.class);
		verify(allergenJpaRepository, times(1)).save(allergenEntityArgumentCaptor.capture());
		AllergenEntity allergenEntity = allergenEntityArgumentCaptor.getValue();
		assertEquals(AllergenDataAccessMapper.INSTANCE.allergenToEntity(allergenMock), allergenEntity);
	}

	@Test
	void shouldReturnAllergen_whenUpdate() {
		// Arrange
		Allergen allergenMock = getAllergenModel();
		AllergenEntity entityMock = getAllergen();
		when(allergenJpaRepository.saveAndFlush(any())).thenReturn(entityMock);
		// Act
		Allergen allergen = allergenRepository.update(allergenMock);
		// Assert
		assertEquals(allergenMock, allergen);
		ArgumentCaptor<AllergenEntity> allergenEntityArgumentCaptor = ArgumentCaptor.forClass(AllergenEntity.class);
		verify(allergenJpaRepository, times(1)).saveAndFlush(allergenEntityArgumentCaptor.capture());
		AllergenEntity allergenEntity = allergenEntityArgumentCaptor.getValue();
		assertEquals(AllergenDataAccessMapper.INSTANCE.allergenToEntity(allergenMock), allergenEntity);
	}

	@Test
	void shouldReturnListAllergen_whenFindAll() {
		// Arrange
		AllergenEntity entityMock = getAllergen();
		List<AllergenEntity> entityListMock = List.of(entityMock);
		AllergenQuery queryMock = AllergenQuery.builder()
				.build();
		when(allergenJpaRepository.findBy(any(Predicate.class), any())).thenReturn(entityListMock);
		// Act
		List<Allergen> allergens = allergenRepository.findAll(new TenantId(TENANT_ID), queryMock);
		// Assert
		verify(allergenJpaRepository, times(1)).findBy(any(Predicate.class), any());
		assertEquals(entityListMock.size(), allergens.size());
	}

	@Test
	void shouldRemove_whenDelete() {
		// Arrange
		AllergenEntity entityMock = getAllergen();
		when(allergenJpaRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(entityMock));
		// Act
		allergenRepository.deleteById(new TenantId(TENANT_ID), new AllergenId(ID));
		// Assert
		verify(allergenJpaRepository, times(1)).findOne(any(Predicate.class));
		verify(allergenJpaRepository, times(1)).delete(any(AllergenEntity.class));
	}

	private void assertEqualsEntityAndModel(AllergenEntity entity, Allergen allergen) {
		assertEquals(entity.getId(), allergen.getId()
				.getValue());
		assertEquals(entity.getTenantId(), allergen.getTenantId()
				.getValue());
		assertEquals(entity.getName(), allergen.getName());
		assertEquals(entity.getDescription(), allergen.getDescription());
		assertEquals(entity.getCreatedAt(), allergen.getCreatedAt());
		assertEquals(entity.getUpdatedAt(), allergen.getUpdatedAt());
	}

	private AllergenEntity getAllergen() {
		return AllergenEntity.builder()
				.id(ID)
				.tenantId(TENANT_ID)
				.name(NAME)
				.description(DESCRIPTION)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}

	private Allergen getAllergenModel() {
		return Allergen.builder()
				.id(new AllergenId(ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.description(DESCRIPTION)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}
}
