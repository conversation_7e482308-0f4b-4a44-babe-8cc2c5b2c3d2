/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.rest.integration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.catalog.service.config.IntegrationTestConfiguration;
import com.styl.pacific.catalog.service.domain.product.dto.command.option.CreateProductOptionCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.option.CreateProductOptionItemCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.option.UpdateProductOptionCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.option.UpdateProductOptionItemCommand;
import com.styl.pacific.catalog.service.features.tenant.TenantCreatedEventKafkaConsumer;
import com.styl.pacific.catalog.service.shared.http.product.response.option.ListProductOptionResponse;
import com.styl.pacific.catalog.service.shared.http.product.response.option.ProductOptionResponse;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import java.math.BigInteger;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.atomic.AtomicReference;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.reactive.server.WebTestClient;

/**
 * <AUTHOR>
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
class ProductOptionControllerIntegrationTest extends BaseWebClientWithDbTest {

	// Path
	private static final String PRODUCT_OPTION_PATH = "/api/catalog/products/{productId}/options";

	private static final Long OPTION_ID = 1L;
	private static final Long PRODUCT_ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final String OPTION_TITLE = "option 1";
	private static final Integer OPTION_MINIMUM = 0;
	private static final Integer OPTION_MAXIMUM = 100;
	private static final Instant OPTION_CREATED_AT;
	private static final Instant OPTION_UPDATED_AT;

	private static final Long OPTION_ITEM_ID = 1L;
	private static final String OPTION_ITEM_NAME = "Item 1";
	private static final BigInteger OPTION_ITEM_ADDITION_PRICE = BigInteger.valueOf(1000);

	private static final AtomicReference<String> productOptionId = new AtomicReference<>();

	static {
		DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
		OPTION_CREATED_AT = OffsetDateTime.parse("2024-05-13T15:05:46.685Z", formatter)
				.toInstant();
		OPTION_UPDATED_AT = OffsetDateTime.parse("2024-05-14T09:56:00.130Z", formatter)
				.toInstant();
	}

	@MockitoBean
	private TenantCreatedEventKafkaConsumer tenantCreatedEventKafkaConsumer;

	private void setHeaders(HttpHeaders headers) {
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(TENANT_ID));
	}

	@Order(1)
	@Test
	void shouldReturn200WithListProductOptionsResponse_whenFindAll() {
		// Arrange
		Map<String, Object> params = new HashMap<>();
		params.put("title", OPTION_TITLE);
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(PRODUCT_ID));
		int sizeExpect = 1;
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.get()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_OPTION_PATH);
					params.forEach(uriBuilder::queryParam);
					return uriBuilder.build(uriVariables);
				})
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec

				.expectStatus()
				.isOk()

				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)

				.expectBody(ListProductOptionResponse.class)
				.consumeWith(result -> {
					ListProductOptionResponse body = result.getResponseBody();
					assert body != null;
					assertEquals(sizeExpect, body.content()
							.size());
				});
	}

	@Order(2)
	@Test
	void shouldReturn200WithProductOptionResponse_whenGetById() {
		// Arrange
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(PRODUCT_ID));
		uriVariables.put("optionId", Long.toString(OPTION_ID));
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.get()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_OPTION_PATH);
					return uriBuilder.pathSegment("{optionId}")
							.build(uriVariables);
				})
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec

				.expectStatus()
				.isOk()

				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)

				.expectBody(ProductOptionResponse.class)
				.consumeWith(result -> {
					ProductOptionResponse body = result.getResponseBody();
					assert body != null;
					assertEquals(OPTION_ID.toString(), body.id());
					assertEquals(PRODUCT_ID.toString(), body.productId());
					assertEquals(OPTION_TITLE, body.title());
					assertEquals(OPTION_MAXIMUM, body.maximum());
					assertEquals(OPTION_MINIMUM, body.minimum());
					assertEquals(1, body.items()
							.size());
					assertEquals(OPTION_ITEM_ID.toString(), body.items()
							.getFirst()
							.id());
					assertEquals(OPTION_ITEM_NAME, body.items()
							.getFirst()
							.name());
					assertThat(body.items()
							.getFirst()
							.additionPrice()).isEqualByComparingTo(OPTION_ITEM_ADDITION_PRICE);
					assertEquals(OPTION_CREATED_AT.toEpochMilli(), body.createdAt());
					assertEquals(OPTION_UPDATED_AT.toEpochMilli(), body.updatedAt());
				});

	}

	@Order(3)
	@Test
	void shouldReturn201WithProductOptionResponse_whenCreate() {
		// Arrange
		CreateProductOptionItemCommand commandItemMock = CreateProductOptionItemCommand.builder()
				.name(OPTION_ITEM_NAME)
				.additionPrice(OPTION_ITEM_ADDITION_PRICE)
				.build();
		CreateProductOptionCommand commandMock = CreateProductOptionCommand.builder()
				.title(OPTION_TITLE)
				.minimum(OPTION_MINIMUM)
				.maximum(OPTION_MAXIMUM)
				.items(List.of(commandItemMock))
				.build();

		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(PRODUCT_ID));

		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_OPTION_PATH);
					return uriBuilder.build(uriVariables);
				})
				.headers(this::setHeaders)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(commandMock)
				.exchange();
		// Assert
		responseSpec

				.expectStatus()
				.isCreated()

				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)

				.expectBody(ProductOptionResponse.class)
				.consumeWith(result -> {
					ProductOptionResponse body = result.getResponseBody();
					assert body != null;
					productOptionId.set(body.id());
					assertEquals(commandMock.getTitle(), body.title());
					assertEquals(commandMock.getMinimum(), body.minimum());
					assertEquals(commandMock.getMaximum(), body.maximum());
					assertEquals(1, body.items()
							.size());
					assertEquals(commandItemMock.getName(), body.items()
							.getFirst()
							.name());
					assertThat(body.items()
							.getFirst()
							.additionPrice()).isEqualByComparingTo(commandItemMock.getAdditionPrice());
				});
	}

	@Test
	@Order(4)
	void shouldReturn200WithProductOptionResponse_whenUpdate() {
		// Arrange
		String newOptionTitle = "New title";
		int newMinimum = 0;
		int newMaximum = 10;

		String newItemName = "New name";
		BigInteger newAdditionPrice = BigInteger.valueOf(50);
		UpdateProductOptionItemCommand commandItemMock = UpdateProductOptionItemCommand.builder()
				.name(newItemName)
				.additionPrice(newAdditionPrice)
				.build();
		UpdateProductOptionCommand commandMock = UpdateProductOptionCommand.builder()
				.title(newOptionTitle)
				.minimum(newMinimum)
				.maximum(newMaximum)
				.items(List.of(commandItemMock))
				.build();

		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("productId", PRODUCT_ID);
		uriVariables.put("optionId", productOptionId.get());
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.put()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_OPTION_PATH);
					return uriBuilder.pathSegment("{optionId}")
							.build(uriVariables);
				})
				.headers(this::setHeaders)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(commandMock)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isOk()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectBody(ProductOptionResponse.class)
				.consumeWith(result -> {
					ProductOptionResponse body = result.getResponseBody();
					assert body != null;
					assertEquals(commandMock.getTitle(), body.title());
					assertEquals(commandMock.getMinimum(), body.minimum());
					assertEquals(commandMock.getMaximum(), body.maximum());
					assertEquals(1, body.items()
							.size());
					assertEquals(commandItemMock.getName(), body.items()
							.getFirst()
							.name());
					assertThat(body.items()
							.getFirst()
							.additionPrice()).isEqualByComparingTo(commandItemMock.getAdditionPrice());
				});
	}

	@Test
	@Order(5)
	void shouldReturn204_whenDelete() {
		// Arrange
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(PRODUCT_ID));
		uriVariables.put("optionId", productOptionId.get());
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.delete()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_OPTION_PATH);
					return uriBuilder.pathSegment("{optionId}")
							.build(uriVariables);
				})
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isNoContent();
	}
}
