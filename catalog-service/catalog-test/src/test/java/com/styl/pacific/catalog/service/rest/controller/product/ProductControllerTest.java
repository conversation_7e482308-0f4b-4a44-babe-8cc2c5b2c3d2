/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.rest.controller.product;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.aws.s3.config.PresignerConfiguration;
import com.styl.pacific.aws.s3.config.S3ConfigProperties;
import com.styl.pacific.aws.s3.mapper.PresignerContextProvider;
import com.styl.pacific.catalog.service.config.MvcTestConfiguration;
import com.styl.pacific.catalog.service.domain.category.dto.CategoryStubDto;
import com.styl.pacific.catalog.service.domain.common.exception.CatalogDomainException;
import com.styl.pacific.catalog.service.domain.healthierchoice.entity.HealthierChoice;
import com.styl.pacific.catalog.service.domain.product.dto.ProductDto;
import com.styl.pacific.catalog.service.domain.product.dto.command.CreateProductCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.UpdateProductCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.image.CreateProductImageCommand;
import com.styl.pacific.catalog.service.domain.product.dto.query.PaginationProductQuery;
import com.styl.pacific.catalog.service.domain.product.entity.ProductImage;
import com.styl.pacific.catalog.service.domain.product.enums.ProductStatus;
import com.styl.pacific.catalog.service.domain.product.exception.ProductNotFoundException;
import com.styl.pacific.catalog.service.domain.product.port.input.service.ProductAllergenService;
import com.styl.pacific.catalog.service.domain.product.port.input.service.ProductNutritionService;
import com.styl.pacific.catalog.service.domain.product.port.input.service.ProductService;
import com.styl.pacific.catalog.service.presenter.exception.handler.CatalogExceptionHandler;
import com.styl.pacific.catalog.service.presenter.rest.product.ProductController;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.ProductType;
import com.styl.pacific.domain.exception.GlobalErrorCode;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.ProductImageId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.math.BigInteger;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * <AUTHOR>
 */
@WebMvcTest(controllers = ProductController.class)
@Import({ ProductController.class, CatalogExceptionHandler.class })
@ContextConfiguration(classes = { MvcTestConfiguration.class, PresignerConfiguration.class, S3ConfigProperties.class,
		PresignerContextProvider.class })
class ProductControllerTest {
	private static final String REQUEST_ID_HEADER = "X-Request-ID";
	private static final String TENANT_ID_HEADER = "X-Tenant-ID";

	private static final String PRODUCT_URL = "/api/catalog/products";

	private static final Long ID = 1L;
	private static final Long CATEGORY_ID = 1L;
	private static final Long HEALTHIER_CHOICE_ID = 1L;
	private static final Long STORE_ID = 1L;
	private static final Long TENANT_ID = 1L;
	private static final String NAME = "productName";
	private static final String SKU = "44444444";
	private static final String BRIEF_DESCRIPTION = "briefDescription";
	private static final String DESCRIPTION = "description";
	private static final String INGREDIENTS = "ingredients";
	private static final String BARCODE = "233433335555";
	private static final ProductStatus STATUS = ProductStatus.ACTIVE;
	private static final Money UNIT_PRICE = Money.ZERO;
	private static final Boolean OPEN_PRICE = false;
	private static final Money TO_UNIT_PRICE = new Money(BigInteger.valueOf(100));

	private static final Long IMAGE_ID = 1L;
	private static final String IMAGE_PATH = "bucket:image.jpg";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private ObjectMapper objectMapper;

	@MockitoBean
	private ProductService productDomainService;

	@MockitoBean
	private ProductAllergenService productAllergenService;

	@MockitoBean
	private ProductNutritionService productNutritionService;

	@Test
	void shouldReturn200WithListProductsResponse_whenFindProductsQuery() throws Exception {
		long totalElements = 10;
		long totalNumberOfElements = 3;
		int totalPages = 5;

		int page = 0;
		int pageSize = 10;

		List<String> sort = Collections.emptyList();

		String sortField = "field";
		String sortDirection = "ASC";

		ProductDto res1 = getProductDto();
		ProductDto res2 = getProductDto();
		ProductDto res3 = getProductDto();
		Paging<ProductDto> response = new Paging<>(List.of(res1, res2, res3), totalElements, totalPages, page, sort);

		when(productDomainService.findAllPaging(any(), any(PaginationProductQuery.class))).thenReturn(response);

		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.queryParam("filter.ids", Stream.of(ID, ID)
						.map(Object::toString)
						.collect(Collectors.joining(",")))
				.queryParam("filter.name", NAME)
				.queryParam("filter.healthierChoiceIds", HEALTHIER_CHOICE_ID)
				.queryParam("filter.categoryIds", CATEGORY_ID)
				.queryParam("filter.storeId", STORE_ID)
				.queryParam("filter.barcode", BARCODE)
				.queryParam("filter.sku", SKU)
				.queryParam("filter.statuses", STATUS)
				.queryParam("filter.fromUnitPrice", UNIT_PRICE.getAmount())
				.queryParam("filter.toUnitPrice", TO_UNIT_PRICE.getAmount())
				.queryParam("page", page)
				.queryParam("size", pageSize)
				.queryParam("sortField", sortField)
				.queryParam("sortDirection", sortDirection)
				.toUriString();

		// Act
		ResultActions resultActions = mockMvc.perform(get(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		resultActions.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.content").isArray())
				.andExpect(jsonPath("$.content.length()").value(totalNumberOfElements))
				.andExpect(jsonPath("$.totalElements").value(totalElements))
				.andExpect(jsonPath("$.totalPages").value(totalPages))
				.andExpect(jsonPath("$.page").value(page))
				.andExpect(jsonPath("$.sort").isArray());

		ArgumentCaptor<PaginationProductQuery> captor = ArgumentCaptor.forClass(PaginationProductQuery.class);
		verify(productDomainService, times(1)).findAllPaging(any(), captor.capture());
		PaginationProductQuery queryCaptor = captor.getValue();
		assertTrue(List.of(ID, ID)
				.containsAll(queryCaptor.getFilter()
						.ids()));
		assertEquals(NAME, queryCaptor.getFilter()
				.name());
		assertEquals(List.of(CATEGORY_ID), queryCaptor.getFilter()
				.categoryIds());
		assertEquals(List.of(HEALTHIER_CHOICE_ID), queryCaptor.getFilter()
				.healthierChoiceIds());
		assertEquals(STORE_ID, queryCaptor.getFilter()
				.storeId());
		assertEquals(BARCODE, queryCaptor.getFilter()
				.barcode());
		assertEquals(SKU, queryCaptor.getFilter()
				.sku());
		assertEquals(List.of(STATUS), queryCaptor.getFilter()
				.statuses());
		assertEquals(UNIT_PRICE.getAmount(), queryCaptor.getFilter()
				.fromUnitPrice());
		assertEquals(TO_UNIT_PRICE.getAmount(), queryCaptor.getFilter()
				.toUnitPrice());
	}

	@Test
	void shouldReturn200WithProductResponse_whenGetProductById() throws Exception {
		// Arrange
		ProductDto res = getProductDto();
		when(productDomainService.findById(any(), any())).thenReturn(res);
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}")
				.buildAndExpand(uriVariables)
				.toUriString();
		// Act
		ResultActions resultActions = mockMvc.perform(get(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(productDomainService, times(1)).findById(any(), any());

		resultActions.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.id").value(ID))
				.andExpect(jsonPath("$.category.id").value(CATEGORY_ID))
				.andExpect(jsonPath("$.healthierChoice.id").value(HEALTHIER_CHOICE_ID))
				.andExpect(jsonPath("$.storeId").value(STORE_ID))
				.andExpect(jsonPath("$.tenantId").value(TENANT_ID))
				.andExpect(jsonPath("$.name").value(NAME))
				.andExpect(jsonPath("$.sku").value(SKU))
				.andExpect(jsonPath("$.briefInformation").value(BRIEF_DESCRIPTION))
				.andExpect(jsonPath("$.description").value(DESCRIPTION))
				.andExpect(jsonPath("$.ingredients").value(INGREDIENTS))
				.andExpect(jsonPath("$.barcode").value(BARCODE))
				.andExpect(jsonPath("$.status").value(STATUS.name()))
				.andExpect(jsonPath("$.unitPrice").value(UNIT_PRICE.getAmount()))
				.andExpect(jsonPath("$.images.length()").value(1));
	}

	@Test
	void shouldReturn404_whenGetProductById() throws Exception {
		// Arrange
		String exceptionMessage = "Not found";
		when(productDomainService.findById(any(), any())).thenThrow(new ProductNotFoundException(exceptionMessage));
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}")
				.buildAndExpand(uriVariables)
				.toUriString();
		// Act
		ResultActions resultActions = mockMvc.perform(get(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(productDomainService, times(1)).findById(any(), any());

		resultActions.andExpect(status().isBadRequest())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.code").value(GlobalErrorCode.PRODUCT_NOT_FOUND.getValue()))
				.andExpect(jsonPath("$.message").value(GlobalErrorCode.PRODUCT_NOT_FOUND.getMessage()));
	}

	@Test
	void shouldReturn400_whenGetProductById() throws Exception {
		// Arrange
		String exceptionMessage = "Bad request";
		when(productDomainService.findById(any(), any())).thenThrow(new CatalogDomainException(exceptionMessage));
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}")
				.buildAndExpand(uriVariables)
				.toUriString();
		// Act
		ResultActions resultActions = mockMvc.perform(get(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(productDomainService, times(1)).findById(any(), eq(new ProductId(ID)));

		resultActions.andExpect(status().isBadRequest())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.code").value(GlobalErrorCode.CATALOG_SERVICE_GENERIC_ERROR.getValue()))
				.andExpect(jsonPath("$.message").value(GlobalErrorCode.CATALOG_SERVICE_GENERIC_ERROR.getMessage()));
	}

	@Test
	void shouldReturn201WithProductResponse_whenCreateProduct() throws Exception {
		// Arrange
		CreateProductImageCommand image = CreateProductImageCommand.builder()
				.imagePath(IMAGE_PATH)
				.build();

		CreateProductCommand command = CreateProductCommand.builder()
				.categoryId(CATEGORY_ID)
				.productType(ProductType.STORE_PRODUCT)
				.storeId(STORE_ID)
				.name(NAME)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.unitPrice(UNIT_PRICE.getAmount())
				.openPrice(OPEN_PRICE)
				.preparationTime(1)
				.images(List.of(image))
				.sku(SKU)
				.barcode(BARCODE)
				.build();

		when(productDomainService.create(any(), any(CreateProductCommand.class))).then(invocation -> {
			TenantId tenantIdInput = invocation.getArgument(0);
			CreateProductCommand commandInput = invocation.getArgument(1);
			return ProductDto.builder()
					.id(new ProductId(ID))
					.storeId(new StoreId(commandInput.getStoreId()))
					.tenantId(tenantIdInput)
					.name(commandInput.getName())
					.sku(commandInput.getSku())
					.barcode(commandInput.getBarcode())
					.openPrice(commandInput.getOpenPrice())
					.build();

		});

		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.toUriString();

		// Act
		ResultActions resultActions = mockMvc.perform(post(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(command)));
		// Assert
		ArgumentCaptor<CreateProductCommand> captor = ArgumentCaptor.forClass(CreateProductCommand.class);
		verify(productDomainService, times(1)).create(any(), captor.capture());
		CreateProductCommand commandInput = captor.getValue();
		assertEquals(NAME, commandInput.getName());
		assertEquals(CATEGORY_ID, commandInput.getCategoryId());
		assertEquals(STORE_ID, commandInput.getStoreId());
		assertEquals(BARCODE, commandInput.getBarcode());
		assertEquals(SKU, commandInput.getSku());
		assertEquals(BRIEF_DESCRIPTION, commandInput.getBriefInformation());
		assertEquals(DESCRIPTION, commandInput.getDescription());
		assertEquals(UNIT_PRICE.getAmount(), commandInput.getUnitPrice());
		assertEquals(OPEN_PRICE, commandInput.getOpenPrice());
		assertEquals(1, commandInput.getImages()
				.size());
		assertEquals(IMAGE_PATH, commandInput.getImages()
				.getFirst()
				.getImagePath());

		resultActions.andExpect(status().isCreated())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.id").value(ID))
				.andExpect(jsonPath("$.storeId").value(STORE_ID))
				.andExpect(jsonPath("$.tenantId").value(TENANT_ID))
				.andExpect(jsonPath("$.name").value(NAME))
				.andExpect(jsonPath("$.sku").value(SKU))
				.andExpect(jsonPath("$.barcode").value(BARCODE));
	}

	@Test
	void shouldReturn400_whenCreateProduct() throws Exception {
		// Arrange
		String exceptionMessage = "bad request";
		CreateProductImageCommand image = CreateProductImageCommand.builder()
				.imagePath(IMAGE_PATH)
				.build();

		CreateProductCommand command = CreateProductCommand.builder()
				.categoryId(CATEGORY_ID)
				.productType(ProductType.STORE_PRODUCT)
				.storeId(STORE_ID)
				.name(NAME)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.unitPrice(UNIT_PRICE.getAmount())
				.openPrice(OPEN_PRICE)
				.preparationTime(1)
				.images(List.of(image))
				.sku(SKU)
				.barcode(BARCODE)
				.build();

		when(productDomainService.create(any(), any(CreateProductCommand.class))).thenThrow(new CatalogDomainException(
				exceptionMessage));

		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.toUriString();

		// Act
		ResultActions resultActions = mockMvc.perform(post(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(command)));
		// Assert
		ArgumentCaptor<CreateProductCommand> captor = ArgumentCaptor.forClass(CreateProductCommand.class);
		verify(productDomainService, times(1)).create(any(), captor.capture());
		CreateProductCommand commandInput = captor.getValue();
		assertEquals(NAME, commandInput.getName());
		assertEquals(CATEGORY_ID, commandInput.getCategoryId());
		assertEquals(STORE_ID, commandInput.getStoreId());
		assertEquals(BARCODE, commandInput.getBarcode());
		assertEquals(SKU, commandInput.getSku());
		assertEquals(BRIEF_DESCRIPTION, commandInput.getBriefInformation());
		assertEquals(DESCRIPTION, commandInput.getDescription());
		assertEquals(UNIT_PRICE.getAmount(), commandInput.getUnitPrice());
		assertEquals(OPEN_PRICE, commandInput.getOpenPrice());
		assertEquals(1, commandInput.getImages()
				.size());
		assertEquals(IMAGE_PATH, commandInput.getImages()
				.getFirst()
				.getImagePath());

		resultActions.andExpect(status().isBadRequest())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.code").value(GlobalErrorCode.CATALOG_SERVICE_GENERIC_ERROR.getValue()))
				.andExpect(jsonPath("$.message").value(GlobalErrorCode.CATALOG_SERVICE_GENERIC_ERROR.getMessage()));
	}

	@Test
	void shouldReturn200WithProductResponse_whenUpdateProduct() throws Exception {
		// Arrange
		UpdateProductCommand command = UpdateProductCommand.builder()
				.categoryId(CATEGORY_ID)
				.healthierChoiceId(HEALTHIER_CHOICE_ID)
				.name(NAME)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.unitPrice(UNIT_PRICE.getAmount())
				.openPrice(OPEN_PRICE)
				.sku(SKU)
				.barcode(BARCODE)
				.build();
		ProductDto productDtoMock = getProductDto();
		when(productDomainService.update(any(), any(), any(UpdateProductCommand.class))).thenReturn(productDtoMock);

		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}")
				.buildAndExpand(uriVariables)
				.toUriString();

		// Act
		ResultActions resultActions = mockMvc.perform(put(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(command)));
		// Assert

		ArgumentCaptor<UpdateProductCommand> captor = ArgumentCaptor.forClass(UpdateProductCommand.class);
		verify(productDomainService, times(1)).update(any(), eq(new ProductId(ID)), captor.capture());
		UpdateProductCommand commandInput = captor.getValue();
		assertEquals(NAME, commandInput.getName());
		assertEquals(CATEGORY_ID, commandInput.getCategoryId());
		assertEquals(BARCODE, commandInput.getBarcode());
		assertEquals(SKU, commandInput.getSku());
		assertEquals(BRIEF_DESCRIPTION, commandInput.getBriefInformation());
		assertEquals(DESCRIPTION, commandInput.getDescription());
		assertEquals(INGREDIENTS, commandInput.getIngredients());
		assertEquals(UNIT_PRICE.getAmount(), commandInput.getUnitPrice());
		assertEquals(OPEN_PRICE, commandInput.getOpenPrice());

		resultActions.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.id").value(ID))
				.andExpect(jsonPath("$.category.id").value(CATEGORY_ID))
				.andExpect(jsonPath("$.healthierChoice.id").value(HEALTHIER_CHOICE_ID))
				.andExpect(jsonPath("$.tenantId").value(TENANT_ID))
				.andExpect(jsonPath("$.name").value(NAME))
				.andExpect(jsonPath("$.sku").value(SKU))
				.andExpect(jsonPath("$.briefInformation").value(BRIEF_DESCRIPTION))
				.andExpect(jsonPath("$.description").value(DESCRIPTION))
				.andExpect(jsonPath("$.ingredients").value(INGREDIENTS))
				.andExpect(jsonPath("$.barcode").value(BARCODE))
				.andExpect(jsonPath("$.status").value(STATUS.name()))
				.andExpect(jsonPath("$.unitPrice").value(UNIT_PRICE.getAmount()))
				.andExpect(jsonPath("$.openPrice").value(OPEN_PRICE));
	}

	@Test
	void shouldReturn404_whenUpdateProduct() throws Exception {
		// Arrange
		String exceptionMessage = "not found";
		UpdateProductCommand command = UpdateProductCommand.builder()
				.categoryId(CATEGORY_ID)
				.name(NAME)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.unitPrice(UNIT_PRICE.getAmount())
				.openPrice(OPEN_PRICE)
				.sku(SKU)
				.barcode(BARCODE)
				.build();

		when(productDomainService.update(any(), any(), any(UpdateProductCommand.class))).thenThrow(
				new ProductNotFoundException(exceptionMessage));

		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}")
				.buildAndExpand(uriVariables)
				.toUriString();

		// Act
		ResultActions resultActions = mockMvc.perform(put(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(command)));
		// Assert

		ArgumentCaptor<UpdateProductCommand> captor = ArgumentCaptor.forClass(UpdateProductCommand.class);
		verify(productDomainService, times(1)).update(any(), eq(new ProductId(ID)), captor.capture());
		UpdateProductCommand commandInput = captor.getValue();
		assertEquals(NAME, commandInput.getName());
		assertEquals(CATEGORY_ID, commandInput.getCategoryId());
		assertEquals(BARCODE, commandInput.getBarcode());
		assertEquals(SKU, commandInput.getSku());
		assertEquals(BRIEF_DESCRIPTION, commandInput.getBriefInformation());
		assertEquals(DESCRIPTION, commandInput.getDescription());
		assertEquals(INGREDIENTS, commandInput.getIngredients());
		assertEquals(UNIT_PRICE.getAmount(), commandInput.getUnitPrice());
		assertEquals(OPEN_PRICE, commandInput.getOpenPrice());

		resultActions.andExpect(status().isBadRequest())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.code").value(GlobalErrorCode.PRODUCT_NOT_FOUND.getValue()))
				.andExpect(jsonPath("$.message").value(GlobalErrorCode.PRODUCT_NOT_FOUND.getMessage()));
	}

	@Test
	void shouldReturn400_whenUpdateProduct() throws Exception {
		// Arrange
		String exceptionMessage = "bad request";
		UpdateProductCommand command = UpdateProductCommand.builder()
				.categoryId(CATEGORY_ID)
				.name(NAME)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.unitPrice(UNIT_PRICE.getAmount())
				.openPrice(OPEN_PRICE)
				.sku(SKU)
				.barcode(BARCODE)
				.build();

		when(productDomainService.update(any(), any(), any(UpdateProductCommand.class))).thenThrow(
				new CatalogDomainException(exceptionMessage));

		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}")
				.buildAndExpand(uriVariables)
				.toUriString();

		// Act
		ResultActions resultActions = mockMvc.perform(put(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(command)));
		// Assert

		ArgumentCaptor<UpdateProductCommand> captor = ArgumentCaptor.forClass(UpdateProductCommand.class);
		verify(productDomainService, times(1)).update(any(), eq(new ProductId(ID)), captor.capture());
		UpdateProductCommand commandInput = captor.getValue();
		assertEquals(NAME, commandInput.getName());
		assertEquals(CATEGORY_ID, commandInput.getCategoryId());
		assertEquals(BARCODE, commandInput.getBarcode());
		assertEquals(SKU, commandInput.getSku());
		assertEquals(BRIEF_DESCRIPTION, commandInput.getBriefInformation());
		assertEquals(DESCRIPTION, commandInput.getDescription());
		assertEquals(INGREDIENTS, commandInput.getIngredients());
		assertEquals(UNIT_PRICE.getAmount(), commandInput.getUnitPrice());
		assertEquals(OPEN_PRICE, commandInput.getOpenPrice());

		resultActions.andExpect(status().isBadRequest())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.code").value(GlobalErrorCode.CATALOG_SERVICE_GENERIC_ERROR.getValue()))
				.andExpect(jsonPath("$.message").value(GlobalErrorCode.CATALOG_SERVICE_GENERIC_ERROR.getMessage()));
	}

	@Test
	void shouldReturn204_whenActivateProduct() throws Exception {
		// Arrange

		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}/activate")
				.buildAndExpand(uriVariables)
				.toUriString();

		// Act
		ResultActions resultActions = mockMvc.perform(patch(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(productDomainService, times(1)).activate(any(), any());

		resultActions

				.andExpect(status().isNoContent());
	}

	@Test
	void shouldReturn404_whenActivateProduct() throws Exception {
		// Arrange
		String exceptionMessage = "not found";
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}/activate")
				.buildAndExpand(uriVariables)
				.toUriString();

		doThrow(new ProductNotFoundException(exceptionMessage)).when(productDomainService)
				.activate(any(), any());

		// Act
		ResultActions resultActions = mockMvc.perform(patch(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(productDomainService, times(1)).activate(any(), eq(new ProductId(ID)));
		resultActions.andExpect(status().isBadRequest());
	}

	@Test
	void shouldReturn400_whenActivateProduct() throws Exception {
		// Arrange
		String exceptionMessage = "bad request";
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}/activate")
				.buildAndExpand(uriVariables)
				.toUriString();

		doThrow(new CatalogDomainException(exceptionMessage)).when(productDomainService)
				.activate(any(), any());

		// Act
		ResultActions resultActions = mockMvc.perform(patch(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(productDomainService, times(1)).activate(any(), eq(new ProductId(ID)));
		resultActions.andExpect(status().isBadRequest());
	}

	@Test
	void shouldReturn204_whenDeactivateProduct() throws Exception {
		// Arrange
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}/deactivate")
				.buildAndExpand(uriVariables)
				.toUriString();
		// Act
		ResultActions resultActions = mockMvc.perform(patch(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(productDomainService, times(1)).deactivate(any(), eq(new ProductId(ID)));
		resultActions.andExpect(status().isNoContent());
	}

	@Test
	void shouldReturn404_whenDeactivateProduct() throws Exception {
		// Arrange
		String exceptionMessage = "not found";
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}/deactivate")
				.buildAndExpand(uriVariables)
				.toUriString();
		doThrow(new ProductNotFoundException(exceptionMessage)).when(productDomainService)
				.deactivate(any(), any());
		// Act
		ResultActions resultActions = mockMvc.perform(patch(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(productDomainService, times(1)).deactivate(any(), eq(new ProductId(ID)));
		resultActions.andExpect(status().isBadRequest());
	}

	@Test
	void shouldReturn400_whenDeactivateProduct() throws Exception {
		// Arrange
		String exceptionMessage = "bad request";
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}/deactivate")
				.buildAndExpand(uriVariables)
				.toUriString();
		doThrow(new CatalogDomainException(exceptionMessage)).when(productDomainService)
				.deactivate(any(), any());
		// Act
		ResultActions resultActions = mockMvc.perform(patch(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(productDomainService, times(1)).deactivate(any(), eq(new ProductId(ID)));
		resultActions.andExpect(status().isBadRequest());
	}

	@Test
	void shouldReturn204_whenDeleteProduct() throws Exception {
		// Arrange
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}")
				.buildAndExpand(uriVariables)
				.toUriString();
		// Act
		ResultActions resultActions = mockMvc.perform(delete(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(productDomainService, times(1)).deleteById(any(), eq(new ProductId(ID)));
		resultActions.andExpect(status().isNoContent());
	}

	@Test
	void shouldReturn404_whenDeleteProduct() throws Exception {
		// Arrange
		String exceptionMessage = "not found";
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}")
				.buildAndExpand(uriVariables)
				.toUriString();
		doThrow(new ProductNotFoundException(exceptionMessage)).when(productDomainService)
				.deleteById(any(), any());
		// Act
		ResultActions resultActions = mockMvc.perform(delete(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(productDomainService, times(1)).deleteById(any(), eq(new ProductId(ID)));
		resultActions.andExpect(status().isBadRequest());
	}

	@Test
	void shouldReturn400_whenDeleteProduct() throws Exception {
		// Arrange
		String exceptionMessage = "bad request";
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("productId", Long.toString(ID));
		String uri = UriComponentsBuilder.fromPath(PRODUCT_URL)
				.pathSegment("{productId}")
				.buildAndExpand(uriVariables)
				.toUriString();

		doThrow(new CatalogDomainException(exceptionMessage)).when(productDomainService)
				.deleteById(any(), any());

		// Act
		ResultActions resultActions = mockMvc.perform(delete(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(productDomainService, times(1)).deleteById(any(), eq(new ProductId(ID)));
		resultActions.andExpect(status().isBadRequest());
	}

	private HttpHeaders getHttpHeaders() {
		Map<String, String> map = new HashMap<>();
		map.put(REQUEST_ID_HEADER, Long.toString(new Random().nextLong()));
		map.put(TENANT_ID_HEADER, Long.toString(TENANT_ID));
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setAll(map);
		return httpHeaders;
	}

	private ProductDto getProductDto() {
		ProductImage image = ProductImage.builder()
				.id(new ProductImageId(IMAGE_ID))
				.imagePath(IMAGE_PATH)
				.build();
		return ProductDto.builder()
				.id(new ProductId(ID))
				.category(CategoryStubDto.builder()
						.id(new CategoryId(CATEGORY_ID))
						.build())
				.healthierChoice(HealthierChoice.builder()
						.id(new HealthierChoiceId(HEALTHIER_CHOICE_ID))
						.build())
				.storeId(new StoreId(STORE_ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.sku(SKU)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.barcode(BARCODE)
				.status(STATUS)
				.unitPrice(UNIT_PRICE.getAmount())
				.openPrice(OPEN_PRICE)
				.images(List.of(image))
				.build();
	}
}
