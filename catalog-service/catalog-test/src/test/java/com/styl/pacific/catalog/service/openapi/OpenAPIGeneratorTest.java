/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.openapi;

import com.styl.pacific.catalog.service.config.IntegrationTestConfiguration;
import com.styl.pacific.catalog.service.features.tenant.TenantCreatedEventKafkaConsumer;
import com.styl.pacific.common.test.openapi.AbstractOpenApiGenerator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoBeans;

@SuppressWarnings("java:S2187")
@DirtiesContext
@MockitoBeans(value = { @MockitoBean(types = TenantCreatedEventKafkaConsumer.class), })
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
public class OpenAPIGeneratorTest extends AbstractOpenApiGenerator {

	@Value("${spring.application.name}")
	private String applicationName;

	protected String getServiceName() {
		return applicationName;
	}

}
