/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.rest.controller.allergen.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.catalog.service.domain.allergen.dto.command.CreateAllergenCommand;
import com.styl.pacific.catalog.service.domain.allergen.dto.command.UpdateAllergenCommand;
import com.styl.pacific.catalog.service.domain.allergen.entity.Allergen;
import com.styl.pacific.catalog.service.presenter.rest.allergen.mapper.AllergenRestMapper;
import com.styl.pacific.catalog.service.shared.http.allergen.request.CreateAllergenRequest;
import com.styl.pacific.catalog.service.shared.http.allergen.request.UpdateAllergenRequest;
import com.styl.pacific.catalog.service.shared.http.allergen.response.AllergenResponse;
import com.styl.pacific.domain.valueobject.AllergenId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
public class AllergenRestMapperTest {
	private static final Long ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final String NAME = "name";
	private static final String DESCRIPTION = "description";
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();

	@Test
	void shouldReturnCreateCommand_whenMapFromRequest() {
		// Arrange
		CreateAllergenRequest requestMock = new CreateAllergenRequest(NAME, null, DESCRIPTION);
		// Act
		CreateAllergenCommand command = AllergenRestMapper.INSTANCE.toCommand(requestMock);
		// Assert
		assertEquals(requestMock.name(), command.name());
		assertEquals(requestMock.description(), command.description());
	}

	@Test
	void shouldReturnUpdateCommand_whenMapFromRequest() {
		// Arrange
		UpdateAllergenRequest requestMock = new UpdateAllergenRequest(NAME, DESCRIPTION);
		// Act
		UpdateAllergenCommand command = AllergenRestMapper.INSTANCE.toCommand(requestMock);
		// Assert
		assertEquals(requestMock.name(), command.name());
		assertEquals(requestMock.description(), command.description());
	}

	@Test
	void shouldReturnAllergenResponse_whenMapFromModel() {
		// Arrange
		Allergen allergenMock = getAllergen();
		// Act
		AllergenResponse response = AllergenRestMapper.INSTANCE.toResponse(allergenMock);
		// Assert
		assertEquals(allergenMock.getId()
				.getValue()
				.toString(), response.id());
		assertEquals(allergenMock.getTenantId()
				.getValue()
				.toString(), response.tenantId());
		assertEquals(allergenMock.getName(), response.name());
		assertEquals(allergenMock.getDescription(), response.description());
		assertEquals(allergenMock.getCreatedAt()
				.toEpochMilli(), response.createdAt());
		assertEquals(allergenMock.getUpdatedAt()
				.toEpochMilli(), response.updatedAt());
	}

	private Allergen getAllergen() {
		return Allergen.builder()
				.id(new AllergenId(ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.description(DESCRIPTION)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}
}
