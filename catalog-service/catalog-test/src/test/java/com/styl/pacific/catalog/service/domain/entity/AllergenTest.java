/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.entity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.catalog.service.domain.allergen.entity.Allergen;
import com.styl.pacific.domain.valueobject.AllergenId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
public class AllergenTest {
	private static final Long ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final String NAME = "name";
	private static final String DESCRIPTION = "description";
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();

	@Test
	void shouldInvolve_whenBuilder() {
		// Act
		Allergen allergen = Allergen.builder()
				.id(new AllergenId(ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.description(DESCRIPTION)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
		// Assert
		assertEquals(ID, allergen.getId()
				.getValue());
		assertEquals(TENANT_ID, allergen.getTenantId()
				.getValue());
		assertEquals(NAME, allergen.getName());
		assertEquals(DESCRIPTION, allergen.getDescription());
		assertEquals(CREATED_AT, allergen.getCreatedAt());
		assertEquals(UPDATED_AT, allergen.getUpdatedAt());
	}

	@Test
	void shouldInvolve_whenSetter() {
		// Arrange
		Allergen allergenMock = Allergen.builder()
				.build();
		// Act
		allergenMock.setId(new AllergenId(ID));
		allergenMock.setTenantId(new TenantId(TENANT_ID));
		allergenMock.setName(NAME);
		allergenMock.setDescription(DESCRIPTION);
		allergenMock.setCreatedAt(CREATED_AT);
		allergenMock.setUpdatedAt(UPDATED_AT);
		// Assert
		assertEquals(ID, allergenMock.getId()
				.getValue());
		assertEquals(TENANT_ID, allergenMock.getTenantId()
				.getValue());
		assertEquals(NAME, allergenMock.getName());
		assertEquals(DESCRIPTION, allergenMock.getDescription());
		assertEquals(CREATED_AT, allergenMock.getCreatedAt());
		assertEquals(UPDATED_AT, allergenMock.getUpdatedAt());
	}

	@Test
	void shouldEqual_whenEquals() {
		// Arrange
		Allergen allergen1 = getAllergen();
		Allergen allergen2 = getAllergen();
		// Act
		boolean equal = allergen1.equals(allergen2);
		// Assert
		assertTrue(equal);
	}

	@Test
	void shouldNotEqual_whenEquals() {
		// Arrange
		Allergen allergen1 = getAllergen();
		Allergen allergen2 = getAllergen();
		allergen2.setName("newName");
		// Act
		boolean equal = allergen1.equals(allergen2);
		// Assert
		assertFalse(equal);
	}

	@Test
	void shouldEqual_whenHashCode() {
		// Arrange
		Allergen allergen1 = getAllergen();
		Allergen allergen2 = getAllergen();
		// Act
		boolean equal = allergen1.hashCode() == allergen2.hashCode();
		// Assert
		assertTrue(equal);
	}

	@Test
	void shouldNotEqual_whenHashCode() {
		// Arrange
		Allergen allergen1 = getAllergen();
		Allergen allergen2 = getAllergen();
		allergen2.setName("newName");
		// Act
		boolean equal = allergen1.hashCode() == allergen2.hashCode();
		// Assert
		assertFalse(equal);
	}

	private Allergen getAllergen() {
		return Allergen.builder()
				.id(new AllergenId(ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.description(DESCRIPTION)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}
}
