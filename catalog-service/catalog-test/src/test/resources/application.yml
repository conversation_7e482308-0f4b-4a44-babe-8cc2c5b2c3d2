server:
  port: 9203

spring:
  application:
    name: catalog-service
    version: '@project.version@'
    author: <EMAIL>
  output.ansi.enabled: ALWAYS
  sql:
    init:
      platform: postgres
  jpa:
    open-in-view: false
    show-sql: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
  datasource:
    url: **********************************************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
  flyway:
    enabled: true
    baselineOnMigrate: true
    validateOnMigrate: true
    locations: classpath:db/migration

logging:
  level:
    com.styl.pacific: DEBUG
    sql: DEBUG
    org.hibernate.orm.jdbc.bind: trace

pacific:
  clients:
    store-service:
      url: http://localhost:9204
    tenant-service:
      url: http://localhost:9201

  kafka:
    catalog-service:
      consumers:
        retry-attempts: 5
        retry-interval-ms: 3000
        tenant-created-event:
          enabled: true
          group-id: catalog-service-tenant-created-event
          topic-name: tenant-service-tenant-created-event

  aws:
    s3:
      endpoint: http://localhost:9000
      region: ap-southeast-1
      accessKey: test
      secretKey: test

kafka-config:
  bootstrap-servers: localhost:9092
  schema-registry-url-key: schema.registry.url
  schema-registry-url: http://localhost:8081
  num-of-partitions: 3
  replication-factor: 3
kafka-producer-config:
  key-serializer-class: org.apache.kafka.common.serialization.UUIDSerializer
  value-serializer-class: io.confluent.kafka.serializers.KafkaAvroSerializer
  compression-type: none
  acks: all
  batch-size: 16384
  batch-size-boost-factor: 100
  linger-ms: 5
  request-timeout-ms: 60000
  retry-count: 0
kafka-consumer-config:
  key-deserializer: org.apache.kafka.common.serialization.UUIDDeserializer
  value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
  auto-offset-reset: earliest
  specific-avro-reader-key: specific.avro.reader
  specific-avro-reader: true
  batch-listener: false
  auto-startup: true
  concurrency-level: 1
  session-timeout-ms: 10000
  heartbeat-interval-ms: 3000
  max-poll-interval-ms: 300000
  max-poll-records: 500
  max-partition-fetch-bytes-default: 1048576
  max-partition-fetch-bytes-boost-factor: 1
  poll-timeout-ms: 150
