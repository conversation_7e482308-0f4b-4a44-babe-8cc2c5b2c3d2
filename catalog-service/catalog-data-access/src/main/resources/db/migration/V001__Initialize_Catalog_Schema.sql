CREATE
    TABLE
        IF NOT EXISTS tb_category(
            id BIGINT PRIMARY KEY NOT NULL,
            tenant_id BIGINT NOT NULL,
            name CHARACTER VARYING(50) NOT NULL,
            icon_path VARCHAR(256),
            description CHARACTER VARYING(256) NOT NULL,
            parent_id BIGINT,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            CONSTRAINT fk_tb_category_parent_id_tb_category FOREIGN KEY(parent_id) REFERENCES tb_category(id)
        );

CREATE
    TABLE
        IF NOT EXISTS tb_healthier_choice(
            id BIGINT PRIMARY KEY NOT NULL,
            tenant_id BIGINT NOT NULL,
            name CHARACTER VARYING(256) NOT NULL,
            symbol_path VARCHAR(2048) NOT NULL,
            description TEXT NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE
        );

CREATE
    TABLE
        IF NOT EXISTS tb_product(
            id BIGINT PRIMARY KEY NOT NULL,
            tenant_id BIGINT NOT NULL,
            store_id BIGINT NOT NULL,
            category_id BIGINT NOT NULL,
            healthier_choice_id BIGINT,
            name CHARACTER VARYING(100) NOT NULL,
            brief_information TEXT,
            description TEXT,
            sku VARCHAR(50) NOT NULL,
            ingredients TEXT,
            barcode VARCHAR(100),
            status VARCHAR(32) NOT NULL,
            preparation_time INTEGER NOT NULL,
            unit_price NUMERIC(
                38,
                0
            ) NOT NULL,
            listing_price NUMERIC(
                38,
                0
            ),
            currency_code VARCHAR(3) NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE
        );

ALTER TABLE
    tb_product ADD CONSTRAINT fk_tb_product_category_id_tb_category FOREIGN KEY(category_id) REFERENCES tb_category(id);

ALTER TABLE
    tb_product ADD CONSTRAINT fk_tb_product_healthier_choice_id_tb_healthier_choice FOREIGN KEY(healthier_choice_id) REFERENCES tb_healthier_choice(id);

CREATE
    TABLE
        IF NOT EXISTS tb_product_image(
            id BIGINT PRIMARY KEY NOT NULL,
            product_id BIGINT NOT NULL,
            POSITION INTEGER NOT NULL,
            image_path VARCHAR(2048) NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            CONSTRAINT fk_tb_product_image_product_id_tb_product FOREIGN KEY(product_id) REFERENCES tb_product(id)
        );

CREATE
    TABLE
        IF NOT EXISTS tb_product_option(
            id BIGINT PRIMARY KEY NOT NULL,
            title CHARACTER VARYING(256) NOT NULL,
            product_id BIGINT NOT NULL,
            minimum INTEGER,
            maximum INTEGER,
            TYPE VARCHAR(32) NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            CONSTRAINT fk_tb_product_option_product_id_tb_product FOREIGN KEY(product_id) REFERENCES tb_product(id)
        );

CREATE
    TABLE
        IF NOT EXISTS tb_product_option_item(
            id BIGINT PRIMARY KEY NOT NULL,
            option_id BIGINT NOT NULL,
            name CHARACTER VARYING(256) NOT NULL,
            addition_price NUMERIC(
                38,
                0
            ) NOT NULL,
            active BOOLEAN NOT NULL DEFAULT TRUE,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            CONSTRAINT fk_tb_product_option_item_option_id_tb_product_option FOREIGN KEY(option_id) REFERENCES tb_product_option(id)
        );

CREATE
    TABLE
        IF NOT EXISTS tb_nutrition(
            id BIGINT PRIMARY KEY NOT NULL,
            tenant_id BIGINT NOT NULL,
            name CHARACTER VARYING(256) NOT NULL,
            unit CHARACTER VARYING(256) NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE
        );

CREATE
    TABLE
        IF NOT EXISTS tb_product_nutrition(
            product_id BIGINT NOT NULL,
            nutrition_id BIGINT NOT NULL,
            value FLOAT(53) NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            PRIMARY KEY(
                product_id,
                nutrition_id
            ),
            CONSTRAINT fk_tb_product_nutrition_product_id_tb_product FOREIGN KEY(product_id) REFERENCES tb_product(id),
            CONSTRAINT fk_tb_product_nutrition_nutrition_id_tb_nutrition FOREIGN KEY(nutrition_id) REFERENCES tb_nutrition(id)
        );

CREATE
    TABLE
        IF NOT EXISTS tb_inventory(
            product_id BIGINT PRIMARY KEY NOT NULL,
            quantity BIGINT NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            CONSTRAINT fk_tb_inventory_product_id_tb_product FOREIGN KEY(product_id) REFERENCES tb_product(id)
        );

CREATE
    TABLE
        tb_nutrition_tenant(
            tenant_id BIGINT NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            CONSTRAINT pk_nutrition_tenant PRIMARY KEY(tenant_id)
        );