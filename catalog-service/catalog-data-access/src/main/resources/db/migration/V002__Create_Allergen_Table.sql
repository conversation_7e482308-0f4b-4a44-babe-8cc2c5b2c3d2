CREATE
    TABLE
        IF NOT EXISTS tb_allergen(
            id BIGINT PRIMARY KEY NOT NULL,
            tenant_id BIGINT NOT NULL,
            name CHARACTER VARYING(32) NOT NULL,
            description TEXT,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE
        );

CREATE
    TABLE
        IF NOT EXISTS tb_product_allergen(
            product_id BIGINT NOT NULL,
            allergen_id BIGINT NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            CONSTRAINT pk_tb_product_allergen PRIMARY KEY(
                product_id,
                allergen_id
            ),
            CONSTRAINT fk_tb_product_allergen_product_id_tb_product FOREIGN KEY(product_id) REFERENCES tb_product(id),
            CONSTRAINT fk_tb_product_allergen_allergen_id_tb_allergen FOREIGN KEY(allergen_id) REFERENCES tb_allergen(id)
        );
