/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.category.entity;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.SQLDelete;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * <AUTHOR>
 */

@Data
@Entity
@Table(name = "tb_category")
@SQLDelete(sql = "UPDATE tb_category SET deleted_at = current_timestamp WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
@DynamicUpdate
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class CategoryEntity extends CategoryBase {
	public static final String FIELD_ID = "id";
	public static final String FIELD_NAME = "name";
	public static final String FIELD_PARENT_ID = "parentId";
	public static final String FIELD_TENANT_ID = "tenantId";
	public static final String FIELD_ICON_PATH = "iconPath";
	public static final String FIELD_DESCRIPTION = "description";
	public static final String FIELD_CREATED_AT = "createdAt";
	public static final String FIELD_UPDATED_AT = "updatedAt";
	public static final String FIELD_DELETED_AT = "deletedAt";

	public static final Set<String> SORTABLE_FIELDS = Set.of(FIELD_ID, FIELD_NAME, FIELD_PARENT_ID, FIELD_CREATED_AT);

	@OneToMany(cascade = CascadeType.ALL)
	@JoinColumn(name = "parent_id", referencedColumnName = "id")
	private List<CategoryEntity> subCategories = new ArrayList<>();
}
