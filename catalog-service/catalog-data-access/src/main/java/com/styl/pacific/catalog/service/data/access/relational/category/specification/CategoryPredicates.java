/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.category.specification;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.styl.pacific.catalog.service.data.access.relational.category.entity.QCategoryEntity;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CategoryPredicates {

	public static BooleanBuilder withMultipleTreeCriteria(Long tenantId, String name) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(tenantId)) {
			specifications.and(withTenantId(tenantId));
		}
		if (StringUtils.isNotBlank(name)) {
			specifications.and(likeName(name));
		}
		specifications.and(withParentIdIsNull());
		specifications.and(withIsNotDeleted());
		return specifications;
	}

	public static BooleanBuilder withTenantIdAndMigrationId(Long tenantId, String migrationId) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(tenantId)) {
			specifications.and(withTenantId(tenantId));
		}
		if (StringUtils.isNotBlank(migrationId)) {
			specifications.and(withMigrationId(migrationId));
		}
		specifications.and(withIsNotDeleted());
		return specifications;
	}

	public static BooleanBuilder withMultipleCriteria(Long tenantId, Long parentId, String name, String migrationId) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(tenantId)) {
			specifications.and(withTenantId(tenantId));
		}
		if (Objects.nonNull(parentId)) {
			specifications.and(withParentId(parentId));
		}
		if (StringUtils.isNotBlank(name)) {
			specifications.and(likeName(name));
		}
		if (StringUtils.isNotBlank(migrationId)) {
			specifications.and(withMigrationId(migrationId));
		}
		specifications.and(withIsNotDeleted());
		return specifications;
	}

	public static BooleanBuilder withTenantIdAndId(Long tenantId, Long id) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(tenantId)) {
			specifications.and(withTenantId(tenantId));
		}
		if (Objects.nonNull(id)) {
			specifications.and(withId(id));
		}
		specifications.and(withIsNotDeleted());
		return specifications;
	}

	public static Predicate withTenantId(Long tenantId) {
		return QCategoryEntity.categoryEntity.tenantId.eq(tenantId);
	}

	public static Predicate withId(Long id) {
		return QCategoryEntity.categoryEntity.id.eq(id);
	}

	public static Predicate withMigrationId(String migrationId) {
		return QCategoryEntity.categoryEntity.migrationId.eq(migrationId);
	}

	public static Predicate withParentId(Long parentId) {
		return QCategoryEntity.categoryEntity.parentId.eq(parentId);
	}

	public static Predicate withParentIdIsNull() {
		return QCategoryEntity.categoryEntity.parentId.isNull();
	}

	public static Predicate likeName(String name) {
		return QCategoryEntity.categoryEntity.name.containsIgnoreCase(name);
	}

	public static Predicate withIsNotDeleted() {
		return QCategoryEntity.categoryEntity.deletedAt.isNull();
	}
}
