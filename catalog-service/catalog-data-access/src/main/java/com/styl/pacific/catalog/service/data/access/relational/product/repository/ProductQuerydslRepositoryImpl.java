/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.repository;

import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.specification.ProductNutritionPredicates;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductAllergenEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductImageEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductNutritionEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.QProductAllergenEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.QProductEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.QProductImageEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.QProductNutritionEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.specification.ProductAllergenPredicates;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ProductQuerydslRepositoryImpl implements ProductQuerydslRepository {

	@PersistenceContext
	private EntityManager entityManager;
	private JPAQueryFactory queryFactory;

	@PostConstruct
	public void setup() {
		this.queryFactory = new JPAQueryFactory(entityManager);
	}

	@Override
	public Optional<ProductEntity> findOneWithFetchJoinDetails(Predicate predicate) {
		var query = queryFactory.selectFrom(QProductEntity.productEntity)
				.leftJoin(QProductEntity.productEntity.category)
				.fetchJoin()
				.leftJoin(QProductEntity.productEntity.healthierChoice)
				.fetchJoin()
				.where(predicate, QProductEntity.productEntity.category.deletedAt.isNull(),
						QProductEntity.productEntity.healthierChoice.deletedAt.isNull());
		return Optional.ofNullable(query.fetchOne())
				.map(productEntity -> {
					List<ProductImageEntity> productImageEntities = queryFactory.selectFrom(
							QProductImageEntity.productImageEntity)
							.where(QProductImageEntity.productImageEntity.product.id.eq(productEntity.getId())
									.and(QProductImageEntity.productImageEntity.deletedAt.isNull()))
							.fetch();
					productEntity.setImages(productImageEntities);
					List<ProductAllergenEntity> productAllergenEntities = queryFactory.selectFrom(
							QProductAllergenEntity.productAllergenEntity)
							.leftJoin(QProductAllergenEntity.productAllergenEntity.allergen)
							.where(ProductAllergenPredicates.joinAllergenWithProductId(productEntity.getId()))
							.fetchJoin()
							.fetch();
					productEntity.setAllergens(productAllergenEntities);
					List<ProductNutritionEntity> productNutritionEntities = queryFactory.selectFrom(
							QProductNutritionEntity.productNutritionEntity)
							.where(ProductNutritionPredicates.joinNutritionWithProductId(productEntity.getId()))
							.fetch();
					productEntity.setNutrition(productNutritionEntities);
					return productEntity;
				});
	}

	@Override
	public Optional<ProductEntity> findOneWithFetchJoin(Predicate predicate) {
		var query = queryFactory.selectFrom(QProductEntity.productEntity)
				.leftJoin(QProductEntity.productEntity.category)
				.fetchJoin()
				.leftJoin(QProductEntity.productEntity.healthierChoice)
				.fetchJoin()
				.where(predicate, QProductEntity.productEntity.category.deletedAt.isNull(),
						QProductEntity.productEntity.healthierChoice.deletedAt.isNull());
		return Optional.ofNullable(query.fetchOne());
	}
}
