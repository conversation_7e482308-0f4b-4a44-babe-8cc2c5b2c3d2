/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.category.handler;

import com.styl.pacific.catalog.service.domain.category.dto.CategoryStubDto;
import com.styl.pacific.catalog.service.domain.category.dto.query.CategoryQuery;
import com.styl.pacific.catalog.service.domain.category.dto.query.PaginationCategoryQuery;
import com.styl.pacific.catalog.service.domain.category.dto.query.TreeCategoryQuery;
import com.styl.pacific.catalog.service.domain.category.entity.Category;
import com.styl.pacific.catalog.service.domain.category.exception.CategoryNotFoundException;
import com.styl.pacific.catalog.service.domain.category.output.repository.CategoryRepository;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CategoryQueryHandler {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	private final CategoryRepository categoryRepository;

	@Transactional(readOnly = true)
	public Category findById(TenantId tenantId, CategoryId id) {
		return categoryRepository.findById(tenantId, id)
				.orElseThrow(() -> {
					logger.warn("Category not found with id: {}", id);
					return new CategoryNotFoundException("Category not found with id: " + id.getValue());
				});
	}

	@Transactional(readOnly = true)
	public List<CategoryStubDto> findAll(TenantId tenantId, CategoryQuery query) {
		return categoryRepository.findAll(tenantId, query);
	}

	@Transactional(readOnly = true)
	public List<Category> findAllTree(TenantId tenantId, TreeCategoryQuery query) {
		return categoryRepository.findTreeAll(tenantId, query);
	}

	@Transactional(readOnly = true)
	public Paging<CategoryStubDto> findAllPaging(TenantId tenantId, PaginationCategoryQuery query) {
		return categoryRepository.findPagingAll(tenantId, query);
	}

}
