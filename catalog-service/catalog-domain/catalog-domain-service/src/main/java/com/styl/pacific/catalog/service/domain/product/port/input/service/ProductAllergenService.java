/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product.port.input.service;

import com.styl.pacific.catalog.service.domain.allergen.entity.Allergen;
import com.styl.pacific.catalog.service.domain.product.dto.command.allergen.UpdateProductAllergenCommand;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.TenantId;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProductAllergenService {
	List<Allergen> findAllByProductId(@NotNull TenantId tenantId, @NotNull ProductId id);

	List<Allergen> updateAllergens(@NotNull TenantId tenantId, @NotNull ProductId id,
			@NotNull @Valid List<UpdateProductAllergenCommand> command);
}
