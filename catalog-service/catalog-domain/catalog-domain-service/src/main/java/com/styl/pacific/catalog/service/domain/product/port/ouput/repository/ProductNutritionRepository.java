/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product.port.ouput.repository;

import com.styl.pacific.catalog.service.domain.product.dto.ProductNutritionDto;
import com.styl.pacific.catalog.service.domain.product.entity.ProductNutrition;
import com.styl.pacific.domain.valueobject.NutritionId;
import com.styl.pacific.domain.valueobject.ProductId;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProductNutritionRepository {
	List<ProductNutritionDto> findAllByProductId(ProductId id);

	List<ProductNutrition> findAllByProductIdInNutritionList(ProductId id, List<NutritionId> nutritionIds);

	List<ProductNutritionDto> updateByProductId(ProductId id, List<ProductNutrition> nutrition);
}
