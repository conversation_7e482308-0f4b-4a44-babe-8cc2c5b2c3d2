/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.tenant.dto;

import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@Builder
public class TenantSettingDto {

	private String defaultDomain;

	private TimezoneDto timeZone;

	private CurrencyDto currency;

	private String dateFormat;

	private String timeFormat;

	@Getter
	@Builder
	public static class CurrencyDto {
		private final int numericCode;
		private final String currencyCode;
		private final String symbol;
		private final int fractionDigits;
	}

	@Getter
	@Builder
	public static class TimezoneDto {
		private String zoneId;
		private String gtmOffset;
		private String displayName;
	}
}
