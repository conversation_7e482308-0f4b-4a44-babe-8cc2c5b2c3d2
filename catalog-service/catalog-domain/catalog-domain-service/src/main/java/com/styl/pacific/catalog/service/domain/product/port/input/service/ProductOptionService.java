/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product.port.input.service;

import com.styl.pacific.catalog.service.domain.product.dto.command.option.CreateProductOptionCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.option.UpdateProductOptionCommand;
import com.styl.pacific.catalog.service.domain.product.dto.query.option.ProductOptionQuery;
import com.styl.pacific.catalog.service.domain.product.entity.ProductOption;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.ProductOptionId;
import com.styl.pacific.domain.valueobject.TenantId;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProductOptionService {
	List<ProductOption> findAll(TenantId tenantId, @NotNull(message = "productId must not be null") ProductId productId,
			@Valid ProductOptionQuery query);

	ProductOption findById(TenantId tenantId, @NotNull(message = "productId must not be null") ProductId productId,
			@NotNull(message = "id must not be null") ProductOptionId id);

	ProductOption create(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "productId must not be null") ProductId productId,
			@Valid @NotNull(message = "command must not be null") CreateProductOptionCommand command);

	ProductOption update(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "productId must not be null") ProductId productId,
			@NotNull(message = "id must not be null") ProductOptionId id,
			@Valid @NotNull(message = "command must not be null") UpdateProductOptionCommand command);

	void deleteById(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "productId must not be null") ProductId productId,
			@NotNull(message = "id must not be null") ProductOptionId id);
}
