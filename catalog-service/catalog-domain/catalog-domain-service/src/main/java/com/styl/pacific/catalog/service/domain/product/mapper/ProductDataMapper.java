/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product.mapper;

import com.styl.pacific.catalog.service.domain.allergen.entity.Allergen;
import com.styl.pacific.catalog.service.domain.category.mapper.CategoryDataMapper;
import com.styl.pacific.catalog.service.domain.common.mapper.CommonDataMapper;
import com.styl.pacific.catalog.service.domain.healthierchoice.mapper.HealthierChoiceDataMapper;
import com.styl.pacific.catalog.service.domain.product.dto.command.CreateProductCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.CreateProductWithDetailsCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.UpdateProductCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.UpdateProductWithDetailsCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.allergen.UpdateProductAllergenCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.image.UpdateProductImageCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.nutrition.UpdateProductNutritionCommand;
import com.styl.pacific.catalog.service.domain.product.entity.Product;
import com.styl.pacific.catalog.service.domain.product.entity.ProductImage;
import com.styl.pacific.catalog.service.domain.product.entity.ProductNutrition;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.enums.ProductType;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.mapstruct.AfterMapping;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { CategoryDataMapper.class, HealthierChoiceDataMapper.class,
		ProductImageDataMapper.class, ProductNutritionDataMapper.class, ProductOptionDataMapper.class,
		CommonDataMapper.class, MapstructCommonMapper.class, MapstructCommonDomainMapper.class })
public interface ProductDataMapper {

	ProductDataMapper INSTANCE = Mappers.getMapper(ProductDataMapper.class);

	@Mapping(target = "id", ignore = true)
	@Mapping(target = "tenantId", source = "tenantId")
	@Mapping(target = "storeId", source = "command.storeId", qualifiedByName = "longToStoreId")
	@Mapping(target = "unitPrice", source = "command.unitPrice", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "listingPrice", source = "command.listingPrice", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "healthierChoiceId", source = "command.healthierChoiceId", qualifiedByName = "longToHealthierChoiceId")
	@Mapping(target = "categoryId", source = "command.categoryId", qualifiedByName = "longToCategoryId")
	@Mapping(target = "currencyCode", ignore = true)
	@Mapping(target = "options", ignore = true)
	@Mapping(target = "nutrition", ignore = true)
	@Mapping(target = "allergens", ignore = true)
	@Mapping(target = "status", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	Product toModel(TenantId tenantId, CreateProductCommand command);

	@Mapping(target = "migrationId", ignore = true)
	@Mapping(target = "id", ignore = true)
	@Mapping(target = "tenantId", ignore = true)
	@Mapping(target = "unitPrice", source = "command.unitPrice", qualifiedByName = "bigIntegerToMoney", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "listingPrice", source = "command.listingPrice", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "currencyCode", ignore = true)
	@Mapping(target = "name", source = "command.name", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "description", source = "command.description", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "briefInformation", source = "command.briefInformation", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "sku", source = "command.sku", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "healthierChoiceId", source = "command.healthierChoiceId", qualifiedByName = "longToHealthierChoiceId")
	@Mapping(target = "categoryId", source = "command.categoryId", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, qualifiedByName = "longToCategoryId")
	@Mapping(target = "productType", ignore = true)
	@Mapping(target = "storeId", ignore = true)
	@Mapping(target = "status", ignore = true)
	@Mapping(target = "images", ignore = true)
	@Mapping(target = "allergens", ignore = true)
	@Mapping(target = "nutrition", ignore = true)
	@Mapping(target = "options", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	@IterableMapping(qualifiedByName = { "updateProductImageCommandToProductImage" })
	void updateProduct(@MappingTarget Product product, UpdateProductCommand command);

	@AfterMapping
	default void afterUpdateProduct(@MappingTarget Product product, UpdateProductCommand command) {
		if (Objects.nonNull(command.getImages())) {
			product.getImages()
					.clear();
			product.setImages(updateProductImage(product.getImages(), command.getImages()));
		}
	}

	private List<ProductImage> updateProductImage(List<ProductImage> images,
			List<UpdateProductImageCommand> updateProductImageCommands) {
		List<ProductImage> productImageList = new ArrayList<>();
		Map<Long, ProductImage> imageMap = images.stream()
				.collect(Collectors.toMap(productImage -> productImage.getId()
						.getValue(), Function.identity()));
		for (UpdateProductImageCommand imageCommand : updateProductImageCommands) {
			if (Objects.isNull(imageCommand.getId()) || !imageMap.containsKey(imageCommand.getId())) {
				productImageList.add(ProductImageDataMapper.INSTANCE.updateProductImageCommandToProductImage(
						imageCommand));
			} else {
				ProductImage productImage = imageMap.get(imageCommand.getId());
				ProductImageDataMapper.INSTANCE.updateProductImageCommandToProductImage(productImage, imageCommand);
				productImageList.add(productImage);
			}
		}
		return productImageList;

	}

	private List<ProductNutrition> updateProductNutrition(List<ProductNutrition> nutrition,
			List<UpdateProductNutritionCommand> updateNutritionCommands) {
		List<ProductNutrition> productNutritionList = new ArrayList<>();
		Map<Long, ProductNutrition> nutritionMap = nutrition.stream()
				.collect(Collectors.toMap(productNutrition -> productNutrition.getNutritionId()
						.getValue(), Function.identity()));
		for (UpdateProductNutritionCommand updateProductNutritionCommand : updateNutritionCommands) {
			if (Objects.isNull(updateProductNutritionCommand.getNutritionId()) || !nutritionMap.containsKey(
					updateProductNutritionCommand.getNutritionId())) {
				productNutritionList.add(ProductNutritionDataMapper.INSTANCE.productNutritionCommandToProductNutrition(
						updateProductNutritionCommand));
			} else {
				ProductNutrition productNutrition = nutritionMap.get(updateProductNutritionCommand.getNutritionId());
				ProductNutritionDataMapper.INSTANCE.productNutritionCommandToProductNutrition(productNutrition,
						updateProductNutritionCommand);
				productNutritionList.add(productNutrition);
			}
		}
		return productNutritionList;
	}

	private List<Allergen> updateProductAllergen(List<UpdateProductAllergenCommand> allergenCommands) {
		return allergenCommands.stream()
				.map(this::toModel)
				.toList();
	}

	@Mapping(target = "id", source = "allergenId", qualifiedByName = "longToAllergenId")
	@Mapping(target = "tenantId", ignore = true)
	@Mapping(target = "name", ignore = true)
	@Mapping(target = "migrationId", ignore = true)
	@Mapping(target = "description", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	Allergen toModel(UpdateProductAllergenCommand command);

	@Mapping(target = "productType", expression = "java(mapProductType(command.getStoreId()))")
	CreateProductCommand toCommand(CreateProductWithDetailsCommand command);

	UpdateProductCommand toCommand(UpdateProductWithDetailsCommand command);

	default ProductType mapProductType(Long storeId) {
		return storeId == null ? ProductType.TENANT_PRODUCT : ProductType.STORE_PRODUCT;
	}
}
