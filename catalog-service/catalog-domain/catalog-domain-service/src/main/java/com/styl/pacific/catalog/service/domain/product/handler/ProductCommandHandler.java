/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product.handler;

import com.styl.pacific.catalog.service.domain.category.exception.CategoryNotFoundException;
import com.styl.pacific.catalog.service.domain.category.output.repository.CategoryRepository;
import com.styl.pacific.catalog.service.domain.common.exception.CatalogDomainException;
import com.styl.pacific.catalog.service.domain.common.exception.CatalogValidationException;
import com.styl.pacific.catalog.service.domain.healthierchoice.exception.HealthierChoiceNotFoundException;
import com.styl.pacific.catalog.service.domain.healthierchoice.output.repository.HealthierChoiceRepository;
import com.styl.pacific.catalog.service.domain.product.ProductDomainCore;
import com.styl.pacific.catalog.service.domain.product.dto.ProductDto;
import com.styl.pacific.catalog.service.domain.product.dto.command.CreateProductCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.UpdateProductCommand;
import com.styl.pacific.catalog.service.domain.product.entity.Product;
import com.styl.pacific.catalog.service.domain.product.entity.ProductImage;
import com.styl.pacific.catalog.service.domain.product.event.ProductActivatedEvent;
import com.styl.pacific.catalog.service.domain.product.event.ProductArchivedEvent;
import com.styl.pacific.catalog.service.domain.product.event.ProductDeactivatedEvent;
import com.styl.pacific.catalog.service.domain.product.exception.ProductNotFoundException;
import com.styl.pacific.catalog.service.domain.product.exception.SkuAlreadyExistsException;
import com.styl.pacific.catalog.service.domain.product.id.generator.ProductIdGenerator;
import com.styl.pacific.catalog.service.domain.product.id.generator.ProductImageIdGenerator;
import com.styl.pacific.catalog.service.domain.product.mapper.ProductDataMapper;
import com.styl.pacific.catalog.service.domain.product.port.ouput.repository.ProductImageRepository;
import com.styl.pacific.catalog.service.domain.product.port.ouput.repository.ProductRepository;
import com.styl.pacific.catalog.service.domain.store.output.repository.StoreRepository;
import com.styl.pacific.catalog.service.domain.tenant.dto.TenantDto;
import com.styl.pacific.catalog.service.domain.tenant.output.repository.TenantRepository;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.ProductImageId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ProductCommandHandler {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private final ProductDomainCore productDomainCore;

	private final CategoryRepository categoryRepository;
	private final ProductRepository productRepository;
	private final ProductImageRepository productImageRepository;
	private final HealthierChoiceRepository healthierChoiceRepository;

	private final StoreRepository storeRepository;
	private final TenantRepository tenantRepository;

	private final ProductIdGenerator productIdGenerator;
	private final ProductImageIdGenerator productImageIdGenerator;

	private void createProductCommandCheck(TenantId tenantId, CreateProductCommand command) {
		createPropertiesProductCheck(tenantId, Optional.ofNullable(command.getCategoryId())
				.map(CategoryId::new)
				.orElse(null), Optional.ofNullable(command.getHealthierChoiceId())
						.map(HealthierChoiceId::new)
						.orElse(null), command.getSku(), command.getKeywords());
		if (Objects.nonNull(command.getStoreId())) {
			storeExistsCheck(new StoreId(command.getStoreId()));
		}
		if (StringUtils.isNotBlank(command.getMigrationId())) {
			boolean isExist = productRepository.existByMigrationId(tenantId, command.getMigrationId());
			if (isExist) {
				logger.warn("Migration ID {} already exist", command.getMigrationId());
				throw new CatalogDomainException(String.format("Migration ID %s already exist", command
						.getMigrationId()));
			}
		}
	}

	@Transactional
	public ProductDto create(TenantId tenantId, @Valid CreateProductCommand command) {
		// Check constraint unique properties
		TenantDto tenant = tenantCheck(tenantId);
		createProductCommandCheck(tenantId, command);
		Product product = ProductDataMapper.INSTANCE.toModel(tenantId, command);
		product.init(productIdGenerator);

		// Process product
		processProductProperties(product);
		if (Objects.isNull(tenant.getSettings()) || Objects.isNull(tenant.getSettings()
				.getCurrency())) {
			throw new CatalogDomainException(String.format("Currency from Tenant %s not found", tenantId));
		}
		product.setCurrencyCode(tenant.getSettings()
				.getCurrency()
				.getCurrencyCode());
		return productRepository.saveDto(product);
	}

	private void updateProductCommandCheck(TenantId tenantId, ProductId id, UpdateProductCommand command) {
		updatePropertiesProductCheck(tenantId, id, Optional.ofNullable(command.getCategoryId())
				.map(CategoryId::new)
				.orElse(null), Optional.ofNullable(command.getHealthierChoiceId())
						.map(HealthierChoiceId::new)
						.orElse(null), command.getSku(), command.getKeywords());
		if (!Objects.isNull(command.getImages())) {
			command.getImages()
					.forEach(updateProductImageCommand -> {
						if (Objects.nonNull(updateProductImageCommand.getId())) {
							productImageExistsCheck(id, Optional.of(updateProductImageCommand.getId())
									.map(ProductImageId::new)
									.orElse(null));
						}
					});
		}
	}

	@Transactional
	public ProductDto update(TenantId tenantId, ProductId id, @Valid UpdateProductCommand command) {
		// Check
		updateProductCommandCheck(tenantId, id, command);
		Product product = productCheck(tenantId, id);
		ProductDataMapper.INSTANCE.updateProduct(product, command);

		// Process Properties
		processProductProperties(product);

		return productRepository.updateDto(product);
	}

	@Transactional
	public void activate(TenantId tenantId, ProductId id) {
		Product product = productCheck(tenantId, id);
		ProductActivatedEvent event = productDomainCore.activeProduct(product);
		productRepository.updateDto(event.getProduct());
	}

	@Transactional
	public void deactivate(TenantId tenantId, ProductId id) {
		Product product = productCheck(tenantId, id);
		ProductDeactivatedEvent event = productDomainCore.inactiveProduct(product);
		productRepository.updateDto(event.getProduct());
	}

	@Transactional
	public void archive(TenantId tenantId, ProductId id) {
		Product product = productCheck(tenantId, id);
		ProductArchivedEvent event = productDomainCore.archiveProduct(product);
		productRepository.updateDto(event.getProduct());
	}

	@Transactional
	public void deleteById(TenantId tenantId, ProductId id) {
		productExistCheck(tenantId, id);
		productRepository.deleteById(tenantId, id);
	}

	private Product productCheck(TenantId tenantId, ProductId id) {
		return productRepository.findById(tenantId, id)
				.orElseThrow(() -> {
					logger.warn("Product {} not found", id);
					return new ProductNotFoundException(String.format("Product %s not found", id.getValue()));
				});
	}

	private void productExistCheck(TenantId tenantId, ProductId id) {
		var exist = productRepository.existById(tenantId, id);
		if (!exist) {
			logger.warn("Product {} not found", id);
			throw new ProductNotFoundException(String.format("Product %s not found", id.getValue()));
		}
	}

	private void categoryExistsCheck(TenantId tenantId, CategoryId categoryId) {
		boolean exist = categoryRepository.existsById(tenantId, categoryId);
		if (!exist) {
			logger.warn("Category {} not exists", categoryId);
			throw new CategoryNotFoundException(String.format("Category %s not found", categoryId.getValue()));
		}
	}

	private void healthierChoiceExistsCheck(TenantId tenantId, HealthierChoiceId healthierId) {
		boolean exist = healthierChoiceRepository.existsById(tenantId, healthierId);
		if (!exist) {
			logger.warn("Healthier Choice {} not found", healthierId);
			throw new HealthierChoiceNotFoundException(String.format("Healthier Choice %s not found", healthierId
					.getValue()));
		}
	}

	private void productImageExistsCheck(ProductId productId, ProductImageId id) {
		boolean exist = productImageRepository.existsById(productId, id);
		if (!exist) {
			logger.warn("Image {} not found", id);
			throw new HealthierChoiceNotFoundException(String.format("Image %s not found", id.getValue()));
		}
	}

	private void storeExistsCheck(StoreId storeId) {
		boolean exist = storeRepository.existsById(storeId);
		if (!exist) {
			logger.warn("Store {} not found", storeId);
			throw new CatalogDomainException(String.format("Store %s not found", storeId.getValue()));
		}
	}

	private void keywordsCheck(List<String> keywords) {
		int totalLength = keywords.stream()
				.map(String::length)
				.reduce(0, Integer::sum);
		if (totalLength > 1000) {
			throw new CatalogValidationException("Keyword must not exceed 1000 characters");
		}
	}

	private TenantDto tenantCheck(TenantId tenantId) {
		return tenantRepository.findById(tenantId.getValue())
				.orElseThrow(() -> {
					logger.warn("Tenant {} not found", tenantId);
					return new CatalogDomainException(String.format("Tenant %s not found", tenantId.getValue()));
				});
	}

	private void processProductProperties(Product product) {
		// Process Image
		if (!Objects.isNull(product.getImages())) {
			List<ProductImage> images = new ArrayList<>(product.getImages());
			for (int i = 0; i < images.size(); i++) {
				ProductImage image = images.get(i);
				if (image.getId() == null) {
					image.setId(productImageIdGenerator.nextId());
				}
				image.setProductId(product.getId());
				image.setPosition(i);
			}
		}
	}

	private void createPropertiesProductCheck(TenantId tenantId, CategoryId categoryId, HealthierChoiceId healthierId,
			String sku, List<String> keywords) {
		if (Objects.nonNull(categoryId)) {
			categoryExistsCheck(tenantId, categoryId);
		}
		if (Objects.nonNull(healthierId)) {
			healthierChoiceExistsCheck(tenantId, healthierId);
		}

		if (StringUtils.isNotBlank(sku)) {
			boolean isExist = productRepository.existBySku(tenantId, sku);
			if (isExist) {
				logger.warn("SKU {} already exist", sku);
				throw new SkuAlreadyExistsException(String.format("SKU %s already exist", sku));
			}
		}
		if (Objects.nonNull(keywords)) {
			keywordsCheck(keywords);
		}
	}

	private void updatePropertiesProductCheck(TenantId tenantId, ProductId productId, CategoryId categoryId,
			HealthierChoiceId healthierId, String sku, List<String> keywords) {
		if (Objects.nonNull(categoryId)) {
			categoryExistsCheck(tenantId, categoryId);
		}
		if (Objects.nonNull(healthierId)) {
			healthierChoiceExistsCheck(tenantId, healthierId);
		}

		if (StringUtils.isNotBlank(sku)) {
			boolean isExist = productRepository.existBySkuNotId(tenantId, productId, sku);
			if (isExist) {
				logger.warn("SKU already exist");
				throw new SkuAlreadyExistsException(String.format("SKU %s already exist", sku));
			}
		}

		if (Objects.nonNull(keywords)) {
			keywordsCheck(keywords);
		}
	}

}
