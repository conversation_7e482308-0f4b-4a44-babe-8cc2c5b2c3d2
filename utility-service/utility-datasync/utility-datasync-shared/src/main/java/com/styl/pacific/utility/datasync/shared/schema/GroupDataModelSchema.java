/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.shared.schema;

import java.util.Map;
import java.util.Optional;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum GroupDataModelSchema {
	GROUP_PATH("group_path"),
	GROUP_DESCRIPTION("group_description");

	private static final Map<String, GroupDataModelSchema> HEADER_KEY_MAP = Map.ofEntries(Map.entry(GROUP_PATH
			.getHeaderKey(), GROUP_PATH), Map.entry(GROUP_DESCRIPTION.getHeaderKey(), GROUP_DESCRIPTION));

	private final String headerKey;

	GroupDataModelSchema(String headerKey) {
		this.headerKey = headerKey;
	}

	public static GroupDataModelSchema parseNullableHeaderKey(String key) {
		return Optional.ofNullable(key)
				.map(String::trim)
				.filter(StringUtils::isNotBlank)
				.map(HEADER_KEY_MAP::get)
				.orElse(null);
	}
}
