/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.api.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonTypeName;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@JsonTypeName("CUSTOMER")
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerDataSyncRecordResponse extends DataSyncRecordDataResponse {

	private final String customerNo;

	private final String firstName;
	private final String lastName;
	private final String email;
	private final String groupName;
	private final String groupPath;
	private final String allergens;
	private final String action;
	private final String familyCode;
	private final String avatarImagePath;
	private final String customerSponsorEmail;
	private final String syncTargetUser;

	@JsonCreator
	@Builder
	public CustomerDataSyncRecordResponse(String customerNo, String firstName, String lastName, String email,
			String groupName, String groupPath, String allergens, String action, String familyCode,
			String avatarImagePath, String customerSponsorEmail, String syncTargetUser) {
		super(DataSyncRecordType.CUSTOMER);
		this.customerNo = customerNo;
		this.firstName = firstName;
		this.lastName = lastName;
		this.email = email;
		this.groupName = groupName;
		this.groupPath = groupPath;
		this.allergens = allergens;
		this.action = action;
		this.familyCode = familyCode;
		this.avatarImagePath = avatarImagePath;
		this.customerSponsorEmail = customerSponsorEmail;
		this.syncTargetUser = syncTargetUser;
	}
}
