/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.api.dto;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Getter;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = DataSyncRecordDataResponse.TYPE_FIELD_NAME, visible = true)
@JsonSubTypes({ @JsonSubTypes.Type(value = GroupDataSyncRecordResponse.class, name = "GROUP"),
		@JsonSubTypes.Type(value = CustomerDataSyncRecordResponse.class, name = "CUSTOMER"),
		@JsonSubTypes.Type(value = CardDataSyncRecordResponse.class, name = "CARD") })
@Getter
public abstract class DataSyncRecordDataResponse {
	public static final String TYPE_FIELD_NAME = "recordType";
	private final DataSyncRecordType recordType;

	protected DataSyncRecordDataResponse(DataSyncRecordType recordType) {
		this.recordType = recordType;
	}
}
