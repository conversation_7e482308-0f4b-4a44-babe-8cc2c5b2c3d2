/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service;

import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface FileUtilitiesApi {

	@GetMapping(path = "/api/utility/files/download/pre-signed-url")
	@PacificApiAuthorized
	public ResponseEntity<PresignedUrlResponse> getPresignedUrlDownloadFile(String filePath);

	@GetMapping(path = "/api/utility/files/images/upload/pre-signed-url")
	@PacificApiAuthorized
	public ResponseEntity<PresignedUrlResponse> getPresignedUrlUploadImage(String fileName);

	@GetMapping(path = "/api/utility/files/preset/upload/pre-signed-url")
	@PacificApiAuthorized
	ResponseEntity<PresignedUrlResponse> getPresignedUrlUploadPreset(String fileName);

	@GetMapping(path = "/api/utility/files/logs/upload/pre-signed-url")
	@PacificApiAuthorized
	ResponseEntity<PresignedUrlResponse> getPresignedUrlUploadLogs(@RequestParam String fileName);
}
