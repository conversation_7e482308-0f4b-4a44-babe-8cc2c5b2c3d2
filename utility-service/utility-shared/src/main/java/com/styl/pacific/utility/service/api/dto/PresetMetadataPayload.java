/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.api.dto;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type", include = JsonTypeInfo.As.EXISTING_PROPERTY, visible = true)
@JsonSubTypes({ @JsonSubTypes.Type(value = MenuPresetMetadataPayload.class, name = "MENU"),
		@JsonSubTypes.Type(value = PreOrderMenuPresetMetadataPayload.class, name = "PRE_ORDER_MENU"),
		@JsonSubTypes.Type(value = CustomerPresetMetadataPayload.class, name = "CUSTOMER"),
		@JsonSubTypes.Type(value = ProductPresetMetadataPayload.class, name = "PRODUCT") })
@SuperBuilder(toBuilder = true)
@Data
@NoArgsConstructor
public class PresetMetadataPayload {
	private PresetType type;
}
