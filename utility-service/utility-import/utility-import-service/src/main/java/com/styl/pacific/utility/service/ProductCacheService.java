/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service;

import com.styl.pacific.catalog.service.shared.http.product.request.CreateProductWithDetailsRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.UpdateProductWithDetailsRequest;
import com.styl.pacific.catalog.service.shared.http.product.response.ProductResponse;
import com.styl.pacific.utility.data.access.client.ProductInternalClient;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ProductCacheService {
	private final ProductInternalClient productInternalClient;

	@CachePut(value = "productCache", key = "#request.sku + '_' + #tenantId")
	public ProductResponse createProduct(CreateProductWithDetailsRequest request, String tenantId) {
		return productInternalClient.create(request);
	}

	@CachePut(value = "productCache", key = "#request.sku + '_' + #tenantId")
	public ProductResponse updateProduct(String id, UpdateProductWithDetailsRequest request, String tenantId) {
		return productInternalClient.update(id, request);
	}

	@Cacheable(value = "productCache", key = "#sku + '_' + #tenantId")
	public ProductResponse getProduct(String sku, String tenantId) {
		return productInternalClient.getProductBySku(sku)
				.stream()
				.findFirst()
				.orElse(null);
	}
}
