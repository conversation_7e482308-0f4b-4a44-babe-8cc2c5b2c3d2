/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.rest.controller;

import com.styl.pacific.utility.service.ConfigApi;
import com.styl.pacific.utility.service.api.dto.ConfigurationResponse;
import com.styl.pacific.utility.service.api.dto.ContactResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
public class ConfigController implements ConfigApi {

	@Value("${pacific.config.base-domain:styl.solutions}")
	private String baseDomain;

	@Value("${pacific.config.contact-email:<EMAIL>}")
	private String contactEmail;

	@Value("${pacific.config.contact-phone:+65 6518 3127}")
	private String contactPhone;

	@Override
	public ResponseEntity<ConfigurationResponse> getConfig() {
		return ResponseEntity.ok(ConfigurationResponse.builder()
				.baseDomain(baseDomain)
				.build());
	}

	@Override
	public ResponseEntity<ContactResponse> getContact() {
		return ResponseEntity.ok(new ContactResponse(contactEmail, contactPhone));
	}

}
