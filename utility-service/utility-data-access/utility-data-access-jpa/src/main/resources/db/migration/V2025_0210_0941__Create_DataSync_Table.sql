CREATE
    TABLE
        IF NOT EXISTS tb_data_sync_jobs(
            id BIGSERIAL NOT NULL CONSTRAINT tb_data_sync_job_pk PRIMARY KEY,
            tenant_id BIGINT NOT NULL,
            archived_zip_file_path VARCHAR(250) NOT NULL,
            archived_checksum_file_path VARCHAR(250) NOT NULL,
            archived_file_directory VARCHAR(250) NOT NULL,
            zip_file_checksum VARCHAR(300),
            is_valid_checksum BOOLEAN DEFAULT FALSE,
            status VARCHAR(70) NOT NULL,
            manifest_content TEXT,
            group_csv_schema TEXT,
            customer_csv_schema TEXT,
            card_csv_schema TEXT,
            record_tracking TEXT,
            error_message TEXT,
            version INTEGER NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP(6) WITH TIME ZONE DEFAULT NOW()
        );

CREATE
    UNIQUE INDEX IF NOT EXISTS tb_data_sync_job_tenant_id_archived_file_directory_uidx ON
    tb_data_sync_jobs(
        tenant_id,
        archived_file_directory
    );