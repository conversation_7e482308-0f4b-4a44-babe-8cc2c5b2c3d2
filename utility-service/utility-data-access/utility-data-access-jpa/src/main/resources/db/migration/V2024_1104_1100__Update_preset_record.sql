DROP
    TABLE
        IF EXISTS tb_preset_record;

CREATE
    TABLE
        IF NOT EXISTS tb_preset_record(
            preset_id VARCHAR(36) NOT NULL,
            tenant_id BIGINT NOT NULL,
            ROW BIGINT NOT NULL,
            col BIGINT NOT NULL,
            val TEXT,
            status VARCHAR(20),
            remarks VARCHAR(255),
            processed_at TIMESTAMP WITH TIME ZONE,
            PRIMARY KEY(
                preset_id,
                ROW,
                col
            ),
            FOREIGN KEY(preset_id) REFERENCES tb_preset(id)
        );
