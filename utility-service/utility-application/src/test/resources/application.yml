server:
  port: 9208
logging:
  level:
    com.styl.pacific: DEBUG
spring:
  application:
    name: utility-service
    version: '@project.version@'
    author: <EMAIL>
  jpa:
    hibernate.ddl-auto: none
    open-in-view: false
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  datasource:
    url: **********************************************************************
    username: postgres
    password: postgres
    platform: postgres
    driver-class-name: org.postgresql.Driver
pacific:
  clients:
    tenant-service:
      url: http://localhost:9201
    user-service:
      url: http://localhost:9202
    catalog-service:
      url: http://localhost:9203
    store-service:
      url: http://localhost:9204
    order-service:
      url: http://localhost:9205
    authorization-service:
      url: http://localhost:9209
    wallet-service:
      url: http://localhost:9210
  aws:
    s3:
      endpoint: http://localhost:4566
      region: ap-southeast-1
      accessKey: test
      secretKey: test
      upload:
        bucketName: pacific
        signatureDuration: PT5M
  datasync:
    s3-bucket: "data-sync"
    detector:
      s3-bucket: ${pacific.datasync.s3-bucket}
      s3-archived-prefix: "archived"
      s3-queuing-prefix: "queuing"
      scheduling:
        enabled: true
        cron-expression: "0 */1 * * * *"
        lock-at-least-for: PT10S
        lock-at-most-for: PT15M
    loader:
      s3-bucket: ${pacific.datasync.s3-bucket}
      scheduling:
        enabled: true
        cron-expression: "0 */1 * * * *"
        lock-at-least-for: PT10S
        lock-at-most-for: PT1H
    processor:
      s3-bucket: ${pacific.datasync.s3-bucket}
      scheduling:
        enabled: true
        cron-expression: "0 */1 * * * *"
        lock-at-least-for: PT10S
        lock-at-most-for: PT1H

  dashboard:
    jwt:
      expiresIn: 86400000
  kafka:
    notification-service:
      notification-event:
        topic-name: notification-service-notification-command
kafka-config:
  bootstrap-servers: "localhost:19092, localhost:29092, localhost:39092"
  schema-registry-url-key: schema.registry.url
  schema-registry-url: http://localhost:8081
  num-of-partitions: 3
  replication-factor: 3
kafka-producer-config:
  key-serializer-class: org.apache.kafka.common.serialization.UUIDSerializer
  value-serializer-class: io.confluent.kafka.serializers.KafkaAvroSerializer
  compression-type: none
  acks: all
  batch-size: 16384
  batch-size-boost-factor: 100
  linger-ms: 5
  request-timeout-ms: 60000
  retry-count: 5