/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.shared.http.requests.staff;

import java.util.Collection;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springdoc.core.annotations.ParameterObject;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@ParameterObject
public class FindStaffsRequest {
	private StaffsFilterRequest filter;
	private Integer size;
	private Integer page;
	private String sortDirection;
	private Collection<String> sortFields;
}
