/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.shared.http.requests.store;

import com.styl.pacific.common.validator.numeric.Numeric;
import com.styl.pacific.common.validator.phonenumber.PhoneNumber;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */

@Getter
@Builder
@AllArgsConstructor
public class CreateStoreRequest {

	@NotBlank
	@Length(max = 100)
	private String name;

	@Email
	@Length(max = 255)
	private String email;

	@PhoneNumber
	@Length(max = 20)
	private String phoneNumber;

	@Length(max = 255)
	private String addressLine1;

	@Length(max = 255)
	private String addressLine2;

	@Length(max = 100)
	private String city;

	@Length(max = 100)
	private String country;

	@Numeric
	@Length(max = 10)
	private String postalCode;

	@Length(max = 100)
	private String workingHour;

	@Length(max = 100)
	private String migrationId;

	@Length(max = 32)
	private String orderNumberPrefix;
}
