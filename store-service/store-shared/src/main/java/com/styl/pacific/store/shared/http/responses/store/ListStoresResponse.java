/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.shared.http.responses.store;

import com.styl.pacific.domain.dto.pagination.Paging;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class ListStoresResponse extends Paging<StoreResponse> {

	/**
	 * @param content
	 * @param totalElements
	 * @param totalPages
	 * @param page
	 * @param sort
	 */
	public ListStoresResponse(List<StoreResponse> content, long totalElements, int totalPages, int page,
			List<String> sort) {
		super(content, totalElements, totalPages, page, sort);
	}

}
