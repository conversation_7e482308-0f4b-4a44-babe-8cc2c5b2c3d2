/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain;

import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.store.service.domain.configuration.StoreReviewConfigProperties;
import com.styl.pacific.store.service.domain.entity.ReviewSummary;
import com.styl.pacific.store.service.domain.entity.ReviewSummaryAllStores;
import com.styl.pacific.store.service.domain.entity.ReviewSummaryIdGenerator;
import com.styl.pacific.store.service.domain.output.repository.ReviewSummaryRepository;
import com.styl.pacific.store.service.domain.port.input.service.ReviewSummaryDomainService;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

@Service
@Transactional
@Validated
@AllArgsConstructor
public class ReviewSummaryDomainServiceImpl implements ReviewSummaryDomainService {
	private final ReviewSummaryIdGenerator idGenerator;
	private final ReviewSummaryRepository repository;
	private final StoreReviewConfigProperties storeReviewConfigs;

	@Override
	public ReviewSummary updateReviewSummary(TenantId tenantId, StoreId storeId, Integer incrementalStars) {
		ReviewSummary reviewSummary = repository.findByStoreIdAndTenantId(storeId.getValue(), tenantId.getValue());
		long storeTotalReviews = Objects.isNull(reviewSummary) ? 1L : reviewSummary.getTotalReviews() + 1L;
		long storeTotalStars = Objects.isNull(reviewSummary)
				? incrementalStars.longValue()
				: reviewSummary.getTotalStars() + incrementalStars.longValue();

		if (Objects.isNull(reviewSummary)) {
			reviewSummary = ReviewSummary.builder()
					.id(idGenerator.nextId())
					.storeId(storeId)
					.tenantId(tenantId)
					.build();
		}

		ReviewSummaryAllStores summaryAllStores = repository.findReviewSummaryAllStores();
		double globalAverageStars = calculateGlobalAverage(summaryAllStores.getTotalReviewsAllStores(), summaryAllStores
				.getTotalStarsAllStores());
		double currentStoreAverageStars = storeTotalReviews > 0 ? (double) storeTotalStars / storeTotalReviews : 0.0;
		int confidenceThreshold = chooseConfidenceThreshold(storeTotalReviews);
		double averageStars = calculateAverageStars(currentStoreAverageStars, storeTotalReviews, confidenceThreshold,
				globalAverageStars);
		reviewSummary.setTotalReviews(storeTotalReviews);
		reviewSummary.setTotalStars(storeTotalStars);
		reviewSummary.setAverageStars(averageStars);
		return repository.save(reviewSummary);
	}

	/**
	 * Calculates the average stars of a store.
	 *
	 * @param R The average stars of the store.
	 * @param v The number of reviews that store has received.
	 * @param m The minimum number of reviews required to be considered reliable.
	 * @param C The global average stars across all stores.
	 * @return The store average stars.
	 */
	private double calculateAverageStars(double R, Long v, int m, double C) {
		return (v / (double) (v + m)) * R + (m / (double) (v + m)) * C;
	}

	/**
	 * * Calculates the global average stars across all stores.
	 *
	 * @param totalReviewsAllStores Total number of reviews across all stores.
	 * @param totalStarsAllStores   Total number of stars across all stores.
	 * @return The global average stars.
	 */
	private double calculateGlobalAverage(Long totalReviewsAllStores, Long totalStarsAllStores) {
		if (totalReviewsAllStores == 0)
			return storeReviewConfigs.getDefaultGlobalAverageStars(); // fallback if no reviews at all
		return (double) totalStarsAllStores / totalReviewsAllStores;
	}

	/**
	 * Chooses a confidence threshold based on the total number of reviews.
	 *
	 * @param totalReviews Total number of reviews for the store.
	 * @return The confidence threshold.
	 */
	private int chooseConfidenceThreshold(long totalReviews) {
		double confidenceRate = storeReviewConfigs.getConfidenceWeightFactor(); // e.g., 0.7
		if (totalReviews < storeReviewConfigs.getMinReviewsRequired()) {
			return storeReviewConfigs.getMinReviewsThreshold();
		}
		return (int) (confidenceRate * totalReviews);
	}

	@Override
	public ReviewSummary getReviewSummary(TenantId tenantId, StoreId storeId) {
		return repository.findByStoreIdAndTenantId(storeId.getValue(), tenantId.getValue());
	}
}
