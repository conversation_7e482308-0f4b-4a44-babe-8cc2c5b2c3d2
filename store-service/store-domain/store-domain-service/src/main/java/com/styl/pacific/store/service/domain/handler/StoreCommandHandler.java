/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.handler;

import com.styl.pacific.store.service.domain.dto.store.ActiveStoreCommand;
import com.styl.pacific.store.service.domain.dto.store.CreateStoreCommand;
import com.styl.pacific.store.service.domain.dto.store.DeleteStoreCommand;
import com.styl.pacific.store.service.domain.dto.store.SuspendStoreCommand;
import com.styl.pacific.store.service.domain.dto.store.UpdateStoreCommand;
import com.styl.pacific.store.service.domain.entity.Store;
import com.styl.pacific.store.service.domain.handler.helper.StoreCreateHelper;
import com.styl.pacific.store.service.domain.mapper.StoreDataMapper;
import com.styl.pacific.store.shared.http.responses.store.StoreResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class StoreCommandHandler {

	private final StoreCreateHelper storeCreateHelper;

	@Transactional
	public StoreResponse createStore(CreateStoreCommand command) {
		Store store = storeCreateHelper.persistNewStore(command);
		return StoreDataMapper.INSTANCE.storeToStoreResponse(store);
	}

	@Transactional
	public StoreResponse updateStore(UpdateStoreCommand command) {
		Store store = storeCreateHelper.updateStore(command);
		return StoreDataMapper.INSTANCE.storeToStoreResponse(store);
	}

	@Transactional
	public void deleteStore(DeleteStoreCommand command) {
		storeCreateHelper.deleteStore(command);
	}

	@Transactional
	public StoreResponse suspendStore(SuspendStoreCommand command) {
		Store store = storeCreateHelper.suspendStore(command);
		return StoreDataMapper.INSTANCE.storeToStoreResponse(store);
	}

	@Transactional
	public StoreResponse activeStore(ActiveStoreCommand command) {
		Store store = storeCreateHelper.activeStore(command);
		return StoreDataMapper.INSTANCE.storeToStoreResponse(store);
	}
}
