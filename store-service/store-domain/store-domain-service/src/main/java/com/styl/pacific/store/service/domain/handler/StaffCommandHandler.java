/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.handler;

import com.styl.pacific.store.service.domain.dto.staff.ActivateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.ArchiveStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.AssignStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.CreateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.InActivateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.ResetPinCodeCommand;
import com.styl.pacific.store.service.domain.dto.staff.UpdateStaffCommand;
import com.styl.pacific.store.service.domain.entity.Staff;
import com.styl.pacific.store.service.domain.handler.helper.StaffAssignmentHelper;
import com.styl.pacific.store.service.domain.handler.helper.StaffCommandHelper;
import com.styl.pacific.store.service.domain.handler.helper.StaffValidateHelper;
import com.styl.pacific.store.service.domain.handler.helper.StoreValidateHelper;
import com.styl.pacific.store.service.domain.mapper.StaffDataMapper;
import com.styl.pacific.store.shared.http.responses.staff.StaffResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class StaffCommandHandler {

	private final StaffCommandHelper staffCommandHelper;

	private final StaffValidateHelper staffValidateHelper;

	private final StoreValidateHelper storeValidateHelper;

	private final StaffAssignmentHelper staffAssignmentHelper;

	@Transactional
	public StaffResponse createStaff(CreateStaffCommand command) {
		staffValidateHelper.checkStaffExistedByStaffCode(command.getStaffCode(), command.getTenantId());
		if (StringUtils.hasText(command.getCardId())) {
			staffValidateHelper.checkStaffExistedByCardId(command.getCardId(), command.getTenantId());
		}
		Staff staff = staffCommandHelper.persistNewStaff(command);

		// assign staff to multi stores
		staffAssignmentHelper.assignStaff(AssignStaffCommand.builder()
				.staffId(staff.getId()
						.getValue())
				.storeIds(command.getStoreIds())
				.tenantId(command.getTenantId())
				.build());

		return StaffDataMapper.INSTANCE.staffToStaffResponse(staff);
	}

	@Transactional
	public StaffResponse updateStaff(UpdateStaffCommand command) {
		Staff staff = staffCommandHelper.updateStaff(command);

		staffAssignmentHelper.assignStaff(AssignStaffCommand.builder()
				.staffId(command.getStaffId())
				.storeIds(command.getStoreIds())
				.tenantId(command.getTenantId())
				.build());

		return StaffDataMapper.INSTANCE.staffToStaffResponse(staff);
	}

	@Transactional
	public StaffResponse inActivateStaff(InActivateStaffCommand command) {
		return staffCommandHelper.inActivateStaff(command);
	}

	@Transactional
	public StaffResponse activateStaff(ActivateStaffCommand command) {
		return staffCommandHelper.activateStaff(command);
	}

	@Transactional
	public void archiveStaff(ArchiveStaffCommand command) {
		staffCommandHelper.archiveStaff(command);
	}

	@Transactional
	public void resetPinCode(ResetPinCodeCommand command) {
		staffCommandHelper.resetPinCode(command);
	}

	public StaffResponse verifyStaff(Long tenantId, Long storeId, String staffCode, String pinCode) {
		storeValidateHelper.checkStoreExistedAndValidateActiveStatus(tenantId, storeId);
		return staffCommandHelper.verifyStaff(tenantId, storeId, staffCode, pinCode);
	}

	public StaffResponse verifyStaffCard(Long tenantId, Long storeId, String cardId) {
		storeValidateHelper.checkStoreExistedAndValidateActiveStatus(tenantId, storeId);
		return staffCommandHelper.verifyStaffCard(tenantId, storeId, cardId);
	}
}
