/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.dto.store;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 */

@Getter
@Builder
@AllArgsConstructor
public class UpdateStoreCommand {

	private final Long storeId;

	private final Long tenantId;

	private final String name;

	private final String email;

	private final String workingHour;

	private final String phoneNumber;

	private final String addressLine1;

	private final String addressLine2;

	private final String city;

	private final String country;

	private final String postalCode;

	private final String orderNumberPrefix;
}
