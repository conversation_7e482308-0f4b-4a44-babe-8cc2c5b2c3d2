/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.output.repository;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.store.service.domain.dto.cashfloat.FindCashFloatsQuery;
import com.styl.pacific.store.service.domain.entity.CashFloat;
import com.styl.pacific.store.shared.http.requests.cashfloat.GetCashFloatQuery;
import java.util.Optional;

/**
 * <AUTHOR>
 *
 */
public interface CashFloatRepository {

	CashFloat save(CashFloat cashFloat);

	Optional<CashFloat> getCashFloat(GetCashFloatQuery query);

	Paging<CashFloat> findCashFloat(FindCashFloatsQuery query);
}
