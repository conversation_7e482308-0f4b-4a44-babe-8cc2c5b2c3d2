/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.utils;

import com.styl.pacific.store.service.domain.exception.StoreDomainException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.HexFormat;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR>
 */

public class SHACryptographic {

	private SHACryptographic() {
	}

	public static String getHashPassword(String pinCode, byte[] salt) {
		try {
			MessageDigest digest = MessageDigest.getInstance("SHA-256");
			digest.update(salt);
			byte[] byteOfCodeToHash = digest.digest(pinCode.getBytes(StandardCharsets.UTF_8));
			return formatHex(byteOfCodeToHash);
		} catch (NoSuchAlgorithmException e) {
			throw new StoreDomainException("Error while hashing password", e);
		}
	}

	public static String getSignatureHMAC256(String key, String data) {
		try {
			Mac sha256HMAC = Mac.getInstance("HmacSHA256");
			SecretKeySpec secretKey = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");
			sha256HMAC.init(secretKey);
			return Base64.getEncoder()
					.encodeToString((sha256HMAC.doFinal(data.getBytes("UTF-8"))));
		} catch (NoSuchAlgorithmException | UnsupportedEncodingException | InvalidKeyException e) {
			throw new StoreDomainException("Error while signature ", e);
		}
	}

	public static byte[] generateSalt() {
		SecureRandom random = new SecureRandom();
		byte[] salt = new byte[16];
		random.nextBytes(salt);
		return salt;
	}

	public static String formatHex(byte[] byteArr) {
		return HexFormat.of()
				.formatHex(byteArr);
	}
}
