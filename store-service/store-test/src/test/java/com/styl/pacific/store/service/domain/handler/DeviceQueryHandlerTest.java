/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.store.service.domain.dto.device.DevicesFilterQuery;
import com.styl.pacific.store.service.domain.dto.device.FindDevicesQuery;
import com.styl.pacific.store.service.domain.entity.Device;
import com.styl.pacific.store.service.domain.handler.helper.DeviceQueryHelper;
import com.styl.pacific.store.shared.http.enums.DeviceStatus;
import com.styl.pacific.store.shared.http.enums.DeviceType;
import com.styl.pacific.store.shared.http.requests.device.GetDeviceQuery;
import com.styl.pacific.store.shared.http.responses.device.DeviceResponse;
import com.styl.pacific.store.shared.http.responses.device.ListDevicesResponse;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class DeviceQueryHandlerTest {

	@Mock
	DeviceQueryHelper deviceQueryHelper;

	@InjectMocks
	DeviceQueryHandler queryDeviceHandler;

	@Test
	void shouldReturnDeviceResponse_whenGetDevice() {
		// Arrange
		Device device = Device.builder()
				.id("123456")
				.storeId(new StoreId(123456L))
				.model("model1")
				.serialNumber("serialNumber123")
				.status(DeviceStatus.ACTIVE)
				.tenantId(new TenantId(12345L))
				.type(DeviceType.POS)
				.build();
		when(deviceQueryHelper.getDevice(any())).thenReturn(device);
		// Act
		DeviceResponse result = queryDeviceHandler.getDevice(new GetDeviceQuery(device.getTenantId()
				.getValue(), device.getStoreId()
						.getValue(), device.getId()));
		// Assert
		assertEquals(device.getId(), result.getDeviceId());
		assertEquals(device.getTenantId()
				.getValue(), result.getTenantId());
		assertEquals(device.getStatus(), result.getStatus());
		assertEquals(device.getStoreId()
				.getValue(), result.getStoreId());
		assertEquals(device.getModel(), result.getModel());
		assertEquals(device.getType(), result.getType());
		assertEquals(device.getSerialNumber(), result.getSerialNumber());
	}

	@Test
	void testFindDevicesThenReturnListDevicesResponse() {
		// Arrange
		DevicesFilterQuery filterQuery = DevicesFilterQuery.builder()
				.tenantId(12345L)
				.serialNumber("serialNumber123")
				.model("model1")
				.statuses(List.of(DeviceStatus.ACTIVE))
				.type(DeviceType.POS)
				.build();
		FindDevicesQuery findDevicesQuery = new FindDevicesQuery(filterQuery, 10, 0, "DESC", List.of("id"));
		DeviceResponse device1 = DeviceResponse.builder()
				.deviceId("123456")
				.storeId(123456L)
				.model("model1")
				.serialNumber("serialNumber123")
				.status(DeviceStatus.ACTIVE)
				.tenantId(12345L)
				.type(DeviceType.POS)
				.build();

		DeviceResponse device2 = DeviceResponse.builder()
				.deviceId("1234567")
				.storeId(123456L)
				.model("model1")
				.serialNumber("serialNumber123")
				.status(DeviceStatus.ACTIVE)
				.tenantId(12345L)
				.type(DeviceType.POS)
				.build();

		DeviceResponse device3 = DeviceResponse.builder()
				.deviceId("12345678")
				.storeId(123456L)
				.model("model1")
				.serialNumber("serialNumber123")
				.status(DeviceStatus.ACTIVE)
				.tenantId(12345L)
				.type(DeviceType.POS)
				.build();
		ListDevicesResponse listDeviceResponse = new ListDevicesResponse(List.of(device1, device2, device3), 3, 1, 0,
				List.of("type"));

		when(deviceQueryHelper.findDevices(any(FindDevicesQuery.class))).thenReturn(listDeviceResponse);
		// Act
		ListDevicesResponse result = queryDeviceHandler.findDevices(findDevicesQuery);
		// Assert
		assertEquals(3, result.getContent()
				.size());
		assertEquals(0, result.getPage());
		assertEquals(List.of("type"), result.getSort());
		assertEquals(3, result.getTotalElements());
		assertEquals(1, result.getTotalPages());
	}
}
