/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.handler.helper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.styl.pacific.domain.enums.StoreStatus;
import com.styl.pacific.domain.valueobject.Address;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.store.service.domain.StoreDomainCore;
import com.styl.pacific.store.service.domain.dto.store.CreateStoreCommand;
import com.styl.pacific.store.service.domain.dto.store.DeleteStoreCommand;
import com.styl.pacific.store.service.domain.dto.store.UpdateStoreCommand;
import com.styl.pacific.store.service.domain.entity.Store;
import com.styl.pacific.store.service.domain.exception.StoreNotFoundException;
import com.styl.pacific.store.service.domain.output.repository.StoreRepository;
import com.styl.pacific.store.shared.http.requests.store.GetStoreQuery;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class StoreCommandHelperTest {

	@InjectMocks
	StoreCreateHelper storeCreateHelper;

	@Mock
	StoreDomainCore storeDomainCore;

	@Mock
	StoreRepository storeRepository;

	@Test
	void shouldReturnStore_whenCreateStore() {
		// Arrange
		CreateStoreCommand createStoreCommand = CreateStoreCommand.builder()
				.tenantId(1L)
				.name("Test Store")
				.email("<EMAIL>")
				.phoneNumber("1234567890")
				.orderNumberPrefix("abcd1234")
				.addressLine1("123 Test St")
				.addressLine2("Apt 4")
				.city("Test City")
				.country("Test Country")
				.postalCode("12345")
				.build();

		Store store = Store.builder()
				.email(createStoreCommand.getEmail())
				.name(createStoreCommand.getName())
				.id(new StoreId(1L))
				.status(StoreStatus.PENDING)
				.phoneNumber(createStoreCommand.getPhoneNumber())
				.address(Address.builder()
						.addressLine1(createStoreCommand.getAddressLine1())
						.addressLine2(createStoreCommand.getAddressLine2())
						.city(createStoreCommand.getCity())
						.country(createStoreCommand.getCountry())
						.postalCode(createStoreCommand.getPostalCode())
						.build())
				.build();
		when(storeRepository.save(any(Store.class))).thenReturn(store);
		// Act
		Store storeResponse = storeCreateHelper.persistNewStore(createStoreCommand);
		// Assert
		assertNotNull(storeResponse);
		verify(storeDomainCore, times(1)).initDataStore(any(Store.class));
		verify(storeDomainCore, times(1)).pending(any(Store.class));
		assertEquals(store.getName(), storeResponse.getName());
		assertEquals(store.getEmail(), storeResponse.getEmail());
		assertEquals(store.getStatus(), storeResponse.getStatus());
		assertEquals(store.getPhoneNumber(), storeResponse.getPhoneNumber());
		assertEquals(store.getOrderNumberPrefix(), storeResponse.getOrderNumberPrefix());
		assertEquals(store.getAddress()
				.getAddressLine1(), storeResponse.getAddress()
						.getAddressLine1());
		assertEquals(store.getAddress()
				.getAddressLine2(), storeResponse.getAddress()
						.getAddressLine2());
		assertEquals(store.getAddress()
				.getPostalCode(), storeResponse.getAddress()
						.getPostalCode());
		assertEquals(store.getAddress()
				.getCity(), storeResponse.getAddress()
						.getCity());
		assertEquals(store.getAddress()
				.getCountry(), storeResponse.getAddress()
						.getCountry());
	}

	@Test
	void shouldThrowException_whenUpdateStoreNotFound() {
		// Arrange
		UpdateStoreCommand updateStoreCommand = UpdateStoreCommand.builder()
				.storeId(1L)
				.name("Updated Store")
				.email("<EMAIL>")
				.phoneNumber("76868686886")
				.orderNumberPrefix("abcd1234")
				.addressLine1("456 Updated St")
				.addressLine2("Apt 5")
				.city("Updated City")
				.country("Updated Country")
				.postalCode("67676767")
				.build();

		when(storeRepository.getStore(any(GetStoreQuery.class))).thenReturn(Optional.empty());
		// Act
		StoreNotFoundException exception = assertThrows(StoreNotFoundException.class, () -> storeCreateHelper
				.updateStore(updateStoreCommand));
		// Assert
		assertTrue(exception.getMessage()
				.contains("Store not found with id: " + 1L));
	}

	@Test
	void shouldReturnStore_whenUpdateStore() {
		// Arrange
		UpdateStoreCommand updateStoreCommand = UpdateStoreCommand.builder()
				.tenantId(1L)
				.storeId(1L)
				.name("Test Store")
				.email("<EMAIL>")
				.phoneNumber("1234567890")
				.orderNumberPrefix("abcd1234")
				.addressLine1("123 Test St")
				.addressLine2("Apt 4")
				.city("Test City")
				.country("Test Country")
				.postalCode("12345")
				.build();

		Store store = Store.builder()
				.email(updateStoreCommand.getEmail())
				.name(updateStoreCommand.getName())
				.id(new StoreId(1L))
				.phoneNumber(updateStoreCommand.getPhoneNumber())
				.orderNumberPrefix(updateStoreCommand.getOrderNumberPrefix())
				.status(StoreStatus.ACTIVE)
				.address(Address.builder()
						.addressLine1(updateStoreCommand.getAddressLine1())
						.addressLine2(updateStoreCommand.getAddressLine2())
						.city(updateStoreCommand.getCity())
						.country(updateStoreCommand.getCountry())
						.postalCode(updateStoreCommand.getPostalCode())
						.build())
				.build();
		when(storeRepository.getStore(any(GetStoreQuery.class))).thenReturn(Optional.of(store));
		when(storeRepository.save(any(Store.class))).thenReturn(store);
		// Act
		Store storeResponse = storeCreateHelper.updateStore(updateStoreCommand);
		// Assert
		assertNotNull(storeResponse);
		verify(storeDomainCore, times(1)).updateTime(any(Store.class));
		assertEquals(store.getName(), storeResponse.getName());
		assertEquals(store.getEmail(), storeResponse.getEmail());
		assertEquals(store.getStatus(), storeResponse.getStatus());
		assertEquals(store.getPhoneNumber(), storeResponse.getPhoneNumber());
		assertEquals(store.getOrderNumberPrefix(), storeResponse.getOrderNumberPrefix());
		assertEquals(store.getAddress()
				.getAddressLine1(), storeResponse.getAddress()
						.getAddressLine1());
		assertEquals(store.getAddress()
				.getAddressLine2(), storeResponse.getAddress()
						.getAddressLine2());
		assertEquals(store.getAddress()
				.getPostalCode(), storeResponse.getAddress()
						.getPostalCode());
		assertEquals(store.getAddress()
				.getCity(), storeResponse.getAddress()
						.getCity());
		assertEquals(store.getAddress()
				.getCountry(), storeResponse.getAddress()
						.getCountry());
	}

	@Test
	void testDeleteStoreShouldOk() {
		// Arrange
		Store store = Store.builder()
				.email("<EMAIL>")
				.name("name store")
				.id(new StoreId(1L))
				.phoneNumber("123456789")
				.orderNumberPrefix("abcd1234")
				.address(Address.builder()
						.addressLine1("line1")
						.addressLine2("line2")
						.city("HCM")
						.country("VN")
						.postalCode("84")
						.build())
				.build();
		when(storeRepository.getStore(any(GetStoreQuery.class))).thenReturn(Optional.of(store));

		// Act
		storeCreateHelper.deleteStore(DeleteStoreCommand.builder()
				.storeId(1L)
				.tenantId(1L)
				.build());
		// Assert
		verify(storeDomainCore, times(1)).terminate(any(Store.class));
		verify(storeDomainCore, times(1)).updateTime(any(Store.class));
		verify(storeRepository, times(1)).save(any(Store.class));
	}

	@Test
	void shouldThrowException_whenDeleteStore() {
		// Arrange
		DeleteStoreCommand deleteStoreCommand = DeleteStoreCommand.builder()
				.tenantId(1L)
				.storeId(1L)
				.build();

		when(storeRepository.getStore(any(GetStoreQuery.class))).thenReturn(Optional.empty());
		// Act
		StoreNotFoundException exception = assertThrows(StoreNotFoundException.class, () -> storeCreateHelper
				.deleteStore(deleteStoreCommand));
		// Assert
		assertTrue(exception.getMessage()
				.contains("Store is not found with id: " + 1L));
	}
}
