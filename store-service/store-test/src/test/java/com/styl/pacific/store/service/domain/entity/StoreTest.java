/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.entity;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.domain.enums.StoreStatus;
import com.styl.pacific.domain.valueobject.Address;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class StoreTest {

	private Store store;

	@BeforeEach
	public void setUp() {

		store = Store.builder()

				.id(new StoreId(1L))

				.tenantId(new TenantId(1L))

				.name("store name")

				.email("<EMAIL>")

				.phoneNumber("0908287376")
				.orderNumberPrefix("abc12345")
				.status(StoreStatus.ACTIVE)

				.address(Address.builder()
						.addressLine1("addressLine1")
						.addressLine2("addressLine2")
						.city("HCM")
						.country("VN")
						.postalCode("123")
						.build())
				.build();
	}

	@Test
	void shouldReturnStoreId_whenGetStoreId() {
		// Act
		StoreId storeId = store.getId();
		// Assert
		assertEquals(new StoreId(1L), storeId);
	}

	@Test
	void shouldReturnTenantId_whenGetTenantId() {

		Store store1 = Store.builder()

				.id(new StoreId(1L))

				.tenantId(new TenantId(1L))

				.name("store name")

				.email("<EMAIL>")
				.orderNumberPrefix("abc12345")
				.phoneNumber("0908287376")

				.address(Address.builder()
						.addressLine1("addressLine1")
						.addressLine2("addressLine2")
						.city("HCM")
						.country("VN")
						.postalCode("123")
						.build())
				.build();
		// Act
		TenantId tenantId = store1.getTenantId();
		// Assert
		assertEquals(new TenantId(1L), tenantId);
	}

	@Test
	void shouldReturnName_whenGetName() {
		// Act
		String name = store.getName();
		// Assert
		assertEquals("store name", name);
	}

	@Test
	void shouldReturnEmail_whenGetEmail() {
		// Act
		String email = store.getEmail();
		// Assert
		assertEquals("<EMAIL>", email);
	}

	@Test
	void shouldReturnPhoneNumber_whenGetPhoneNumber() {
		// Act
		String phoneNumber = store.getPhoneNumber();
		// Assert
		assertEquals("0908287376", phoneNumber);
	}

	@Test
	void shouldReturnOrderNumberPrefix_whenGetOrderNumberPrefix() {
		// Act
		String orderNumberPrefix = store.getOrderNumberPrefix();
		// Assert
		assertEquals("abc12345", orderNumberPrefix);
	}

	@Test
	void shouldReturnAddress_whenGetAddress() {
		// Act
		Address address = store.getAddress();
		// Assert
		assertEquals("addressLine1", address.getAddressLine1());
		assertEquals("addressLine2", address.getAddressLine2());
		assertEquals("VN", address.getCountry());
		assertEquals("HCM", address.getCity());
		assertEquals("123", address.getPostalCode());
	}

	@Test
	void shouldReturnStatus_whenGetStatus() {
		// Act
		StoreStatus status = store.getStatus();
		// Assert
		assertEquals(StoreStatus.ACTIVE, status);
	}

	@Test
	void shouldTrue_whenEqual() {
		// Arrange
		Store store2 = Store.builder()

				.id(new StoreId(1L))

				.tenantId(new TenantId(1L))

				.status(StoreStatus.ACTIVE)

				.name("store name")

				.email("<EMAIL>")
				.orderNumberPrefix("abc12345")
				.phoneNumber("0908287376")

				.address(Address.builder()
						.addressLine1("addressLine1")
						.addressLine2("addressLine2")
						.city("HCM")
						.country("VN")
						.postalCode("123")
						.build())
				.build();

		// Assert
		assertThat(store.equals(store2)).isTrue();
	}

	@Test
	void shouldFalse_whenEqual() {
		// Arrange
		Store store2 = Store.builder()

				.id(new StoreId(2L))

				.tenantId(new TenantId(2L))

				.status(StoreStatus.ACTIVE)

				.name("store name")

				.email("<EMAIL>")
				.orderNumberPrefix("abc12345")
				.phoneNumber("0908287376")

				.address(Address.builder()
						.addressLine1("addressLine1")
						.addressLine2("addressLine2")
						.city("HCM")
						.country("VN")
						.postalCode("123")
						.build())
				.build();

		// Assert
		assertThat(store.equals(store2)).isFalse();
	}
}
