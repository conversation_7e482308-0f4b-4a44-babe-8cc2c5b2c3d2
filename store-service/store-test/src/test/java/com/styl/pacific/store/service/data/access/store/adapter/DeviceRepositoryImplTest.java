/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.data.access.store.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.common.test.BaseDataJpaTest;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.kafka.consumer.config.KafkaConsumerConfig;
import com.styl.pacific.store.service.config.IntegrationTestConfiguration;
import com.styl.pacific.store.service.domain.dto.device.DevicesFilterQuery;
import com.styl.pacific.store.service.domain.dto.device.FindDevicesQuery;
import com.styl.pacific.store.service.domain.entity.Device;
import com.styl.pacific.store.service.domain.output.repository.DeviceRepository;
import com.styl.pacific.store.shared.http.enums.DeviceStatus;
import com.styl.pacific.store.shared.http.enums.DeviceType;
import com.styl.pacific.store.shared.http.requests.device.GetDeviceQuery;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.test.context.ContextConfiguration;

/**
 * <AUTHOR>
 */
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
@MockBeans({ @MockBean(KafkaConsumerConfig.class) })
class DeviceRepositoryImplTest extends BaseDataJpaTest {

	@Autowired
	DeviceRepository deviceRepository;

	private final Long STORE_ID = 123456L;
	private final Long TENANT_ID = 12345L;

	@Test
	void testSaveDeviceShouldOK() {
		// Arrange
		Device device = Device.builder()
				.id("123456")
				.storeId(new StoreId(STORE_ID))
				.model("model1")
				.serialNumber("serialNumber123")
				.status(DeviceStatus.ACTIVE)
				.tenantId(new TenantId(TENANT_ID))
				.type(DeviceType.POS)
				.build();
		// Act
		Device deviceResponse = deviceRepository.save(device);
		// Assert
		assertEquals(device.getStoreId(), deviceResponse.getStoreId());
		assertEquals(device.getId(), deviceResponse.getId());
		assertEquals(device.getType(), deviceResponse.getType());
		assertEquals(device.getModel(), deviceResponse.getModel());
		assertEquals(device.getModel(), deviceResponse.getModel());
		assertEquals(device.getStatus(), deviceResponse.getStatus());
		assertEquals(device.getTenantId(), deviceResponse.getTenantId());
	}

	@Test
	void shouldReturnDevice_whenGetDevice() {
		// Arrange
		Device device = Device.builder()
				.id("123456")
				.storeId(new StoreId(STORE_ID))
				.model("model1")
				.serialNumber("serialNumber123")
				.status(DeviceStatus.ACTIVE)
				.tenantId(new TenantId(TENANT_ID))
				.type(DeviceType.POS)
				.build();
		deviceRepository.save(device);
		// Act
		Optional<Device> deviceResponse = deviceRepository.getDevice(GetDeviceQuery.builder()
				.deviceId("123456")
				.tenantId(TENANT_ID)
				.storeId(STORE_ID)
				.build());
		// Assert
		assertTrue(deviceResponse.isPresent());
		assertEquals(device.getStoreId(), deviceResponse.get()
				.getStoreId());
		assertEquals(device.getType(), deviceResponse.get()
				.getType());
		assertEquals(device.getModel(), deviceResponse.get()
				.getModel());
		assertEquals(device.getModel(), deviceResponse.get()
				.getModel());
		assertEquals(device.getStatus(), deviceResponse.get()
				.getStatus());
		assertEquals(device.getTenantId(), deviceResponse.get()
				.getTenantId());
	}

	@Test
	void shouldReturnEmpty_whenGetDevice() {
		// Arrange
		Device device = Device.builder()
				.id("123456")
				.storeId(new StoreId(STORE_ID))
				.model("model1")
				.serialNumber("serialNumber123")
				.status(DeviceStatus.ACTIVE)
				.tenantId(new TenantId(TENANT_ID))
				.type(DeviceType.POS)
				.build();
		deviceRepository.save(device);
		// Act
		Optional<Device> deviceResponse = deviceRepository.getDevice(GetDeviceQuery.builder()
				.deviceId("12345678")
				.tenantId(TENANT_ID)
				.storeId(STORE_ID)
				.build());
		// Assert
		assertTrue(deviceResponse.isEmpty());
	}

	@Test
	void testPagingDeviceShouldOK() {
		// create 2 device
		Device device1 = Device.builder()
				.id("123456")
				.storeId(new StoreId(STORE_ID))
				.model("model1")
				.serialNumber("serialNumber123")
				.status(DeviceStatus.ACTIVE)
				.tenantId(new TenantId(TENANT_ID))
				.type(DeviceType.KDS)
				.build();
		deviceRepository.save(device1);

		Device device2 = Device.builder()
				.id("12345678")
				.storeId(new StoreId(STORE_ID))
				.model("model1")
				.serialNumber("serialNumber123")
				.status(DeviceStatus.ACTIVE)
				.tenantId(new TenantId(TENANT_ID))
				.type(DeviceType.KDS)
				.build();
		deviceRepository.save(device2);

		// Create 100 device
		for (int i = 0; i < 100; i++) {
			Device device = Device.builder()
					.id("12345678" + i)
					.storeId(new StoreId(STORE_ID))
					.model("model1")
					.serialNumber("serialNumber978")
					.status(DeviceStatus.BROKEN)
					.tenantId(new TenantId(TENANT_ID))
					.type(DeviceType.POS)
					.build();
			deviceRepository.save(device);
		}

		DevicesFilterQuery filterQuery1 = DevicesFilterQuery.builder()
				.serialNumber("serialNumber123")
				.tenantId(TENANT_ID)
				.storeId(STORE_ID)
				.build();
		// act
		Paging<Device> result1 = deviceRepository.findDevices(new FindDevicesQuery(filterQuery1, 10, 0, "asc", List.of(
				"type")));

		// assert
		assertNotNull(result1);
		assertEquals(2, result1.getContent()
				.size());
		assertEquals(2, result1.getTotalElements());
		assertEquals(1, result1.getTotalPages());
		assertEquals(0, result1.getPage());
		assertEquals(1, result1.getSort()
				.size());
		for (int i = 0; i < 1; i++) {
			Assertions.assertTrue(result1.getContent()
					.get(i)
					.getType()
					.name()
					.compareToIgnoreCase(result1.getContent()
							.get(i + 1)
							.getType()
							.name()) <= 0);
		}

		DevicesFilterQuery filterQuery2 = DevicesFilterQuery.builder()
				.storeId(STORE_ID)
				.tenantId(TENANT_ID)
				.statuses(List.of(DeviceStatus.BROKEN))
				.build();
		// act
		Paging<Device> result2 = deviceRepository.findDevices(new FindDevicesQuery(filterQuery2, 10, 0, "asc", List.of(
				"type")));
		assertNotNull(result2);
		assertEquals(10, result2.getContent()
				.size());
		assertEquals(100, result2.getTotalElements());
		assertEquals(10, result2.getTotalPages());
		assertEquals(0, result2.getPage());
		assertEquals(1, result2.getSort()
				.size());

		DevicesFilterQuery filterQuery3 = DevicesFilterQuery.builder()
				.storeId(STORE_ID)
				.tenantId(TENANT_ID)
				.type(DeviceType.KDS)
				.build();
		// act
		Paging<Device> result3 = deviceRepository.findDevices(new FindDevicesQuery(filterQuery3, 10, 0, "asc", List.of(
				"type")));
		assertNotNull(result3);
		assertEquals(2, result3.getContent()
				.size());
		assertEquals(2, result3.getTotalElements());
		assertEquals(1, result3.getTotalPages());
		assertEquals(0, result3.getPage());
		assertEquals(1, result3.getSort()
				.size());

		DevicesFilterQuery filterQuery4 = DevicesFilterQuery.builder()
				.storeId(STORE_ID)
				.tenantId(TENANT_ID)
				.statuses(List.of(DeviceStatus.BROKEN))
				.build();

		// act
		Paging<Device> result4 = deviceRepository.findDevices(new FindDevicesQuery(filterQuery4, 10, 5, "asc", List.of(
				"type")));
		assertNotNull(result4);
		assertEquals(10, result4.getContent()
				.size());
		assertEquals(100, result4.getTotalElements());
		assertEquals(5, result4.getPage());
		assertEquals(10, result4.getTotalPages());

		DevicesFilterQuery filterQuery5 = DevicesFilterQuery.builder()
				.storeId(STORE_ID)
				.tenantId(TENANT_ID)
				.statuses(List.of(DeviceStatus.INACTIVE))
				.build();
		// act
		Paging<Device> result5 = deviceRepository.findDevices(new FindDevicesQuery(filterQuery5, 10, 0, "asc", List.of(
				"type")));
		assertNotNull(result5);
		assertEquals(0, result5.getContent()
				.size());
		assertEquals(0, result5.getTotalElements());
		assertEquals(0, result5.getTotalPages());
	}
}
