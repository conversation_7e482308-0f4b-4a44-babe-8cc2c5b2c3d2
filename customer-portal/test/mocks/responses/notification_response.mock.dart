final Map<String, dynamic> mockNotificationResponse = {
  "content": [
    {
      "id": "9007199254740991",
      "tenantId": "9007199254740991",
      "userId": "9007199254740991",
      "read": false,
      "action": "string",
      "content": "string 1",
      "data": {"additionalProp1": "string", "additionalProp2": "string", "additionalProp3": "string"},
      "source": "string 2",
      "title": "string 3",
      "createdAt": 1744881290000
    }
  ],
  "lastEvaluatedKey": {
    "partition_key": {"s": "string", "n": "string"},
    "id": {"s": "string", "n": "string"},
  }
};

final Map<String, dynamic> mockNotiTrackingResponse = {
  "tenantId": "string",
  "userId": "string",
  "hasNewNotification": true
};
