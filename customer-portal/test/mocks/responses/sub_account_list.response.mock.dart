final Map<String, dynamic> mockSubAccountListResponse = {
  "content": [
    {
      "id": "string",
      "subUser": {
        "id": "string",
        "ssoId": "string",
        "externalId": "string",
        "firstName": "string",
        "lastName": "string",
        "email": "string",
        "avatar": {"path": "string", "url": "string"},
        "userType": "SYSTEM_ADMIN",
        "userStatus": "CREATED",
        "realmId": "string",
        "phoneNumber": "string",
        "userGroup": {
          "id": "string",
          "tenantId": "string",
          "path": "string",
          "groupName": "string",
          "parent": null,
          "description": "string",
          "createdAt": 0,
          "updatedAt": 0
        },
        "userNonCompletedActions": ["KEYCLOAK_REGISTRATION"],
        "createdAt": 0,
        "updatedAt": 0
      },
      "subAccountStatus": "PENDING",
      "createdAt": 0,
      "updatedAt": 0
    }
  ],
  "totalElements": 0,
  "totalPages": 0,
  "page": 0,
  "sort": ["string"]
};
