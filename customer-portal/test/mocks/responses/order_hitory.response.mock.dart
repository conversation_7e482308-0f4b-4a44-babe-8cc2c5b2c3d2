final Map<String, dynamic> mockOrderHistoryResponse = {
  "content": [
    {
      "version": 1,
      "id": "128823834052476928",
      "tenantId": "115697404415799296",
      "storeId": "116157484682370048",
      "orderNumber": "1730713995475",
      "staffCode": null,
      "staffName": null,
      "issuerId": "116039188868036608",
      "customerId": "116039188868036608",
      "customerEmail": "<EMAIL>",
      "customerName": "Toan <PERSON>ui",
      "status": "COLLECTED",
      "type": "PRE_ORDER",
      "preOrderId": "128823834018922496",
      "mealTimeId": "121129752049380352",
      "mealTime": {"id": "121129752049380352", "name": "Lunch"},
      "collectionDate": "2024-11-08",
      "collectedAt": null,
      "collectorStaffCode": null,
      "collectorStaffName": null,
      "collectorName": null,
      "lineItems": [
        {
          "id": "128823834077642752",
          "orderId": "128823834052476928",
          "productId": "122554368580054016",
          "preOrderMenuItemId": "122555884407538752",
          "metadata": {
            "name": "Milk Tea",
            "healthierChoice": null,
            "category": {
              "name": "Cat 2",
              "icon": {
                "path": "pacific:image/2024/10/18/1b36ec6a5f3d800-medium.jpg",
                "url":
                    "https://*************:31805/pacific/image/2024/10/18/1b36ec6a5f3d800-medium.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241107T044308Z&X-Amz-SignedHeaders=host&X-Amz-Credential=pacific-readwrite%2F20241107%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Expires=300&X-Amz-Signature=a468e4498a65875d54bd6ff6f22a674d55365f2cf9f79e7969b92d90d3566e56"
              }
            },
            "sku": "MILKTEAH7467",
            "barcode": null,
            "ingredients": null,
            "unitPrice": 500,
            "listingPrice": 550,
            "options": null
          },
          "productName": "Milk Tea",
          "option": "option: item",
          "quantity": 1,
          "note": "test test test",
          "reversible": null,
          "unitPrice": 500,
          "optionPrice": null,
          "totalDiscount": 0,
          "totalAmount": 500
        },
        {
          "id": "128823834077642753",
          "orderId": "128823834052476928",
          "productId": "122554368580054016",
          "preOrderMenuItemId": "122555884407538752",
          "metadata": {
            "name": "Test",
            "healthierChoice": null,
            "category": {
              "name": "Cat 2",
              "icon": {
                "path": "pacific:image/2024/10/18/1b36ec6a5f3d800-medium.jpg",
                "url":
                    "https://*************:31805/pacific/image/2024/10/18/1b36ec6a5f3d800-medium.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241107T044308Z&X-Amz-SignedHeaders=host&X-Amz-Credential=pacific-readwrite%2F20241107%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Expires=300&X-Amz-Signature=a468e4498a65875d54bd6ff6f22a674d55365f2cf9f79e7969b92d90d3566e56"
              }
            },
            "sku": "MILKTEAH7467",
            "barcode": null,
            "ingredients": null,
            "unitPrice": 500,
            "listingPrice": 550,
            "options": null
          },
          "productName": "Test",
          "option": null,
          "quantity": 1,
          "note": "",
          "reversible": null,
          "unitPrice": 500,
          "optionPrice": null,
          "totalDiscount": 0,
          "totalAmount": 500
        }
      ],
      "note": null,
      "taxName": "GST01",
      "taxRate": 0.1200,
      "taxInclude": true,
      "taxAmount": 54,
      "subtotalAmount": 500,
      "discountAmount": 0,
      "totalAmount": 500,
      "currency": {
        "displayName": "US Dollar",
        "numericCode": 840,
        "currencyCode": "USD",
        "symbol": "\$",
        "fractionDigits": 2
      },
      "paymentMethodId": "121487977543276544",
      "paymentMethodName": "Payment with E-Wallet",
      "paymentTransactionId": "128823840874499072",
      "paymentStatus": "PAID",
      "transactionRef": null,
      "terminalId": null,
      "paymentRef": "PRE_ORDER.128823834018922496.a7d1b4e0aa973bab40257ca2d7aeac5ac6fd6a71bb65e1505a412b5261d446f2",
      "refundable": true,
      "cancellationDueAt": 1731014636000,
      "cancelReason": null,
      "canceledAt": null,
      "expiredAt": 1731014636000,
      "createdAt": 1730713995554,
      "updatedAt": 1730713997709
    },
    {
      "version": 1,
      "id": "128823945490939904",
      "tenantId": "115697404415799296",
      "storeId": "115698396406916096",
      "orderNumber": "1730714022038",
      "staffCode": null,
      "staffName": null,
      "issuerId": "116039188868036608",
      "customerId": "116039188868036608",
      "customerEmail": "<EMAIL>",
      "customerName": "Toan Bui",
      "status": "PAID",
      "type": "PRE_ORDER",
      "preOrderId": "128823945482551296",
      "mealTimeId": "118928704165580800",
      "collectionDate": "2024-11-08",
      "collectedAt": null,
      "collectorStaffCode": null,
      "collectorStaffName": null,
      "collectorName": null,
      "lineItems": [
        {
          "id": "128823945490939904",
          "orderId": "128823945490939904",
          "productId": "116401519797567488",
          "preOrderMenuItemId": "128823593181986816",
          "metadata": {
            "name": "Product 4",
            "healthierChoice": {
              "name": "HC 2",
              "symbol": {
                "path": "pacific:image/2024/10/18/1b36cb2fe33d800-pngtree-adorable-green-tree-png-image_11593973.png",
                "url":
                    "https://*************:31805/pacific/image/2024/10/18/1b36cb2fe33d800-pngtree-adorable-green-tree-png-image_11593973.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241107T044308Z&X-Amz-SignedHeaders=host&X-Amz-Credential=pacific-readwrite%2F20241107%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Expires=300&X-Amz-Signature=ea42ab16017c39269b4f9c54e1d691a13c3ee6025bb9e9099464fef799b9806a"
              }
            },
            "category": {"name": "Category 2", "icon": null},
            "sku": "PRODUCTL2053",
            "barcode": "123456789012",
            "ingredients": null,
            "unitPrice": 20,
            "listingPrice": 12,
            "options": null
          },
          "productName": "Product 4",
          "option": null,
          "quantity": 1,
          "note": "",
          "reversible": null,
          "unitPrice": 20,
          "optionPrice": null,
          "totalDiscount": 0,
          "totalAmount": 20
        }
      ],
      "note": null,
      "taxName": "GST01",
      "taxRate": 0.1200,
      "taxInclude": true,
      "taxAmount": 2,
      "subtotalAmount": 20,
      "discountAmount": 0,
      "totalAmount": 20,
      "currency": {
        "displayName": "US Dollar",
        "numericCode": 840,
        "currencyCode": "USD",
        "symbol": "\$",
        "fractionDigits": 2
      },
      "paymentMethodId": "121487977543276544",
      "paymentMethodName": "Payment with E-Wallet",
      "paymentTransactionId": "128823946717760512",
      "paymentStatus": "PAID",
      "transactionRef": null,
      "terminalId": null,
      "paymentRef": "PRE_ORDER.128823945482551296.79cad226682f74bc15a2f680c1a75114455f1d2a92742899c8a96ef76523b40c",
      "refundable": true,
      "cancellationDueAt": 1730901722000,
      "cancelReason": null,
      "canceledAt": null,
      "expiredAt": 1730901722000,
      "createdAt": 1730714022051,
      "updatedAt": 1730714022412
    }
  ],
  "totalElements": 2,
  "totalPages": 1,
  "page": 0,
  "sort": ["id: ASC"]
};
