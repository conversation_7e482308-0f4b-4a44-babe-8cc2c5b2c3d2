import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/common/widgets/user_avatar/user_avatar.dart';
import 'package:pacific_2_customer_portal/models/sub_account.model.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';
import 'package:pacific_2_customer_portal/pages/principal_account/widgets/principal_account_desktop_item.dart';

import '../../mocks/localized_widget.dart';
import '../../mocks/responses/user_profile.response.mock.dart';
import '../../setup.config.dart';

void main() {
  setupTestEnv();

  testWidgets("PrincipalAccountDesktopItem render correctly active",
      (tester) async {
    FlutterError.onError = ignoreOverflowErrors;
    tester.view.devicePixelRatio = 1;
    tester.view.physicalSize = const Size(2200, 800);

    bool pressedOrder = false;
    bool pressedDelete = false;
    bool pressedDetails = false;
    bool pressedEdit = false;
    UserProfile profile = UserProfile.fromJson(mockUserProfileResponse);
    profile.avatar?.url = null;
    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(
        PrincipalAccountDesktopItem(
          item: SubAccount(subUser: profile, subAccountStatus: 'ACTIVE'),
          onPressOrder: () => pressedOrder = true,
          onPressDelete: () => pressedDelete = true,
          onPressDetails: () => pressedDetails = true,
          onPressEdit: () => pressedEdit = true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.byType(UserAvatar), findsOneWidget);
    expect(
        find.text(
            '${mockUserProfileResponse['firstName']} ${mockUserProfileResponse['lastName']}'),
        findsOne);
    expect(find.byType(IconButton), findsNWidgets(3));
    expect(find.text(mockUserProfileResponse['userGroup']['groupName'] ?? "-"),
        findsAny);
    expect(find.text(mockUserProfileResponse['externalId'] ?? "-"), findsAny);

    await tester.tap(find.text('View Pre-order'));
    await tester.pumpAndSettle();
    expect(pressedOrder, isTrue);

    await tester.tap(find.byType(IconButton).at(0));
    await tester.pumpAndSettle();
    expect(pressedDelete, isTrue);

    await tester.tap(find.byType(IconButton).at(1));
    await tester.pumpAndSettle();
    expect(pressedDetails, isTrue);

    await tester.tap(find.byType(IconButton).at(2));
    await tester.pumpAndSettle();
    expect(pressedEdit, isTrue);
  });

  testWidgets("PrincipalAccountDesktopItem render correctly pending",
      (tester) async {
    FlutterError.onError = ignoreOverflowErrors;
    tester.view.devicePixelRatio = 1;
    tester.view.physicalSize = const Size(2200, 800);

    bool pressedDelete = false;
    UserProfile profile = UserProfile.fromJson(mockUserProfileResponse);
    profile.avatar?.url = null;
    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(
        PrincipalAccountDesktopItem(
          item: SubAccount(subUser: profile, subAccountStatus: 'PENDING'),
          onPressOrder: () {},
          onPressDelete: () => pressedDelete = true,
          onPressDetails: () {},
          onPressEdit: () {},
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.byType(UserAvatar), findsOneWidget);
    expect(
        find.text(
            '${mockUserProfileResponse['firstName']} ${mockUserProfileResponse['lastName']}'),
        findsOne);
    expect(find.text(mockUserProfileResponse['userGroup']['groupName'] ?? "-"),
        findsNothing);
    expect(
        find.text(mockUserProfileResponse['externalId'] ?? "-"), findsNothing);
    expect(find.text('Awaiting link account acceptance'), findsOne);
    expect(find.byType(IconButton), findsNWidgets(1));

    await tester.tap(find.byType(IconButton).at(0));
    await tester.pumpAndSettle();
    expect(pressedDelete, isTrue);
  });
}
