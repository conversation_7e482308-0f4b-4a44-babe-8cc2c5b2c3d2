import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/common/widgets/card_list_edit/add_card_dialog.dart';

import '../../../mocks/localized_widget.dart';

void main() {
  testWidgets('AddCardDialog renders and validates input', (WidgetTester tester) async {
    bool onSaveCalled = false;
    bool onCloseCalled = false;
    await tester.pumpWidget(renderLocalizedWidget(
      AddCardDialog(
        onSave: (id, alias, type) => onSaveCalled = true,
        onClose: () => onCloseCalled = true,
      ),
    ));
    await tester.pumpAndSettle();

    expect(find.text('Add Card'), findsOne);

    await tester.tap(find.textContaining('Save'));
    await tester.pumpAndSettle();
    expect(onSaveCalled, isFalse);

    expect(find.byType(TextField), findsNWidgets(2));
    await tester.enterText(find.byType(TextField).at(0), '123');
    await tester.pumpAndSettle();
    await tester.enterText(find.byType(TextField).at(1), 'alias');
    await tester.pumpAndSettle();
    expect(find.text('123'), findsOne);
    expect(find.text('alias'), findsOne);

    await tester.tap(find.textContaining('Save'));
    await tester.pumpAndSettle();
    expect(onSaveCalled, isTrue);

    await tester.tap(find.textContaining('Cancel'));
    await tester.pumpAndSettle();
    expect(onCloseCalled, isTrue);
  });
}
