// coverage:ignore-file
// ignore_for_file: constant_identifier_names, non_constant_identifier_names

import 'package:pacific_2_customer_portal/models/navigation_menu_item.model.dart';

class SystemConst {
  static const ROUTE = {
    'AUTH': {
      'LOGIN': 'login',
      'LOGIN_SUCCESS': 'login-success',
      'LOGOUT': 'logout',
    },
    'HOME': {
      'MAIN': 'dashboard',
    },
    'SUB_ACCOUNT': {
      'MAIN': 'principal-account',
      'ADD': 'principal-account/add',
      'UPDATE': 'principal-account/update',
      'EDIT': 'principal-account/edit',
      'ORDERS': 'principal-account/orders',
      'BANNED_ITEMS': 'principal-account/banned-items',
    },
    'WALLET': {
      'MAIN': 'wallet',
      'TOPUP': 'wallet/topup',
      'HISTORY': 'wallet/history',
      'DETAILS': 'wallet/history/details',
    },
    'PRE_ORDER': {
      'MAIN': 'pre-order',
      'CART': 'pre-order/cart',
      'CHECKOUT': 'pre-order/checkout',
    },
    'ORDER': {
      'HISTORY': 'order/history',
    },
    'PAYMENT': {
      'HISTORY': 'payment/history',
    },
    'CONTACT': {
      'MAIN': 'contact-us',
    },
    'ACCOUNT': {
      'MAIN': 'my-account',
      'BANNED_ITEMS': 'my-account/banned-items',
    },
  };

  static final List<NavigationMenuItem> TOP_MENU = [
    NavigationMenuItem(
      id: 'home',
      titleKey: 'dashboard',
      url: '/${ROUTE['HOME']!['MAIN']!}',
    ),
    NavigationMenuItem(
      id: 'sub-account',
      titleKey: 'principalAccount',
      url: '/${ROUTE['SUB_ACCOUNT']!['MAIN']!}',
    ),
    NavigationMenuItem(
      id: 'wallet',
      titleKey: 'wallet',
      url: '/${ROUTE['WALLET']!['MAIN']!}',
    ),
    NavigationMenuItem(
      id: 'pre-order',
      titleKey: 'mealPreorder',
      url: '/${ROUTE['PRE_ORDER']!['MAIN']!}',
    ),
    NavigationMenuItem(
      id: 'orderHistory',
      titleKey: 'orderHistory',
      url: '/${ROUTE['ORDER']!['HISTORY']!}',
    ),
    NavigationMenuItem(
      id: 'paymentHistory',
      titleKey: 'paymentHistory',
      url: '/${ROUTE['PAYMENT']!['HISTORY']!}',
    ),
    NavigationMenuItem(
      id: 'contact',
      titleKey: 'contactUs',
      url: '/${ROUTE['CONTACT']!['MAIN']!}',
      icon: 'assets/images/ic-menu-contact.svg',
    ),
  ];

  static final List<NavigationMenuItem> BOTTOM_MENU = [
    NavigationMenuItem(
      id: 'account',
      titleKey: 'myAccount',
      url: '/${ROUTE['ACCOUNT']!['MAIN']!}',
      icon: 'assets/images/ic-menu-account.svg',
    ),
    NavigationMenuItem(
      id: 'logout',
      titleKey: 'logout',
      url: '/${ROUTE['AUTH']!['LOGOUT']!}',
      icon: 'assets/images/ic-menu-logout.svg',
    ),
  ];

  static const API = {
    'TENANT': {
      'INFO': '/api/tenant/tenants/info',
      'REALM': '/api/tenant/tenants/{id}/keycloak/realm.json',
      'TAX': '/api/tenant/taxes/currentTax',
      'T&C': '/api/tenant/agreement-terms',
      'ANNOUNCEMENT': '/api/tenant/announcement',
      'INTERCOM_TOKEN': '/api/tenant/communication/intercom/token',
      'BANNER': '/api/tenant/banner',
    },
    'USER': {
      'PROFILE': '/api/user/users/{userId}/profile',
      'MAIN_PROFILE': '/api/user/customers/{userId}/alternative-access/user/{alternativeUserId}',
      'SUB_ACCOUNT_LIST': '/api/user/users/{userId}/sub-accounts',
      'UPDATE_PROFILE': '/api/user/users/profile',
      'CARDS_INFO': '/api/user/users/{userId}/cards',
      'CARDS_ADD': '/api/user/users/{userId}/cards',
      'CARDS_DELETE': '/api/user/users/{userId}/cards/{cardId}',
      'CARDS_UPDATE': '/api/user/users/{userId}/cards/{cardId}',
      'SUB_ACCOUNT_UPDATE': '/api/user/sub-accounts/{userId}',
      'PREFERENCE': '/api/user/users/{userId}/preferences',
      'FEEDBACK': '/api/notification/feedbacks',
      'REQUIRED_ACTION_CHECK': '/api/user/users/{userId}/required-actions',
      'REQUIRED_ACTION_REPLY': '/api/user/users/{userId}/required-actions/{userRequiredActionId}/reply',
      'BANNED_ITEMS': '/api/user/users/{userId}/banned-items',
    },
    'PRE_ORDER': {
      'MENU_ITEMS': '/api/order/assignments/menu-items',
      'MEAL_TIMES': '/api/order/mealtimes',
      'CALENDAR_ORDER': '/api/order/orders/daily-counts',
      'CALENDAR_STATUS': '/api/order/assignments/menu-items/daily-counts',
      'SUMMARY': '/api/order/pre-orders/orders/summary',
      'CHECKOUT': '/api/order/pre-orders/orders/place',
      'HISTORY': '/api/order/pre-orders/orders',
      'CANCEL': '/api/order/pre-orders/orders/{id}/cancel',
      'PRE_ORDER_MENU_ITEMS': '/api/order/pre-orders/menus/{menuId}/items',
      'PRE_ORDER_MENU': '/api/order/pre-orders/menus/{id}',
      'DETAILS': '/api/order/pre-orders/orders/{id}',
    },
    'ORDER': {
      'DETAILS': '/api/order/orders/{id}',
      'CANCEL': '/api/order/orders/cancel',
      'LIST': '/api/order/orders',
      'SERVICE_CHARGE': '/api/order/service-charges',
    },
    'PAYMENT': {
      'METHODS': '/api/payment/methods',
      'METHOD': '/api/payment/methods/{methodId}',
      'CREATE_SESSION': '/api/payment/sessions',
      'TRANSACTION': '/api/payment/transactions/{transactionId}',
      'TRANSACTION_LIST': '/api/payment/transactions',
    },
    'PRINCIPAL_ACCOUNT': {
      'SUB_ACCOUNT_DETAIL': '/api/user/sub-accounts/{subAccountId}',
      'GROUPS': '/api/user/groups/tree',
      'SUB_ACCOUNT_ADD': '/api/user/sub-accounts/new',
      'SUB_ACCOUNT_LINK': '/api/user/sub-accounts/link',
      'SUB_ACCOUNT_DELETE': '/api/user/sub-accounts/{subAccountId}/unlink',
    },
    'ALLERGEN': {
      'GET_ALLERGEN': '/api/user/users/{userId}/allergens',
      'UPDATE_ALLERGEN': '/api/user/users/{userId}/allergens',
    },
    'UTILITY': {
      'IMAGE_UPLOAD_PRE_SIGNED': '/api/utility/files/images/upload/pre-signed-url',
      'TIMEZONE': '/api/utility/metadata/timezones',
      'DATE_FORMAT': '/api/utility/metadata/date-formats',
      'TIME_FORMAT': '/api/utility/metadata/time-formats',
    },
    'WALLET': {
      'LIST': '/api/wallet/wallets',
      'CREATE': '/api/wallet/wallets',
      'PAY': '/api/wallet/wallets/{id}/pay',
      'TRANSACTION_LIST': '/api/wallet/transactions',
      'TRANSACTION_DETAILS': '/api/wallet/transactions/{id}',
      'GET': '/api/wallet/wallets/{id}',
      'CREATE_TOPUP_SESSION': '/api/wallet/topup-sessions',
      "FIND": '/api/wallet/wallets/find',
      'DELEGATE': '/api/wallet/delegate-settings',
      'LIMIT': '/api/wallet/wallets/{walletId}/spending-limit-settings',
      'SETTINGS': '/api/wallet/settings',
      'EXPIRING': '/api/wallet/wallets/{id}/expiring',
    },
    'CATALOG': {
      'GET_ALLERGEN': '/api/catalog/allergens',
      'GET_PRODUCT': '/api/catalog/products/{id}',
      'GET_PRODUCT_LIST': '/api/catalog/products',
      'GET_STORE': '/api/store/stores/{id}',
      'GET_STORE_LIST': '/api/store/stores',
      'GET_UNIQUE_PRODUCT_NAME': '/api/catalog/products/name',
      'CATEGORY_TREE': '/api/catalog/categories/tree',
    },
    'NOTIFICATION': {
      'TRACKING': "/api/notification/notifications/tracking",
      'GET_LIST': '/api/notification/notifications',
      'READ': "/api/notification/notifications/read",
      'DELETE': '/api/notification/notifications',
    },
  };

  static const double INTERCOM_PADDING_BOTTOM = 56;
}
