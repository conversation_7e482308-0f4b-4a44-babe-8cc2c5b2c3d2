import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pacific_2_customer_portal/common/const/const.asset.dart';
import 'package:pacific_2_customer_portal/common/note_view/note_view.dart';
import 'package:pacific_2_customer_portal/common/widgets/dashed_line/dashed_line.dart';
import 'package:pacific_2_customer_portal/common/widgets/day_list/day_list.dart';
import 'package:pacific_2_customer_portal/common/widgets/menu_item_image/menu_item_image.dart';
import 'package:pacific_2_customer_portal/common/widgets/title_row/title_row_dialog.dart';
import 'package:pacific_2_customer_portal/models/cart.model.dart';
import 'package:pacific_2_customer_portal/themes/colors.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';
import 'package:pacific_2_customer_portal/utils/order_utils.dart';

class CheckoutSummaryDetails extends StatefulWidget {
  const CheckoutSummaryDetails({
    super.key,
    required this.cartAccount,
    required this.data,
    required this.getServiceCharges,
  });

  final CartAccount cartAccount;
  final List<CartItem> data;
  final Function(List<CartItem> data) getServiceCharges;

  @override
  State<CheckoutSummaryDetails> createState() => _CheckoutSummaryDetailsState();
}

class _CheckoutSummaryDetailsState extends State<CheckoutSummaryDetails> {
  late CartDate _selectedDate = getSortedDayList().first;

  List<CartDate> getSortedDayList() {
    return sortDayList([...widget.data.map((e) => e.groupCriteria.cartDate!).toSet()]);
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    int itemCount = getTotalItemCountByAccount(widget.data);
    final groupedList = getGroupedByMealTimeData(widget.data, _selectedDate);

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 10, 16, 0),
      child: Column(
        children: [
          const TitleRowDialog(titleKey: 'itemSelected', padding: EdgeInsets.zero),
          const SizedBox(height: 16),
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.cartAccount.profile.getFullName(),
                style: theme.textTheme.titleMedium!.copyWith(color: theme.colorScheme.primary),
              ),
              Text(
                '$itemCount ${FlutterI18n.plural(context, 'item.count', itemCount)}',
                style: theme.textTheme.titleMedium!.copyWith(color: theme.colorScheme.primary),
              ),
            ],
          ),
          DayList(
            dayList: getSortedDayList(),
            selectedDate: _selectedDate,
            onSelectDate: (newDate) {
              setState(() {
                _selectedDate = newDate;
              });
            },
            padding: 0,
          ),
          Expanded(
            child: ListView.separated(
              itemCount: groupedList.length,
              separatorBuilder: (context, index) => Divider(
                height: 16,
                thickness: 1,
                color: theme.colorScheme.outline,
              ),
              itemBuilder: (context, index) {
                final item = groupedList[index];
                int mealTimeItemCount = getMealTimeTotal(item.value);
                final orders = getGroupedByCartCriteriaData(item.value);

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${item.value.firstOrNull?.item.mealTime?.name}',
                            style: theme.textTheme.headlineSmall,
                          ),
                          Text(
                            '$mealTimeItemCount ${FlutterI18n.plural(context, 'item.count', mealTimeItemCount)}',
                            style: theme.textTheme.titleMedium,
                          ),
                        ],
                      ),
                    ),
                    ...orders.map((e) => _renderOrder(context, e.value)),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _renderOrder(BuildContext context, List<CartItem> data) {
    ThemeData theme = Theme.of(context);

    final serviceCharge = formatPrice(widget.getServiceCharges(data));

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SvgPicture.asset(ConstAsset.icStore, width: 16),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  data.first.storeName,
                  style: theme.textTheme.bodySmall,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 12),
              SvgPicture.asset(ConstAsset.icMoney, width: 16),
              const SizedBox(width: 4),
              Text(FlutterI18n.translate(context, 'serviceFee'), style: theme.textTheme.bodySmall),
              const SizedBox(width: 4),
              Text(serviceCharge, style: theme.textTheme.labelSmall!.copyWith(color: success)),
            ],
          ),
          const SizedBox(height: 8),
          ListView.separated(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            primary: false,
            itemCount: data.length,
            separatorBuilder: (_, __) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: DashedLine(color: theme.colorScheme.onSurfaceVariant),
            ),
            itemBuilder: (_, index) => _renderItem(context, data[index]),
          )
        ],
      ),
    );
  }

  Widget _renderItem(BuildContext context, CartItem cItem) {
    ThemeData theme = Theme.of(context);

    final optionText = cItem.getOptions();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: MenuItemImage(
                url: cItem.item.product?.images?.firstOrNull?.image?.url,
                width: 48,
                height: 40,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              '${cItem.quantity}x ',
              style: theme.textTheme.titleSmall,
            ),
            Expanded(
              child: Text(
                '${cItem.item.product?.name}',
                style: theme.textTheme.bodyMedium,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              formatPrice(cItem.getSellingPrice()),
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
        if (optionText.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            optionText,
            style: theme.textTheme.bodySmall!.copyWith(color: theme.colorScheme.onSurfaceVariant),
          ),
        ],
        if (cItem.notes.isNotEmpty) ...[
          const SizedBox(height: 8),
          NoteView(note: cItem.notes),
        ],
      ],
    );
  }
}
