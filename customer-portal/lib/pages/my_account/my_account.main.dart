import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/widgets/title_row/title_row.dart';
import 'package:pacific_2_customer_portal/pages/my_account_feedback/my_account_feedback.main.dart';
import 'package:pacific_2_customer_portal/pages/my_account_general_info/general_information.main.dart';
import 'package:pacific_2_customer_portal/pages/my_account_settings/my_account_settings.main.dart';
import "package:universal_html/html.dart" as html;

import '../../common/responsive/responsive.dart';
import '../my_account_card/card_page.dart';

class MyAccount extends StatefulWidget {
  const MyAccount({super.key});

  @override
  State<MyAccount> createState() => _MyAccountState();
}

class _MyAccountState extends State<MyAccount> {
  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Padding(
      padding: Responsive.isDesktop(context) ? const EdgeInsets.only(left: 32, right: 32) : EdgeInsets.zero,
      child: _tabView(theme),
    );
  }

  Widget _tabView(ThemeData theme) {
    return DefaultTabController(
      length: 4,
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Padding(
          padding: EdgeInsets.fromLTRB(Responsive.isDesktop(context) ? 0 : 16, 16, 16, 0),
          child: TitleRow(
            title: FlutterI18n.translate(context, 'myAccount'),
          ),
        ),
        TabBar(
          isScrollable: true,
          tabAlignment: TabAlignment.start,
          labelColor: theme.colorScheme.onSurface,
          unselectedLabelColor: theme.colorScheme.onSurfaceVariant,
          indicatorColor: theme.colorScheme.primary,
          indicatorSize: TabBarIndicatorSize.tab,
          tabs: [
            Tab(text: FlutterI18n.translate(context, 'personalInfo')),
            Tab(text: FlutterI18n.translate(context, 'card')),
            Tab(text: FlutterI18n.translate(context, 'settings')),
            Tab(text: FlutterI18n.translate(context, 'sendFeedback')),
          ],
        ),
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              html.window.location.reload();
            },
            notificationPredicate: (ScrollNotification notification) {
              return notification.depth == 1;
            },
            child: const TabBarView(
              children: [
                GeneralInformation(),
                CardPage(),
                MyAccountSettings(),
                MyAccountFeedback(),
              ],
            ),
          ),
        ),
      ]),
    );
  }
}
