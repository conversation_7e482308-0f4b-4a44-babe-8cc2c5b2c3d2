import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';
import 'package:pacific_2_customer_portal/controllers/loading.controller.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';
import 'package:pacific_2_customer_portal/models/wallet_transaction.model.dart';
import 'package:pacific_2_customer_portal/services/wallet.service.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class TransactionHistoryController extends GetxController {
  final WalletService _walletService;
  final LoadingController _loadingController = Get.find<LoadingController>();

  TransactionHistoryController(this._walletService);

  int _currentTransactionPage = 0;
  bool _canLoadMoreTransaction = true;
  bool _isLoadingTransaction = false;
  final RxList<WalletTransaction> transactionList = <WalletTransaction>[].obs;

  final Rx<TransactionHistoryFilter> filter = TransactionHistoryFilter.create(null, []).obs;

  // desktop view
  final Rxn<WalletTransaction> selectedTransaction = Rxn();

  Future<void> getTransactionHistory({
    int size = 20,
    bool isRefresh = false,
  }) async {
    if (_isLoadingTransaction) return;
    if (isRefresh) {
      _currentTransactionPage = 0;
      _canLoadMoreTransaction = true;
      selectedTransaction.value = null;
      transactionList.value = [];
    }
    if (!_canLoadMoreTransaction) return;

    _isLoadingTransaction = true;
    _loadingController.showLoading(true);
    try {
      final response = await _walletService.getTransactionList({
        'size': size,
        'page': _currentTransactionPage,
        'sortDirection': 'DESC',
        'sortFields': 'createdAt',
        'filter.customerIds': (filter.value.accounts!.isEmpty ? filter.value.allAccounts! : filter.value.accounts!)
            .map((e) => e.id)
            .join(','),
        'filter.transactionTypes': filter.value.transactionTypes!.map((item) => item.type).toList(),
        'filter.transactionCategories': filter.value.transactionCategories!.map((item) => item.category).toList(),
        'filter.fromAmount': filter.value.amountFrom,
        'filter.toAmount': filter.value.amountTo,
        if (filter.value.dateFrom != null)
          'filter.fromTime':
              getTZDateTimeFromUtc(Jiffy.parseFromDateTime(filter.value.dateFrom!).startOf(Unit.day).dateTime)
                  .millisecondsSinceEpoch,
        if (filter.value.dateTo != null)
          'filter.toTime': getTZDateTimeFromUtc(Jiffy.parseFromDateTime(filter.value.dateTo!).endOf(Unit.day).dateTime)
              .millisecondsSinceEpoch,
      });
      final Map<String, dynamic> data = Map.from(response);
      final List<dynamic> transactions = data['content'];
      transactionList.addAll(transactions.map((e) => WalletTransaction.fromJson(e)));
      _currentTransactionPage++;
      _canLoadMoreTransaction = transactions.length == size;
    } catch (e) {/** */}
    _loadingController.showLoading(false);
    _isLoadingTransaction = false;
  }

  void onFilterChanged(TransactionHistoryFilter newFitler) {
    filter.value = newFitler;
    getTransactionHistory(isRefresh: true);
  }

  void clearFilter({UserProfile? initProfilie, required List<UserProfile> allAccounts}) {
    filter.value = TransactionHistoryFilter.create(initProfilie, allAccounts);
  }

  // desktop view
  void onSelectTransaction(WalletTransaction transaction) {
    selectedTransaction.value = transaction;
    transactionList.refresh();
  }

  bool isTransactionSelected(WalletTransaction transaction) {
    return transaction.transactionId == selectedTransaction.value?.transactionId;
  }
}
