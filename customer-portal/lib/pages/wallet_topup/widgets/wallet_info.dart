import 'package:flutter/material.dart';
import 'package:pacific_2_customer_portal/models/wallet.model.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class WalletInfo extends StatelessWidget {
  const WalletInfo({super.key, required this.wallet});

  final Wallet wallet;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('#${wallet.walletId!}', style: theme.textTheme.titleSmall),
          Container(
            width: double.infinity,
            margin: const EdgeInsets.only(top: 12),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              formatPrice(wallet.balance!),
              style: theme.textTheme.headlineSmall!.copyWith(color: theme.colorScheme.primary),
            ),
          ),
        ],
      ),
    );
  }
}
