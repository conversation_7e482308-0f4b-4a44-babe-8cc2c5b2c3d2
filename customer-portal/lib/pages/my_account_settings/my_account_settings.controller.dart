import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/controllers/loading.controller.dart';
import 'package:pacific_2_customer_portal/models/common.model.dart';
import 'package:pacific_2_customer_portal/models/user_preference.model.dart';
import 'package:pacific_2_customer_portal/services/user.service.dart';
import 'package:pacific_2_customer_portal/services/utility.service.dart';

class MyAccountSettingsController extends GetxController {
  final UserService _userService;
  final UtilityService _utilityService;
  final LoadingController _loadingController = Get.find<LoadingController>();
  final AppController _appController = Get.find<AppController>();

  MyAccountSettingsController(this._userService, this._utilityService);

  List<TimeZone> timeZonelist = [];
  List<String> dateFormatList = [];
  List<String> timeFormatList = [];

  final Rxn<UserPreference> dateTimeFormatPreference = Rxn();
  final Rxn<UserPreference> paymentNotiPreference = Rxn();
  final Rxn<UserPreference> orderNotiPreference = Rxn();
  final Rxn<UserPreference> walletNotiPreference = Rxn();
  final Rxn<UserPreference> mfaPreference = Rxn();

  Future<void> getMetaData() async {
    _loadingController.showLoading(true);
    try {
      dateFormatList =
          (await _utilityService.getDateFormatMetaData() as List<dynamic>)
              .cast<String>();
      timeFormatList =
          (await _utilityService.getTimeFormatMetaData() as List<dynamic>)
              .cast<String>();
      final List<dynamic> timezones =
          await _utilityService.getTimeZoneMetaData();
      timeZonelist = timezones.map((item) => TimeZone.fromJson(item)).toList();
    } catch (e) {/** */}
    _loadingController.showLoading(false);
  }

  Future<void> getUserPreference() async {
    _loadingController.showLoading(true);
    try {
      final responseDateTime = await _userService.getUserPreference(
        _appController.userProfile.value.id!,
        UserPreferenceKey.dateTimeFormat.key,
      );
      final responsePayment = await _userService.getUserPreference(
        _appController.userProfile.value.id!,
        UserPreferenceKey.notificationPayment.key,
      );
      final responseOrder = await _userService.getUserPreference(
        _appController.userProfile.value.id!,
        UserPreferenceKey.notificationOrder.key,
      );
      final responseWallet = await _userService.getUserPreference(
        _appController.userProfile.value.id!,
        UserPreferenceKey.notificationWallet.key,
      );
      final responseMFA = await _userService.getUserPreference(
        _appController.userProfile.value.id!,
        UserPreferenceKey.mfa.key,
      );
      dateTimeFormatPreference.value =
          UserPreference.fromJson(Map.from(responseDateTime));
      paymentNotiPreference.value =
          UserPreference.fromJson(Map.from(responsePayment));
      orderNotiPreference.value =
          UserPreference.fromJson(Map.from(responseOrder));
      walletNotiPreference.value =
          UserPreference.fromJson(Map.from(responseWallet));
      mfaPreference.value = UserPreference.fromJson(Map.from(responseMFA));
    } catch (e) {/** */}
    _loadingController.showLoading(false);
  }

  Future<bool> saveDateTimeFormat() async {
    bool success = false;
    _loadingController.showLoading(true);
    try {
      Map<String, dynamic> params = {
        'key': UserPreferenceKey.dateTimeFormat.key,
        'timeZone': dateTimeFormatPreference.value?.data?.timeZone?.zoneId,
        'dateFormat': dateTimeFormatPreference.value?.data?.dateFormat,
        'timeFormat': dateTimeFormatPreference.value?.data?.timeFormat,
      };
      final response = dateTimeFormatPreference.value!.isDefault!
          ? await _userService.createUserPreference(
              _appController.userProfile.value.id!, params)
          : await _userService.updateUserPreference(
              _appController.userProfile.value.id!,
              dateTimeFormatPreference.value!.id!,
              params);
      if (response['code'] == null) {
        final pref = UserPreference.fromJson(Map.from(response));
        dateTimeFormatPreference.value = pref;
        _appController.setFormatPref(pref);
        success = true;
      }
    } catch (e) {/** */}
    _loadingController.showLoading(false);
    return success;
  }

  Future<void> saveNotiSettings(UserPreferenceKey key) async {
    _loadingController.showLoading(true);
    try {
      final rxPref = switch (key) {
        UserPreferenceKey.notificationPayment => paymentNotiPreference,
        UserPreferenceKey.notificationOrder => orderNotiPreference,
        _ => walletNotiPreference,
      };

      Map<String, dynamic> params = {
        'key': key.key,
        'options': rxPref.value?.data?.channels,
      };
      final response = rxPref.value!.isDefault!
          ? await _userService.createUserPreference(
              _appController.userProfile.value.id!, params)
          : await _userService.updateUserPreference(
              _appController.userProfile.value.id!, rxPref.value!.id!, params);
      if (response['code'] == null) {
        rxPref.value = UserPreference.fromJson(Map.from(response));
      }
    } catch (e) {/** */}
    _loadingController.showLoading(false);
  }

  Future<void> saveMFASettings() async {
    _loadingController.showLoading(true);
    try {
      Map<String, dynamic> params = {
        'key': UserPreferenceKey.mfa.key,
        'isEnabledEmailOtp': mfaPreference.value?.data?.isEnabledEmailOtp,
      };
      final response = mfaPreference.value!.isDefault!
          ? await _userService.createUserPreference(
              _appController.userProfile.value.id!, params)
          : await _userService.updateUserPreference(
              _appController.userProfile.value.id!,
              mfaPreference.value!.id!,
              params);
      if (response['code'] == null) {
        mfaPreference.value = UserPreference.fromJson(Map.from(response));
      }
    } catch (e) {/** */}
    _loadingController.showLoading(false);
  }
}
