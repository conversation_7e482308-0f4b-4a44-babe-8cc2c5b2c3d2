import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/my_bottom_sheet/my_bottom_sheet.dart';
import 'package:pacific_2_customer_portal/controllers/loading.controller.dart';
import 'package:pacific_2_customer_portal/models/user_preference.model.dart';
import 'package:pacific_2_customer_portal/pages/my_account_settings/my_account_settings.controller.dart';
import 'package:pacific_2_customer_portal/pages/my_account_settings/widgets/date_time_list.dart';
import 'package:pacific_2_customer_portal/pages/my_account_settings/widgets/time_zone_list.dart';

class DateTimeFormatSettings extends StatefulWidget {
  const DateTimeFormatSettings({
    super.key,
    required this.pref,
    required this.onSavePref,
    required this.onRefresh,
  });

  final UserPreference? pref;
  final Function() onSavePref;
  final Function() onRefresh;

  @override
  State<DateTimeFormatSettings> createState() => _DateTimeFormatSettingsState();
}

class _DateTimeFormatSettingsState extends State<DateTimeFormatSettings> {
  bool _isTZDirty = false;

  void showDateFormatPicker(BuildContext context) {
    final data = Get.find<MyAccountSettingsController>().dateFormatList;
    showMyBottomSheet(
      context,
      () => DateTimeList(
        title: FlutterI18n.translate(context, 'date'),
        data: data,
        selected: widget.pref?.data?.dateFormat,
        onSelect: (value) {
          widget.pref!.data!.dateFormat = value;
          widget.onRefresh();
        },
      ),
    );
  }

  void showTimeFormatPicker(BuildContext context) {
    final data = Get.find<MyAccountSettingsController>().timeFormatList;
    showMyBottomSheet(
      context,
      () => DateTimeList(
        title: FlutterI18n.translate(context, 'time'),
        data: data,
        selected: widget.pref?.data?.timeFormat,
        onSelect: (value) {
          widget.pref!.data!.timeFormat = value;
          widget.onRefresh();
        },
      ),
    );
  }

  void showTimeZonePicker(BuildContext context) {
    final data = Get.find<MyAccountSettingsController>().timeZonelist;
    showMyBottomSheet(
      context,
      () => TimeZoneList(
        data: data,
        selected: widget.pref?.data?.timeZone,
        onSelect: (value) {
          if (value != widget.pref!.data!.timeZone) {
            _isTZDirty = true;
          }
          widget.pref!.data!.timeZone = value;
          widget.onRefresh();
        },
      ),
    );
  }

  void showTZChangeConfirm(BuildContext context) {
    ThemeData theme = Theme.of(context);
    final loadingController = Get.find<LoadingController>();

    loadingController.showAlertDialog(
      context,
      showTitle: false,
      content: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset('assets/images/img-airplane.png', height: 164),
            const SizedBox(height: 12),
            Text(
              FlutterI18n.translate(context, 'timezoneChangeTitle'),
              style: theme.textTheme.headlineSmall,
            ),
            const SizedBox(height: 12),
            Text(
              FlutterI18n.translate(
                context,
                'timezoneChangeMessage',
                translationParams: {'timezone': widget.pref?.data?.timeZone?.displayName ?? '-'},
              ),
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
      positiveText: FlutterI18n.translate(context, 'confirm'),
      onPositivePress: widget.onSavePref,
      onNegativePress: () => {},
    );
  }

  void onSave(BuildContext context) {
    if (_isTZDirty) {
      showTZChangeConfirm(context);
    } else {
      widget.onSavePref();
    }
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(Responsive.isDesktop(context) ? 16 : 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text(FlutterI18n.translate(context, 'dateTimeFormat'), style: theme.textTheme.headlineSmall),
              ),
              MainButton(
                width: 102,
                height: 32,
                text: FlutterI18n.translate(context, 'saveChange'),
                textStyle: theme.textTheme.labelSmall,
                onPressed: () => onSave(context),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _renderSettingField(
                  context,
                  FlutterI18n.translate(context, 'date'),
                  widget.pref?.data?.dateFormat,
                  () => showDateFormatPicker(context),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _renderSettingField(
                  context,
                  FlutterI18n.translate(context, 'time'),
                  widget.pref?.data?.timeFormat,
                  () => showTimeFormatPicker(context),
                ),
              ),
              if (Responsive.isDesktop(context)) ...[
                const SizedBox(width: 12),
                Expanded(child: _renderTimeZoneField(context)),
              ]
            ],
          ),
          if (!Responsive.isDesktop(context)) ...[
            const SizedBox(height: 8),
            _renderTimeZoneField(context),
          ]
        ],
      ),
    );
  }

  Widget _renderTimeZoneField(BuildContext context) {
    return _renderSettingField(
      context,
      FlutterI18n.translate(context, 'timezone'),
      widget.pref?.data?.timeZone?.displayName,
      () => showTimeZonePicker(context),
    );
  }

  Widget _renderSettingField(BuildContext context, String label, String? value, Function() onPress) {
    ThemeData theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: theme.textTheme.bodyMedium),
        const SizedBox(height: 4),
        Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onPress,
            borderRadius: BorderRadius.circular(8),
            child: Ink(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.colorScheme.outline),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(value ?? '-', style: theme.textTheme.bodyMedium),
                  ),
                  SvgPicture.asset('assets/images/ic-arrow-down.svg', width: 16),
                ],
              ),
            ),
          ),
        )
      ],
    );
  }
}
