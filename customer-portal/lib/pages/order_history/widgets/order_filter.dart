import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/tap_gesture_detector/tap_gesture_detector.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_outlined_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/checkbox/my_checkbox.dart';
import 'package:pacific_2_customer_portal/models/controller_tag.model.dart';
import 'package:pacific_2_customer_portal/models/order_history.model.dart';
import 'package:pacific_2_customer_portal/common/widgets/filter/date_filter.dart';
import 'package:pacific_2_customer_portal/common/widgets/filter/account_filter.dart';

class OrderFilterWidget extends StatefulWidget {
  const OrderFilterWidget({
    super.key,
    required this.currentFilter,
    required this.onApplyFilter,
    required this.onClearFilter,
    required this.onClose,
  });

  final OrderHistoryFilter currentFilter;
  final Function(OrderHistoryFilter filter) onApplyFilter;
  final Function() onClearFilter;
  final Function() onClose;

  @override
  State<OrderFilterWidget> createState() => _OrderFilterWidgetState();
}

class _OrderFilterWidgetState extends State<OrderFilterWidget> {
  bool isDirty = false;
  bool isCreatedError = false;
  bool isCollectionError = false;
  late OrderHistoryFilter filter = OrderHistoryFilter.copy(widget.currentFilter);

  void applyFilter() {
    bool createdError =
        filter.createdFrom != null && filter.createdTo != null && filter.createdFrom!.isAfter(filter.createdTo!);
    bool collectionError = filter.collectionFrom != null &&
        filter.collectionTo != null &&
        filter.collectionFrom!.isAfter(filter.collectionTo!);

    if (createdError || collectionError) {
      setState(() {
        isCreatedError = createdError;
        isCollectionError = collectionError;
      });
      return;
    }

    widget.onApplyFilter(filter);
    widget.onClose();
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      width: double.infinity,
      height: double.infinity,
      color: theme.colorScheme.surfaceContainer,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(FlutterI18n.translate(context, 'filter'), style: theme.textTheme.headlineSmall),
                SizedBox(
                  width: 24,
                  height: 24,
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    onPressed: widget.onClose,
                    icon: const Icon(Icons.close),
                    iconSize: 24,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(mainAxisSize: MainAxisSize.max),
                  AccountFilter(
                    controllerTag: ControllerTag.accountListOrder.name,
                    selectedAccounts: filter.accounts!,
                    onChanged: (accounts) => setState(() {
                      isDirty = true;
                      filter.accounts = accounts;
                    }),
                  ),
                  const SizedBox(height: 16),
                  Text(FlutterI18n.translate(context, 'orderType'), style: theme.textTheme.titleMedium),
                  const SizedBox(height: 8),
                  _renderTypeRow(context, OrderType.preorder),
                  _renderTypeRow(context, OrderType.onsite),
                  const SizedBox(height: 16),
                  Text(FlutterI18n.translate(context, 'orderStatus'), style: theme.textTheme.titleMedium),
                  const SizedBox(height: 8),
                  _renderStatusRow(context, OrderStatus.created),
                  _renderStatusRow(context, OrderStatus.pending),
                  _renderStatusRow(context, OrderStatus.paid),
                  _renderStatusRow(context, OrderStatus.cancelling),
                  _renderStatusRow(context, OrderStatus.cancelled),
                  _renderStatusRow(context, OrderStatus.preparing),
                  _renderStatusRow(context, OrderStatus.collected),
                  _renderStatusRow(context, OrderStatus.completed),
                  const SizedBox(height: 16),
                  Text(FlutterI18n.translate(context, 'byOrderDate'), style: theme.textTheme.titleMedium),
                  const SizedBox(height: 12),
                  DateFilter(
                    dateRangeType: filter.createdRangeType,
                    dateFrom: filter.createdFrom,
                    dateTo: filter.createdTo,
                    onChanged: (type, from, to) => setState(() {
                      isDirty = true;
                      isCreatedError = false;
                      filter.createdRangeType = type;
                      filter.createdFrom = from;
                      filter.createdTo = to;
                    }),
                    isError: isCreatedError,
                  ),
                  const SizedBox(height: 16),
                  Text(FlutterI18n.translate(context, 'byCollectionDate'), style: theme.textTheme.titleMedium),
                  const SizedBox(height: 12),
                  DateFilter(
                    dateRangeType: filter.collectionRangeType,
                    dateFrom: filter.collectionFrom,
                    dateTo: filter.collectionTo,
                    onChanged: (type, from, to) => setState(() {
                      isDirty = true;
                      isCollectionError = false;
                      filter.collectionRangeType = type;
                      filter.collectionFrom = from;
                      filter.collectionTo = to;
                    }),
                    isError: isCollectionError,
                  )
                ],
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(top: BorderSide(color: theme.colorScheme.outline)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: MainOutlinedButton(
                    height: 40,
                    text: FlutterI18n.translate(context, 'clearFilter'),
                    textStyle: theme.textTheme.labelMedium,
                    onPressed: () {
                      widget.onClearFilter();
                      widget.onClose();
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: MainButton(
                    height: 40,
                    text: FlutterI18n.translate(context, 'applyFilter'),
                    textStyle: theme.textTheme.labelMedium,
                    disabled: !isDirty,
                    onPressed: applyFilter,
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _renderTypeRow(BuildContext context, OrderType type) {
    ThemeData theme = Theme.of(context);

    bool isChecked = filter.orderTypes!.contains(type);
    void onCheck(bool checked) {
      setState(() {
        isDirty = true;
        if (checked) {
          filter.orderTypes!.add(type);
        } else {
          filter.orderTypes!.remove(type);
        }
      });
    }

    return TapGestureDetector(
      onTap: () => onCheck(!isChecked),
      child: Container(
        color: theme.colorScheme.surfaceContainer,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 40,
              height: 40,
              child: MyCheckBox(
                checked: isChecked,
                onChanged: (checked) => onCheck(checked!),
              ),
            ),
            Text(FlutterI18n.translate(context, type.labelKey), style: theme.textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }

  Widget _renderStatusRow(BuildContext context, OrderStatus status) {
    ThemeData theme = Theme.of(context);

    bool isChecked = filter.orderStatuses!.contains(status);
    void onCheck(bool checked) {
      setState(() {
        isDirty = true;
        if (checked) {
          filter.orderStatuses!.add(status);
        } else {
          filter.orderStatuses!.remove(status);
        }
      });
    }

    return TapGestureDetector(
      onTap: () => onCheck(!isChecked),
      child: Container(
        color: theme.colorScheme.surfaceContainer,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 40,
              height: 40,
              child: MyCheckBox(
                checked: isChecked,
                onChanged: (checked) => onCheck(checked!),
              ),
            ),
            Text(FlutterI18n.translate(context, status.labelKey), style: theme.textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }
}
