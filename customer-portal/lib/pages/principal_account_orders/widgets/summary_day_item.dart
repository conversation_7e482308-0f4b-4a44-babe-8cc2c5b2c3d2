import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pacific_2_customer_portal/common/tap_gesture_detector/tap_gesture_detector.dart';

class SummaryDateItem extends StatelessWidget {
  const SummaryDateItem({
    super.key,
    required this.date,
    required this.isSelected,
    required this.onPress,
  });

  final DateTime date;
  final bool isSelected;
  final Function() onPress;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    String dow = DateFormat('EEE').format(date);
    Color textColor = isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurface;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(dow, style: theme.textTheme.bodySmall),
        const SizedBox(height: 8),
        TapGestureDetector(
          onTap: onPress,
          child: Container(
            width: 34,
            height: 34,
            decoration: BoxDecoration(
              color: isSelected ? theme.colorScheme.primaryContainer : theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: textColor),
              boxShadow: [
                if (isSelected)
                  BoxShadow(
                    color: theme.colorScheme.primary.withValues(alpha: 0.24),
                    offset: const Offset(0, 8),
                    blurRadius: 16,
                    spreadRadius: 0,
                  ),
              ],
            ),
            child: Center(
              child: Text(
                date.day.toString(),
                style: theme.textTheme.titleSmall!.copyWith(color: textColor),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
