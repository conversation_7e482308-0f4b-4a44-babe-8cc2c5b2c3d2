import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:jiffy/jiffy.dart';
import 'package:pacific_2_customer_portal/controllers/loading.controller.dart';
import 'package:pacific_2_customer_portal/models/order_history.model.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';
import 'package:pacific_2_customer_portal/services/catalog.service.dart';
import 'package:pacific_2_customer_portal/services/order.service.dart';

class PrincipalAccountOrdersController extends GetxController {
  final OrderService _orderService;
  final CatalogService _catalogService;
  final LoadingController _loadingController = Get.find<LoadingController>();

  PrincipalAccountOrdersController(this._orderService, this._catalogService);

  UserProfile _user = UserProfile();
  final Rx<DateTime> focusedDate = DateTime.now().copyWith(isUtc: true).obs;
  final Rx<DateTime> selectedDate = DateTime.now().copyWith(isUtc: true).obs;

  int _currentPage = 0;
  bool _canLoadMoreOrder = true;
  bool _isLoadingOrder = false;
  final RxList<OrderHistoryItem> orderList = <OrderHistoryItem>[].obs;

  Future<void> setUser(UserProfile user) async {
    _user = user;
    getOrderList();
  }

  Future<void> getOrderList({int size = 20, bool isRefresh = false}) async {
    if (_isLoadingOrder) return;
    if (isRefresh) {
      _currentPage = 0;
      _canLoadMoreOrder = true;
      orderList.value = [];
    }
    if (!_canLoadMoreOrder) return;

    _isLoadingOrder = true;
    _loadingController.showLoading(true);
    try {
      DateFormat dateFormat = DateFormat('yyyy-MM-dd');
      final response = await _orderService.getOrderList(
        {
          "page": _currentPage,
          "size": size,
          'filter.statuses': [
            'CREATED',
            'PENDING',
            'PAID',
            'COMPLETED',
            'PREPARING',
            'COLLECTED',
          ],
          'filter.types': 'PRE_ORDER',
          'filter.customerIds': [_user.id],
          'filter.fromCollectionDate': dateFormat.format(selectedDate.value),
          'filter.toCollectionDate': dateFormat.format(selectedDate.value),
        },
      );
      final Map<String, dynamic> data = Map.from(response);
      final List<dynamic> orderData = data['content'];
      final List<OrderHistoryItem> orderItems = orderData.map((e) => OrderHistoryItem.fromJson(e)).toList();

      final List<String> storeIds = orderItems.map((e) => e.storeId ?? '').toSet().toList();
      final responseStores = await _catalogService.getStoreList(storeIds);
      final List<dynamic> storeList = responseStores['content'];
      final storeMap = {for (var store in storeList) store['storeId']: store['name']};

      for (var order in orderItems) {
        order.storeName = storeMap[order.storeId];
      }

      orderList.addAll(orderItems);
      _currentPage++;
      _canLoadMoreOrder = orderData.length == size;
    } catch (e) {/** */}
    _loadingController.showLoading(false);
    _isLoadingOrder = false;
  }

  Future<bool> cancelOrder(String orderId) async {
    bool result = false;
    _loadingController.showLoading(true);
    try {
      final response = await _orderService.cancelOrder({
        'ids': [orderId],
        'reason': 'Customer cancelled',
        'refundable': true,
      });
      if (response['code'] == null) {
        getOrderList(isRefresh: true);
        result = true;
      }
    } catch (e) {/** */}
    _loadingController.showLoading(false);
    return result;
  }

  void onNextWeek() {
    focusedDate.value = Jiffy.parseFromDateTime(focusedDate.value).add(weeks: 1).dateTime;
  }

  void onPreviousWeek() {
    focusedDate.value = Jiffy.parseFromDateTime(focusedDate.value).subtract(weeks: 1).dateTime;
  }

  void onSelectDate(DateTime selected) {
    selectedDate.value = selected;
    getOrderList(isRefresh: true);
  }

  Future<void> onToday() async {
    final now = DateTime.now().copyWith(isUtc: true);
    selectedDate.value = now;
    focusedDate.value = now;
    getOrderList(isRefresh: true);
  }
}
