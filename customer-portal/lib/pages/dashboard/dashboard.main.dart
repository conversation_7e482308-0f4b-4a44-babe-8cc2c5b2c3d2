// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:pacific_2_customer_portal/common/layout/app_layout.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/core/constants/system_const.dart';
import 'package:pacific_2_customer_portal/pages/dashboard/widget_announcement/widget_announcement.main.dart';
import 'package:pacific_2_customer_portal/pages/dashboard/widget_today_order/widget_today_order.main.dart';
import 'package:pacific_2_customer_portal/pages/dashboard/widget_upcoming/widget_upcoming.main.dart';
import 'package:pacific_2_customer_portal/pages/dashboard/widget_welcome_banner/widget_welcome_banner.main.dart';

class DashBoard extends StatelessWidget {
  const DashBoard({super.key});

  final route = SystemConst.ROUTE;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
            image: AssetImage(
              Responsive.isMobile(context)
                  ? 'assets/images/background-dashboard.png'
                  : 'assets/images/background-dashboard-desktop.png',
            ),
            fit: BoxFit.cover),
      ),
      child: AppLayout(
        backgroundColor: Colors.transparent,
        child: Column(
          children: [
            const WidgetAnnouncement(),
            Expanded(
              child: Responsive(
                mobile: _renderMobile(context),
                desktop: _renderDesktop(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _renderMobile(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const SizedBox(height: 314, child: WidgetWelcomeBanner()),
        const SizedBox(height: 16),
        const SizedBox(height: 372, child: WidgetTodayOrder()),
        const SizedBox(height: 16),
        const WidgetUpcoming()
      ],
    );
  }

  Widget _renderDesktop(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Column(
              children: [
                SizedBox(height: 280, child: WidgetWelcomeBanner()),
                SizedBox(height: 16),
                Expanded(child: WidgetUpcoming()),
              ],
            ),
          ),
          SizedBox(width: 24),
          Expanded(
            flex: 1,
            child: Column(
              children: [
                Expanded(child: WidgetTodayOrder()),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
