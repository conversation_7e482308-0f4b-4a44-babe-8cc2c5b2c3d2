import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_outlined_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/text_field_money/text_field_money.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class SetDailyQuota extends StatefulWidget {
  const SetDailyQuota({
    super.key,
    required this.currentQuota,
    required this.onSave,
  });

  final int currentQuota;
  final Function(int quota) onSave;

  @override
  State<SetDailyQuota> createState() => _SetDailyQuotaState();
}

class _SetDailyQuotaState extends State<SetDailyQuota> {
  late String _quota = '${widget.currentQuota != 0 ? widget.currentQuota : ''}';
  late final TextEditingController _textEditingController = TextEditingController(
      text: widget.currentQuota != 0 ? formatPrice(widget.currentQuota, showCurrency: false) : '');

  @override
  Widget build(BuildContext context) {
    return Responsive.isDesktop(context) ? _renderDesktop(context) : _renderMobile(context);
  }

  Widget _renderDesktop(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return SizedBox(
      width: 428,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Align(
              alignment: Alignment.center,
              child: Text(
                FlutterI18n.translate(context, 'dailyQuota'),
                style: theme.textTheme.headlineLarge,
              ),
            ),
            const SizedBox(height: 12),
            Flexible(child: ListView(shrinkWrap: true, children: _renderContent(context))),
            const SizedBox(height: 24),
            _renderButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _renderMobile(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 10, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            FlutterI18n.translate(context, 'dailyQuota'),
            style: theme.textTheme.headlineSmall,
          ),
          const SizedBox(height: 4),
          Expanded(child: ListView(children: _renderContent(context))),
          const SizedBox(height: 16),
          _renderButtons(context),
        ],
      ),
    );
  }

  List<Widget> _renderContent(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return [
      _renderRefBulletRow(context, 'dailyQuotaRef1'),
      _renderRefBulletRow(context, 'dailyQuotaRef2'),
      const SizedBox(height: 16),
      Text(
        FlutterI18n.translate(context, 'dailyQuotaAmount'),
        style: theme.textTheme.bodyMedium,
      ),
      const SizedBox(height: 4),
      TextFieldMoney(
        controller: _textEditingController,
        hintText: FlutterI18n.translate(context, 'noLimit'),
        onChanged: (text) => setState(() {
          _quota = text.replaceAll(',', '').replaceAll('.', '');
        }),
      ),
    ];
  }

  Widget _renderButtons(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Row(
      children: [
        Expanded(
          child: MainOutlinedButton(
            height: 40,
            text: FlutterI18n.translate(context, 'cancel'),
            textStyle: theme.textTheme.labelMedium,
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: MainButton(
            height: 40,
            text: FlutterI18n.translate(context, 'save'),
            textStyle: theme.textTheme.labelMedium,
            onPressed: () {
              widget.onSave(int.tryParse(_quota.trim()) ?? 0);
            },
          ),
        )
      ],
    );
  }

  Widget _renderRefBulletRow(BuildContext context, String key) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(" • "),
        Expanded(
          child: Text(
            FlutterI18n.translate(context, key),
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }
}
