import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class SpendingLimit extends StatelessWidget {
  const SpendingLimit({
    super.key,
    required this.currentLimitDay,
    required this.currentLimitWeek,
    required this.currentLimitMonth,
    required this.onRemove,
  });

  final int currentLimitDay;
  final int currentLimitWeek;
  final int currentLimitMonth;
  final Function() onRemove;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    final noLimitText = FlutterI18n.translate(context, 'noLimit');
    final dayText = currentLimitDay != 0 ? formatPrice(currentLimitDay) : noLimitText;
    final weekText = currentLimitWeek != 0 ? formatPrice(currentLimitWeek) : noLimitText;
    final monthText = currentLimitMonth != 0 ? formatPrice(currentLimitMonth) : noLimitText;

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(children: [
            Expanded(
              child: Text(
                FlutterI18n.translate(context, 'spendingLimit'),
                style: theme.textTheme.bodySmall,
              ),
            ),
            SizedBox(
              width: 20,
              height: 20,
              child: IconButton(
                padding: EdgeInsets.zero,
                onPressed: onRemove,
                iconSize: 20,
                icon: const Icon(Icons.cancel_outlined),
              ),
            ),
          ]),
          const SizedBox(height: 8),
          IntrinsicHeight(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Flexible(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.baseline,
                    textBaseline: TextBaseline.alphabetic,
                    children: [
                      Flexible(
                        child: Text(dayText, style: theme.textTheme.titleLarge),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        FlutterI18n.translate(context, 'daily'),
                        style: theme.textTheme.bodySmall!.copyWith(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ],
                  ),
                ),
                _renderDivider(context),
                Flexible(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.baseline,
                    textBaseline: TextBaseline.alphabetic,
                    children: [
                      Flexible(
                        child: Text(weekText, style: theme.textTheme.titleLarge),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        FlutterI18n.translate(context, 'weekly'),
                        style: theme.textTheme.bodySmall!.copyWith(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ],
                  ),
                ),
                _renderDivider(context),
                Flexible(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.baseline,
                    textBaseline: TextBaseline.alphabetic,
                    children: [
                      Flexible(
                        child: Text(monthText, style: theme.textTheme.titleLarge),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        FlutterI18n.translate(context, 'monthly'),
                        style: theme.textTheme.bodySmall!.copyWith(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _renderDivider(BuildContext context) {
    return Container(
      width: 1,
      margin: const EdgeInsets.all(2),
      color: Theme.of(context).colorScheme.outline,
    );
  }
}
