import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_outlined_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/text_field_money/text_field_money.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class SetSpendingLimit extends StatefulWidget {
  const SetSpendingLimit({
    super.key,
    required this.currentLimitDay,
    required this.currentLimitWeek,
    required this.currentLimitMonth,
    required this.onSave,
  });

  final int currentLimitDay;
  final int currentLimitWeek;
  final int currentLimitMonth;
  final Function(int limitDay, int limitWeek, int limitMonth) onSave;

  @override
  State<SetSpendingLimit> createState() => _SetSpendingLimitState();
}

class _SetSpendingLimitState extends State<SetSpendingLimit> {
  late String _amountDay = '${widget.currentLimitDay != 0 ? widget.currentLimitDay : ''}';
  late final TextEditingController _textEditingControllerDay = TextEditingController(
      text: widget.currentLimitDay != 0 ? formatPrice(widget.currentLimitDay, showCurrency: false) : '');

  late String _amountWeek = '${widget.currentLimitWeek != 0 ? widget.currentLimitWeek : ''}';
  late final TextEditingController _textEditingControllerWeek = TextEditingController(
      text: widget.currentLimitWeek != 0 ? formatPrice(widget.currentLimitWeek, showCurrency: false) : '');

  late String _amountMonth = '${widget.currentLimitMonth != 0 ? widget.currentLimitMonth : ''}';
  late final TextEditingController _textEditingControllerMonth = TextEditingController(
      text: widget.currentLimitMonth != 0 ? formatPrice(widget.currentLimitMonth, showCurrency: false) : '');

  @override
  Widget build(BuildContext context) {
    return Responsive.isDesktop(context) ? _renderDesktop(context) : _renderMobile(context);
  }

  Widget _renderDesktop(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return SizedBox(
      width: 428,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Align(
              alignment: Alignment.center,
              child: Text(
                FlutterI18n.translate(context, 'setSpendingLimit'),
                style: theme.textTheme.headlineLarge,
              ),
            ),
            const SizedBox(height: 12),
            Flexible(child: ListView(shrinkWrap: true, children: _renderContent(context))),
            const SizedBox(height: 24),
            _renderButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _renderMobile(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 10, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            FlutterI18n.translate(context, 'setSpendingLimit'),
            style: theme.textTheme.headlineSmall,
          ),
          const SizedBox(height: 4),
          Expanded(child: ListView(children: _renderContent(context))),
          const SizedBox(height: 16),
          _renderButtons(context),
        ],
      ),
    );
  }

  List<Widget> _renderContent(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return [
      Text(
        FlutterI18n.translate(context, 'dailySpendingAmount'),
        style: theme.textTheme.bodyMedium,
      ),
      const SizedBox(height: 4),
      TextFieldMoney(
        controller: _textEditingControllerDay,
        hintText: FlutterI18n.translate(context, 'noLimit'),
        onChanged: (text) => setState(() {
          _amountDay = text.replaceAll(',', '').replaceAll('.', '');
        }),
      ),
      const SizedBox(height: 16),
      Text(
        FlutterI18n.translate(context, 'weeklySpendingAmount'),
        style: theme.textTheme.bodyMedium,
      ),
      const SizedBox(height: 4),
      TextFieldMoney(
        controller: _textEditingControllerWeek,
        hintText: FlutterI18n.translate(context, 'noLimit'),
        onChanged: (text) => setState(() {
          _amountWeek = text.replaceAll(',', '').replaceAll('.', '');
        }),
      ),
      const SizedBox(height: 16),
      Text(
        FlutterI18n.translate(context, 'monthlySpendingAmount'),
        style: theme.textTheme.bodyMedium,
      ),
      const SizedBox(height: 4),
      TextFieldMoney(
        controller: _textEditingControllerMonth,
        hintText: FlutterI18n.translate(context, 'noLimit'),
        onChanged: (text) => setState(() {
          _amountMonth = text.replaceAll(',', '').replaceAll('.', '');
        }),
      ),
      const SizedBox(height: 16),
      _renderQuickRef(context)
    ];
  }

  Widget _renderQuickRef(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            FlutterI18n.translate(context, 'spendingLimitRefTitle'),
            style: theme.textTheme.titleLarge!.copyWith(color: theme.colorScheme.primary),
          ),
          ..._renderRefSectionRow(context, 'spendingLimitRef1'),
          _renderRefBulletRow(context, 'spendingLimitRef2'),
          _renderRefBulletRow(context, 'spendingLimitRef3'),
          _renderRefBulletRow(context, 'spendingLimitRef4'),
          //
          ..._renderRefSectionRow(context, 'spendingLimitRef5'),
          _renderRefBulletRow(context, 'spendingLimitRef6'),
          _renderRefBulletRow(context, 'spendingLimitRef7'),
          //
          ..._renderRefSectionRow(context, 'spendingLimitRef8'),
          _renderRefBulletRow(context, 'spendingLimitRef9'),
          _renderRefBulletRow(context, 'spendingLimitRef10'),
          const SizedBox(height: 4),
          RichText(
            text: TextSpan(
              text: FlutterI18n.translate(context, 'spendingLimitRef11-1'),
              style: theme.textTheme.titleLarge,
              children: [
                TextSpan(
                  text: FlutterI18n.translate(context, 'spendingLimitRef11-2'),
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
          ),
          //
          ..._renderRefSectionRow(context, 'spendingLimitRef12'),
          _renderRefBulletRow(context, 'spendingLimitRef13'),
          _renderRefBulletRow(context, 'spendingLimitRef14'),
          _renderRefBulletRow(context, 'spendingLimitRef15'),
          Text(
            FlutterI18n.translate(context, 'spendingLimitRef16'),
            style: Theme.of(context).textTheme.bodySmall,
          ),
          Text(
            FlutterI18n.translate(context, 'spendingLimitRef17'),
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }

  Widget _renderButtons(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Row(
      children: [
        Expanded(
          child: MainOutlinedButton(
            height: 40,
            text: FlutterI18n.translate(context, 'cancel'),
            textStyle: theme.textTheme.labelMedium,
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: MainButton(
            height: 40,
            text: FlutterI18n.translate(context, 'save'),
            textStyle: theme.textTheme.labelMedium,
            onPressed: () {
              widget.onSave(
                int.tryParse(_amountDay.trim()) ?? 0,
                int.tryParse(_amountWeek.trim()) ?? 0,
                int.tryParse(_amountMonth.trim()) ?? 0,
              );
            },
          ),
        )
      ],
    );
  }

  List<Widget> _renderRefSectionRow(BuildContext context, String key) {
    return [
      const SizedBox(height: 8),
      Text(
        FlutterI18n.translate(context, key),
        style: Theme.of(context).textTheme.titleLarge,
      ),
      const SizedBox(height: 4),
    ];
  }

  Widget _renderRefBulletRow(BuildContext context, String key) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(" • "),
        Expanded(
          child: Text(
            FlutterI18n.translate(context, key),
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ),
      ],
    );
  }
}
