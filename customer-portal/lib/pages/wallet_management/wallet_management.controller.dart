import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/controllers/loading.controller.dart';
import 'package:pacific_2_customer_portal/models/wallet_transaction.model.dart';
import 'package:pacific_2_customer_portal/models/sub_account.model.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';
import 'package:pacific_2_customer_portal/models/wallet.model.dart';
import 'package:pacific_2_customer_portal/services/user.service.dart';
import 'package:pacific_2_customer_portal/services/wallet.service.dart';

class WalletManagementController extends GetxController {
  final UserService _userService;
  final WalletService _walletService;
  final LoadingController _loadingController = Get.find<LoadingController>();
  final AppController _appController = Get.find<AppController>();

  WalletManagementController(this._userService, this._walletService);

  int _currentPage = 0;
  bool _canLoadMoreAccount = true;
  bool _isLoadingAccount = false;
  final RxList<UserProfile> subAccountList = <UserProfile>[].obs;
  final Rxn<UserProfile> selectedUser = Rxn();

  final Rxn<Wallet> depositWallet = Rxn();
  final RxList<Wallet> fundedWalletList = <Wallet>[].obs;

  final RxBool isDelegateEnabled = false.obs;
  final RxInt delegateDailyQuota = 0.obs;

  final RxList<WalletTransaction> transactionList = <WalletTransaction>[].obs;

  final RxInt spendingLimitDay = 0.obs;
  final RxInt spendingLimitWeek = 0.obs;
  final RxInt spendingLimitMonth = 0.obs;

  Future<void> getAccountList({int size = 20}) async {
    if (!_canLoadMoreAccount || _isLoadingAccount) {
      return;
    }
    _isLoadingAccount = true;
    _loadingController.showLoading(true);
    try {
      final response = await _userService.getSubAccountList(
        _appController.userProfile.value.id!,
        {
          "page": _currentPage,
          "size": size,
          "filter.byStatuses": "ACTIVE",
        },
      );
      final Map<String, dynamic> data = Map.from(response);
      final List<dynamic> accountList = data['content'];
      subAccountList.addAll(accountList.map((e) => SubAccount.fromJson(e).subUser!));
      _currentPage++;
      _canLoadMoreAccount = accountList.length == size;
    } catch (e) {/** */}
    _loadingController.showLoading(false);
    _isLoadingAccount = false;
  }

  Future<void> getWalletList() async {
    _loadingController.showLoading(true);
    try {
      final response = await _walletService.getWalletList({
        'filter.tenantId': _appController.tenant.value.tenantId,
        'filter.customerId': selectedUser.value?.id,
      });
      final Map<String, dynamic> data = Map.from(response);
      final List<dynamic> wallets = data['content'];
      List<Wallet> walletList = wallets.map((wallet) => Wallet.fromJson(wallet)).toList();
      walletList.sort((a, b) => a.priority!.compareTo(b.priority!));

      depositWallet.value = walletList.firstWhereOrNull((wallet) => wallet.type == WalletType.deposit.type);
      fundedWalletList.value =
          walletList.where((wallet) => wallet.type == WalletType.funded.type && wallet.status == 'ACTIVE').toList();

      if (depositWallet.value == null) {
        await createDepositWallet();
      }

      if (isMainUser()) {
        _appController.walletList.value = [if (depositWallet.value != null) depositWallet.value!, ...fundedWalletList];
      }
    } catch (e) {/** */}
    _loadingController.showLoading(false);
  }

  Future<void> createDepositWallet() async {
    _loadingController.showLoading(true);
    try {
      final response = await _walletService.createDepositWallet({
        'customerId': selectedUser.value?.id,
        'type': 'DEPOSIT',
      });
      final Map<String, dynamic> data = Map.from(response);
      final wallet = Wallet.fromJson(data);
      if (wallet.walletId != null) {
        depositWallet.value = wallet;
      }
    } catch (e) {/** */}
    _loadingController.showLoading(false);
  }

  Future<void> getFundedWalletExpiration() async {
    _loadingController.showLoading(true);
    try {
      final responses = await Future.wait(fundedWalletList.map(
        (wallet) => _walletService.getWalletExpiring(wallet.walletId!, selectedUser.value?.id),
      ));

      for (var response in responses) {
        final Map<String, dynamic> data = Map.from(response);
        final WalletExpiration expiration = WalletExpiration.fromJson(data);
        final Wallet? wallet = fundedWalletList.firstWhereOrNull((w) => w.walletId == expiration.walletId);
        wallet?.setWalletExpiration(expiration);
      }
      fundedWalletList.refresh();
    } catch (e) {/** */}
    _loadingController.showLoading(false);
  }

  Future<void> getTransactionList() async {
    _loadingController.showLoading(true);
    try {
      final response = await _walletService.getTransactionList({
        'filter.tenantId': _appController.tenant.value.tenantId,
        'filter.customerIds': [selectedUser.value?.id],
        'size': 4,
        'sortDirection': 'DESC',
        'sortFields': 'createdAt',
      });
      final Map<String, dynamic> data = Map.from(response);
      final List<dynamic> transactions = data['content'];
      transactionList.value = transactions.map((transaction) => WalletTransaction.fromJson(transaction)).toList();
    } catch (e) {/** */}
    _loadingController.showLoading(false);
  }

  Future<void> getSpendingLimit() async {
    if (depositWallet.value == null) return;
    _loadingController.showLoading(true);
    try {
      final walletId = depositWallet.value?.walletId ?? '';
      final response = await _walletService.getSpendingLimit(walletId);
      handleSpendingLimitResponse(response);
    } catch (e) {/** */}
    _loadingController.showLoading(false);
  }

  String? validateSpendingLimit(int limitDay, int limitWeek, int limitMonth) {
    if (limitWeek != 0 && limitDay > limitWeek) {
      return 'spendingLimitDailyWeeklyError';
    }
    if (limitMonth != 0 && limitDay > limitMonth) {
      return 'spendingLimitDailyMonthlyError';
    }
    if (limitMonth != 0 && limitWeek > limitMonth) {
      return 'spendingLimitWeeklyMonthlyError';
    }
    return null;
  }

  Future<void> setSpendingLimit(int limitDay, int limitWeek, int limitMonth) async {
    _loadingController.showLoading(true);
    try {
      final walletId = depositWallet.value?.walletId ?? '';
      final currency = _appController.tenant.value.settings?.currency?.currencyCode;
      final response = await _walletService.setSpendingLimit(walletId, {
        'items': [
          {
            'type': 'DAY',
            'status': 'ACTIVE',
            'spendingLimit': limitDay,
            'currency': currency,
          },
          {
            'type': 'WEEK',
            'status': 'ACTIVE',
            'spendingLimit': limitWeek,
            'currency': currency,
          },
          {
            'type': 'MONTH',
            'status': 'ACTIVE',
            'spendingLimit': limitMonth,
            'currency': currency,
          },
        ],
      });
      handleSpendingLimitResponse(response);
    } catch (e) {/** */}
    _loadingController.showLoading(false);
  }

  void handleSpendingLimitResponse(dynamic response) {
    final Map<String, dynamic> data = Map.from(response);
    final List<dynamic> spendingLimits = data['content'];
    for (var spendingLimit in spendingLimits) {
      if (spendingLimit['type'] == 'DAY') {
        spendingLimitDay.value = spendingLimit['spendingLimit'] ?? 0;
      } else if (spendingLimit['type'] == 'WEEK') {
        spendingLimitWeek.value = spendingLimit['spendingLimit'] ?? 0;
      } else if (spendingLimit['type'] == 'MONTH') {
        spendingLimitMonth.value = spendingLimit['spendingLimit'] ?? 0;
      }
    }
  }

  bool shouldShowSpendingLimit() {
    return !isDepositWalletActive() &&
        (spendingLimitDay.value != 0 || spendingLimitWeek.value != 0 || spendingLimitMonth.value != 0);
  }

  void clearSpendingLimit() {
    spendingLimitDay.value = 0;
    spendingLimitWeek.value = 0;
    spendingLimitMonth.value = 0;
  }

  Future<void> getDelegateSetting() async {
    _loadingController.showLoading(true);
    try {
      final response = await _walletService.getDelegate(depositWallet.value!.walletId!);
      final Map<String, dynamic> data = Map.from(response);
      isDelegateEnabled.value = data['enabled'];
      delegateDailyQuota.value = data['dailyQuota'] ?? 0;
    } catch (e) {/** */}
    _loadingController.showLoading(false);
  }

  Future<void> updateDelegate({required bool enabled, int dailyQuota = 0}) async {
    _loadingController.showLoading(true);
    try {
      final response = await _walletService.updateDelegate({
        'parentWalletId': _appController.getDepositWallet().walletId,
        'subWalletId': depositWallet.value?.walletId,
        'enabled': enabled,
        'dailyQuota': dailyQuota,
      });
      if (response['code'] == null) {
        isDelegateEnabled.value = enabled;
        delegateDailyQuota.value = dailyQuota;
      }
    } catch (e) {/** */}
    _loadingController.showLoading(false);
  }

  bool isMainUser() {
    return selectedUser.value?.id == _appController.userProfile.value.id;
  }

  Future<void> onUserSelected(UserProfile user) async {
    selectedUser.value = user;
    clearSpendingLimit();
    await getWalletList();
    getFundedWalletExpiration();
    if (!isMainUser()) {
      getDelegateSetting();
    }
    getSpendingLimit();
    getTransactionList();
  }

  bool isDepositWalletActive() {
    return depositWallet.value?.status == 'ACTIVE';
  }
}
