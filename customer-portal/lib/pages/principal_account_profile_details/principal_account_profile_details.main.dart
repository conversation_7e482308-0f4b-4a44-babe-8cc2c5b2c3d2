// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/widgets/title_row/title_row_dialog.dart';
import 'package:pacific_2_customer_portal/common/widgets/user_avatar/user_avatar.dart';
import 'package:pacific_2_customer_portal/pages/principal_account_profile_details/principal_account_profile_details.controller.dart';
import 'package:pacific_2_customer_portal/pages/principal_account_profile_details/widgets/card_list_view.dart';
import 'package:pacific_2_customer_portal/pages/principal_account_profile_details/widgets/general_info_view.dart';
import 'package:pacific_2_customer_portal/pages/principal_account_profile_details/widgets/wallet_view.dart';
import 'package:pacific_2_customer_portal/services/user.service.dart';
import 'package:pacific_2_customer_portal/services/wallet.service.dart';

import '../../controllers/subaccount.controller.dart';
import '../../models/sub_account.model.dart';
import '../../utils/general_utils.dart';

class PrincipalAccountProfileDetails extends StatefulWidget {
  final SubAccount? selectedAccount;
  const PrincipalAccountProfileDetails({super.key, this.selectedAccount});

  @override
  State<PrincipalAccountProfileDetails> createState() => _PrincipalAccountProfileDetailsState();
}

class _PrincipalAccountProfileDetailsState extends State<PrincipalAccountProfileDetails> {
  late final SubAccountController _subAccountController = Get.find<SubAccountController>();
  final PrincipalAccountProfileDetailsController _principalAccountProfileDetailsController =
      Get.put(PrincipalAccountProfileDetailsController(UserService(), WalletService()));
  late final SubAccount? _account = widget.selectedAccount;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchWalletInfo();
    });
  }

  Future<void> _fetchWalletInfo() async {
    if (_account?.id != null) {
      try {
        final userId = _account!.subUser!.id!;
        await _principalAccountProfileDetailsController.getFundedWalletList(userId);
        _principalAccountProfileDetailsController.getFundedWalletExpiration(userId);
        await _subAccountController.getAllergensCatalog();
        _subAccountController.getAllergensList(_account.subUser!.id!);
        _principalAccountProfileDetailsController.getCards(userId: _account.subUser!.id!);
      } catch (e) {/** */}
    }
  }

  @override
  void dispose() {
    _subAccountController.clearAllergenList();
    Get.delete<PrincipalAccountProfileDetailsController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Obx(
      () => Column(
        children: [
          const TitleRowDialog(titleKey: 'profileDetail'),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              children: [
                _buildHeaderView(theme),
                const SizedBox(height: 16),
                GeneralInfoView(
                  profile: _account?.subUser,
                  allergen: _principalAccountProfileDetailsController.getAllergensDetail(),
                ),
                if (_principalAccountProfileDetailsController.fundedWalletList.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  WalletView(
                    walletList: _principalAccountProfileDetailsController.fundedWalletList.toList(),
                  ),
                ],
                if (_principalAccountProfileDetailsController.cardList.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  CardListView(
                    cardList: _principalAccountProfileDetailsController.cardList.toList(),
                  ),
                ]
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildHeaderView(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            UserAvatar(url: _account?.subUser?.avatar?.url, size: 80),
            const SizedBox(width: 16.0),
            Expanded(
                child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 14.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 24,
                          child: Text(
                            _account?.subUser?.getFullName() ?? "-",
                            overflow: TextOverflow.ellipsis, // Adds "..." when the text is too long
                            maxLines: 1, // Ensures the text stays on one line
                            style: theme.textTheme.titleMedium?.copyWith(color: theme.colorScheme.onSurface),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          height: 19,
                          padding: const EdgeInsets.symmetric(vertical: 1, horizontal: 8),
                          decoration: BoxDecoration(
                            color: const Color(0xff36B37E).withValues(alpha: 0.16),
                            // Green background color
                            borderRadius: BorderRadius.circular(6), // Rounded corners
                          ),
                          child: Text(capitalizeFirstLetter(_account?.subAccountStatus ?? ""),
                              style: theme.textTheme.bodySmall?.copyWith(color: const Color(0xff1B806A))),
                        )
                      ],
                    )))
          ],
        )
      ],
    );
  }
}
