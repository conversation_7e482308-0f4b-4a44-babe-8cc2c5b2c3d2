import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class CartAccountSummary extends StatelessWidget {
  const CartAccountSummary({
    super.key,
    required this.totalItems,
    required this.subTotal,
    required this.canCheckout,
    required this.showDelete,
    required this.onProceedPayment,
    required this.onDeleteAccount,
  });

  final int totalItems;
  final int subTotal;
  final bool canCheckout;
  final bool showDelete;
  final Function() onProceedPayment;
  final Function() onDeleteAccount;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${FlutterI18n.translate(context, 'totalItem')}:',
                style: theme.textTheme.bodyLarge,
              ),
              Text(totalItems.toString(), style: theme.textTheme.bodyLarge),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${FlutterI18n.translate(context, 'subTotal')}:',
                style: theme.textTheme.bodyLarge,
              ),
              Text(formatPrice(subTotal), style: theme.textTheme.bodyLarge),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              showDelete
                  ? SizedBox(
                      width: 36,
                      height: 36,
                      child: IconButton(
                        onPressed: onDeleteAccount,
                        icon: SvgPicture.asset('assets/images/ic-trash-button.svg'),
                        padding: EdgeInsets.zero,
                      ),
                    )
                  : const SizedBox.shrink(),
              MainButton(
                text: FlutterI18n.translate(context, 'proceedPayment'),
                onPressed: onProceedPayment,
                width: 132,
                height: 32,
                textStyle: theme.textTheme.labelSmall,
                disabled: !canCheckout,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
