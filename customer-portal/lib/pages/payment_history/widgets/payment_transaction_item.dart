import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pacific_2_customer_portal/common/widgets/status_badge/status_badge.dart';
import 'package:pacific_2_customer_portal/models/payment_transaction.model.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class PaymentTransactionItem extends StatefulWidget {
  const PaymentTransactionItem({
    super.key,
    required this.item,
    required this.onPressDetails,
  });

  final PaymentTransaction item;
  final Function() onPressDetails;

  @override
  State<PaymentTransactionItem> createState() => _PaymentTransactionItemState();
}

class _PaymentTransactionItemState extends State<PaymentTransactionItem> with AutomaticKeepAliveClientMixin {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    ThemeData theme = Theme.of(context);

    return Material(
      child: InkWell(
        onTap: () {
          setState(() {
            isExpanded = !isExpanded;
          });
        },
        child: Ink(
          color: isExpanded ? theme.colorScheme.surface : theme.colorScheme.surfaceContainer,
          padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
          child: Column(
            children: [
              Row(
                children: [
                  SvgPicture.asset(
                    'assets/images/circle-${isExpanded ? 'up' : 'down'}.svg',
                    width: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Flexible(
                              child: Text(widget.item.id ?? '-', style: theme.textTheme.titleSmall),
                            ),
                            const SizedBox(width: 12),
                            StatusBadge(
                              status: PaymentTransactionStatus.fromString(widget.item.transactionStatus),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          FlutterI18n.translate(
                              context, PaymentTransactionType.fromString(widget.item.transactionType).labelKey),
                          style: theme.textTheme.bodyMedium!.copyWith(color: theme.colorScheme.onSurfaceVariant),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              //------------------------
              if (isExpanded)
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: _buildInfoWidgets(
                                context,
                                'paidAt',
                                formatDateTime(DateTime.fromMillisecondsSinceEpoch(widget.item.paidAt!)),
                              ),
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: _buildInfoWidgets(
                              context,
                              'paymentMethod',
                              widget.item.paymentMethodDisplayName ?? '-',
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: _buildInfoWidgets(
                              context,
                              'amount',
                              formatPrice(widget.item.amount ?? 0),
                            ),
                          ),
                          IconButton(
                            onPressed: widget.onPressDetails,
                            icon: SvgPicture.asset('assets/images/ic-info-button.svg'),
                            padding: EdgeInsets.zero,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              const SizedBox(height: 12),
              Divider(
                height: 1,
                thickness: 1,
                color: theme.colorScheme.outline,
              )
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildInfoWidgets(BuildContext context, String titleKey, String value) {
    ThemeData theme = Theme.of(context);
    return [
      Text(
        FlutterI18n.translate(context, titleKey),
        style: theme.textTheme.bodySmall!.copyWith(color: theme.colorScheme.onSurfaceVariant),
      ),
      const SizedBox(height: 2),
      Text(value, style: theme.textTheme.bodyMedium),
    ];
  }

  @override
  bool get wantKeepAlive => true;
}
