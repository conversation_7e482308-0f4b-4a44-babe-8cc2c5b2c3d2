// coverage:ignore-file
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pacific_2_customer_portal/common/tap_gesture_detector/tap_gesture_detector.dart';
import 'package:permission_handler/permission_handler.dart';
import "package:universal_html/html.dart" as html;

typedef AvatarSelectedCallback = void Function(String?, Uint8List?, File?);

class AvatarView extends StatefulWidget {
  final String? avatarUrl;
  final AvatarSelectedCallback onAvatarSelected;
  const AvatarView({super.key, required this.onAvatarSelected, this.avatarUrl});

  @override
  State<AvatarView> createState() => _AvatarViewState();
}

class _AvatarViewState extends State<AvatarView> {
  final ImagePicker _picker = ImagePicker();
  Uint8List? _imageBytes; // For web, use Uint8List
  File? _imageFile; // For mobile, use File
  String? _fileName;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return TapGestureDetector(
      onTap: kIsWeb ? _pickImage : _requestStoragePermission,
      child: CircleAvatar(
        radius: 126 / 2,
        backgroundColor: theme.colorScheme.surface,
        child: ClipOval(child: _getBackgroundImage()),
      ),
    );
  }

  Widget? _getBackgroundImage() {
    if (_imageFile != null) {
      return Image.file(_imageFile!);
    } else if (_imageBytes != null) {
      return Image.memory(
        _imageBytes!,
        fit: BoxFit.cover,
        width: 126,
        height: 126,
      );
    } else if (widget.avatarUrl != null) {
      return Image.network(
        widget.avatarUrl!,
        fit: BoxFit.cover,
        width: 126,
        height: 126,
        errorBuilder: (BuildContext context, Object error, StackTrace? stackTrace) {
          return SvgPicture.asset(
            'assets/images/avatar_action.svg',
            height: 126,
            width: 126,
          );
        },
      );
    } else {
      return SvgPicture.asset(
        'assets/images/avatar_action.svg',
        height: 126,
        width: 126,
      );
    }
  }

  Future<void> _requestStoragePermission() async {
    if (await Permission.storage.request().isGranted) {
      _pickImage();
    } else {
      print('Storage permission denied');
    }
  }

  Future<void> _pickImage() async {
    try {
      // If running on web
      if (kIsWeb) {
        final input = html.FileUploadInputElement();
        input.accept = 'image/png, image/jpeg';
        input.click();

        input.onChange.listen((e) async {
          if (input.files!.isNotEmpty) {
            final file = input.files![0]; // Get the first selected file
            _fileName = file.name; // Get the file name
            final reader = html.FileReader();
            reader.readAsArrayBuffer(file); // Read the file as ArrayBuffer
            reader.onLoadEnd.listen((e) {
              setState(() {
                _imageBytes = reader.result as Uint8List?; // Store image bytes
              });
              widget.onAvatarSelected(_fileName, _imageBytes, _imageFile);
            });
          }
        });
      } else {
        final XFile? image = await _picker.pickImage(
          source: ImageSource.gallery,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 85,
        );

        if (image != null) {
          setState(() {
            _imageFile = File(image.path); // For mobile
          });
          _fileName = image.name;
          widget.onAvatarSelected(_fileName, _imageBytes, _imageFile);
        }
      }
    } catch (e) {/** */}
  }
}
