import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pacific_2_customer_portal/models/wallet_transaction.model.dart';
import 'package:pacific_2_customer_portal/themes/colors.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';
import 'package:pacific_2_customer_portal/utils/transaction_utils.dart';

class TransactionItem extends StatelessWidget {
  const TransactionItem({
    super.key,
    required this.item,
    this.onPress,
    this.showTime = false,
    this.isSelected = false,
    this.isDesktop = false,
  });

  final WalletTransaction item;
  final Function()? onPress;
  final bool showTime;
  final bool isSelected;
  final bool isDesktop;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    bool isIncoming = checkIsIncoming(item);

    DateTime date = DateTime.fromMillisecondsSinceEpoch(item.createdAt ?? 0);
    String dateText = showTime ? formatDateTime(date) : formatDate(date);
    String amountText =
        '${isIncoming ? '+' : '-'} ${formatPrice((item.amount ?? 0).abs())}';

    return InkWell(
      onTap: onPress,
      borderRadius: isDesktop ? BorderRadius.circular(12) : null,
      child: Ink(
        padding: isDesktop
            ? const EdgeInsets.symmetric(vertical: 8, horizontal: 12)
            : const EdgeInsets.only(top: 8),
        decoration: BoxDecoration(
          color: isSelected && isDesktop ? theme.colorScheme.surface : null,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SvgPicture.asset(
                  'assets/images/ic-transaction-${isIncoming ? 'in' : 'out'}.svg',
                  width: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(dateText, style: theme.textTheme.bodyMedium),
                      const SizedBox(height: 4),
                      Text(
                        item.description ?? '-',
                        style: theme.textTheme.bodySmall!.copyWith(
                            color: theme.colorScheme.onSurfaceVariant),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  amountText,
                  style: theme.textTheme.bodyMedium!.copyWith(
                      color: isIncoming
                          ? successDark
                          : theme.colorScheme.onSurface),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Divider(
              height: 1,
              thickness: 1,
              color: theme.colorScheme.outline,
              indent: 36,
            ),
          ],
        ),
      ),
    );
  }
}
