import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/card_list_edit/card_edit_actions.dart';
import 'package:pacific_2_customer_portal/common/widgets/my_bottom_sheet/my_bottom_sheet.dart';
import 'package:pacific_2_customer_portal/models/card.model.dart';

class CardEditItem extends StatefulWidget {
  const CardEditItem({
    super.key,
    required this.item,
    required this.onToggleCardStatus,
    required this.onEditCardInfo,
    required this.onDeleteCard,
  });

  final CardAccount item;
  final Function() onToggleCardStatus;
  final Function() onEditCardInfo;
  final Function() onDeleteCard;

  @override
  State<CardEditItem> createState() => _CardEditItemState();
}

class _CardEditItemState extends State<CardEditItem> {
  bool _isShowingActions = false;

  void showCardActions(BuildContext context, CardAccount card) {
    if (Responsive.isDesktop(context)) {
      setState(() {
        _isShowingActions = !_isShowingActions;
      });
    } else {
      showMyBottomSheet(
        context,
        () => _renderCardActions(context, card),
        isFullHeight: false,
      );
    }
  }

  void hideCardActions() {
    if (Responsive.isDesktop(context)) {
      setState(() {
        _isShowingActions = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(FlutterI18n.translate(context, 'cardId'),
                    style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
                const SizedBox(height: 2),
                Text(widget.item.displayValue ?? '-',
                    style: theme.textTheme.bodyMedium, maxLines: 1, overflow: TextOverflow.ellipsis),
                const SizedBox(height: 12),
                Text(FlutterI18n.translate(context, 'cardType'),
                    style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
                const SizedBox(height: 2),
                Text(FlutterI18n.translate(context, widget.item.cardType!.labelKey),
                    style: theme.textTheme.bodyMedium, maxLines: 1, overflow: TextOverflow.ellipsis),
                const SizedBox(height: 12),
                Text(FlutterI18n.translate(context, 'cardAlias'),
                    style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
                const SizedBox(height: 2),
                Text(widget.item.cardAlias ?? '-',
                    style: theme.textTheme.bodyMedium, maxLines: 1, overflow: TextOverflow.ellipsis),
              ],
            ),
          ),
          PortalTarget(
            visible: _isShowingActions,
            portalFollower: GestureDetector(
              onTap: () => setState(() {
                _isShowingActions = false;
              }),
            ),
            child: PortalTarget(
              visible: _isShowingActions,
              anchor: const Aligned(
                follower: Alignment.topRight,
                target: Alignment.bottomRight,
              ),
              portalFollower: _renderCardActions(context, widget.item),
              child: SizedBox(
                width: 26,
                height: 26,
                child: IconButton(
                  onPressed: () => showCardActions(context, widget.item),
                  padding: EdgeInsets.zero,
                  icon: SvgPicture.asset(
                    'assets/images/ic-more-actions.svg',
                    width: 26,
                    height: 26,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _renderCardActions(BuildContext context, CardAccount card) {
    return CardEditActions(
      card: card,
      onToggleCardStatus: widget.onToggleCardStatus,
      onEditCardInfo: () {
        if (Responsive.isMobile(context)) Navigator.of(context).pop();
        hideCardActions();
        widget.onEditCardInfo();
      },
      onDeleteCard: () {
        hideCardActions();
        widget.onDeleteCard();
      },
    );
  }
}
