import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_outlined_button.dart';
import 'package:pacific_2_customer_portal/models/card.model.dart';
import 'package:pacific_2_customer_portal/utils/card_utils.dart';

class AddCardDialog extends StatefulWidget {
  const AddCardDialog({
    super.key,
    this.cardId,
    this.cardAlias,
    required this.onSave,
    required this.onClose,
  });

  final String? cardId;
  final String? cardAlias;
  final Function(String cardId, String cardAlias, CardType cardType) onSave;
  final Function() onClose;

  @override
  State<AddCardDialog> createState() => _AddCardDialogState();
}

class _AddCardDialogState extends State<AddCardDialog> {
  late final TextEditingController _cardIdController = TextEditingController(text: widget.cardId ?? '');
  late final TextEditingController _cardAliasController = TextEditingController(text: widget.cardAlias ?? '');

  String? _cardIdError;
  String? _cardAliasError;
  late CardType _cardType = getCardTypeFromCardId(_cardIdController.text.trim());

  @override
  void initState() {
    super.initState();
    _cardIdController.addListener(() {
      setState(() {
        _cardType = getCardTypeFromCardId(_cardIdController.text.trim());
      });
    });
  }

  @override
  void dispose() {
    _cardIdController.dispose();
    _cardAliasController.dispose();
    super.dispose();
  }

  bool validateInput(BuildContext context) {
    setState(() {
      _cardIdError = _cardIdController.text.trim().isEmpty ? FlutterI18n.translate(context, 'cardIdError') : null;
      _cardAliasError =
          _cardAliasController.text.trim().isEmpty ? FlutterI18n.translate(context, 'cardAliasError') : null;
    });
    return _cardIdError == null && _cardAliasError == null;
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Dialog(
      backgroundColor: theme.colorScheme.surfaceContainer,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 476),
        padding: EdgeInsets.all(Responsive.isDesktop(context) ? 24 : 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(FlutterI18n.translate(context, 'addCard'), style: theme.textTheme.headlineSmall),
            ),
            const SizedBox(height: 12),
            Text(FlutterI18n.translate(context, 'addCardDes'), style: theme.textTheme.bodyMedium),
            const SizedBox(height: 12),
            _renderTextField(
              theme,
              _cardIdController,
              FlutterI18n.translate(context, 'cardId'),
              FlutterI18n.translate(context, 'cardIdPlaceholder'),
              _cardIdError,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp("[0-9a-zA-Z]")),
                LengthLimitingTextInputFormatter(16),
              ],
              suffix: _cardType != CardType.unknown ? _renderCardType(context) : null,
            ),
            const SizedBox(height: 12),
            _renderTextField(
              theme,
              _cardAliasController,
              FlutterI18n.translate(context, 'cardAlias'),
              FlutterI18n.translate(context, 'cardAliasPlaceholder'),
              _cardAliasError,
            ),
            const SizedBox(height: 24),
            _renderButton(context),
          ],
        ),
      ),
    );
  }

  Widget _renderTextField(ThemeData theme, TextEditingController controller, String title, String hint, String? error,
      {List<TextInputFormatter>? inputFormatters, Widget? suffix}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(children: [
          Text(title, style: theme.textTheme.bodyMedium),
          const SizedBox(width: 2),
          Text(
            "*",
            style: theme.textTheme.titleMedium?.copyWith(color: theme.colorScheme.error),
          ),
        ]),
        const SizedBox(height: 4),
        TextField(
          controller: controller,
          inputFormatters: inputFormatters,
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
            errorText: error, // Display error if validation fails
            hintText: hint,
            hintStyle: theme.textTheme.bodyMedium!.copyWith(color: theme.colorScheme.outline.withValues(alpha: 0.48)),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            suffix: suffix,
          ),
        ),
      ],
    );
  }

  Widget _renderCardType(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Container(
      width: 36,
      height: 24,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: theme.colorScheme.primary),
      ),
      child: Center(
          child: Text(_cardType.type, style: theme.textTheme.labelSmall!.copyWith(color: theme.colorScheme.primary))),
    );
  }

  Widget _renderButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Responsive.isDesktop(context) ? 24 : 0),
      child: Row(
        children: [
          Expanded(
            child: MainOutlinedButton(
              height: 40,
              text: FlutterI18n.translate(context, 'cancel'),
              onPressed: widget.onClose,
            ),
          ),
          const SizedBox(width: 24),
          Expanded(
            child: MainButton(
              height: 40,
              text: FlutterI18n.translate(context, 'save'),
              onPressed: () {
                if (!validateInput(context)) return;
                widget.onClose();
                widget.onSave(
                  _cardIdController.text.trim(),
                  _cardAliasController.text.trim(),
                  _cardType,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
