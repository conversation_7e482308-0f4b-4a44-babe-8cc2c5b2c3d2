import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/const/const.asset.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/card_list_edit/add_card_dialog.dart';
import 'package:pacific_2_customer_portal/common/widgets/card_list_edit/card_edit_item.dart';
import 'package:pacific_2_customer_portal/controllers/loading.controller.dart';
import 'package:pacific_2_customer_portal/models/card.model.dart';

class CardListEdit extends StatelessWidget {
  const CardListEdit({
    super.key,
    required this.cardList,
    required this.onAddCard,
    required this.onUpdateCard,
    required this.onDeleteCard,
  });

  final List<CardAccount> cardList;
  final Function(CardAccount card) onAddCard;
  final Function(CardAccount card) onUpdateCard;
  final Function(CardAccount card) onDeleteCard;

  void showAddCardDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext ctx) {
        return AddCardDialog(
          onSave: (String cardId, String cardAlias, CardType cardType) {
            onAddCard(CardAccount()..updateData(cardId: cardId, cardAlias: cardAlias, cardType: cardType));
          },
          onClose: () => Navigator.of(ctx).pop(),
        );
      },
    );
  }

  void showEditCardDialog(BuildContext context, CardAccount card) {
    showDialog(
      context: context,
      builder: (BuildContext ctx) {
        return AddCardDialog(
          cardId: card.displayValue ?? card.cardId,
          cardAlias: card.cardAlias,
          onSave: (String cardId, String cardAlias, CardType cardType) {
            card.updateData(cardId: cardId, cardAlias: cardAlias, cardType: cardType);
            onUpdateCard(card);
          },
          onClose: () => Navigator.of(ctx).pop(),
        );
      },
    );
  }

  void showDeleteConfirmationDialog(BuildContext context, CardAccount card) {
    final loadingController = Get.find<LoadingController>();
    loadingController.showAlertDialog(
      context,
      title: FlutterI18n.translate(context, 'cardDelete'),
      message: FlutterI18n.translate(context, 'cardDeleteMessage'),
      positiveText: FlutterI18n.translate(context, 'yes'),
      onPositivePress: () {
        if (Responsive.isMobile(context)) Navigator.of(context).pop();
        onDeleteCard(card);
      },
      onNegativePress: () {},
    );
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: Responsive.isDesktop(context) ? 16 : 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Responsive.isDesktop(context) ? _renderDesktop(context) : _renderMobile(context),
    );
  }

  Widget _renderDesktop(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _renderHeader(context),
        GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisExtent: 166,
          ),
          primary: false,
          shrinkWrap: true,
          itemCount: cardList.length,
          itemBuilder: (context, index) {
            final card = cardList[index];
            return _renderCardItem(context, card);
          },
        ),
      ],
    );
  }

  Widget _renderMobile(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _renderHeader(context),
        ...cardList.map((card) => _renderCardItem(context, card)),
      ],
    );
  }

  Widget _renderHeader(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Row(
      children: [
        Expanded(
          child: Text(
            FlutterI18n.translate(context, 'addCard'),
            style: theme.textTheme.headlineSmall,
          ),
        ),
        IconButton(
          onPressed: () => showAddCardDialog(context),
          icon: SvgPicture.asset(ConstAsset.icAddButton),
          padding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _renderCardItem(BuildContext context, CardAccount card) {
    return CardEditItem(
      item: card,
      onToggleCardStatus: () {
        card.status = card.status == CardStatus.active ? CardStatus.inactive : CardStatus.active;
        onUpdateCard(card);
      },
      onEditCardInfo: () => showEditCardDialog(context, card),
      onDeleteCard: () => showDeleteConfirmationDialog(context, card),
    );
  }
}
