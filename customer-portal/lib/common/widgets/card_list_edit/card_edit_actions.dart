import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pacific_2_customer_portal/common/const/const.asset.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/my_switch/my_switch.dart';
import 'package:pacific_2_customer_portal/models/card.model.dart';

class CardEditActions extends StatefulWidget {
  const CardEditActions({
    super.key,
    required this.card,
    required this.onToggleCardStatus,
    required this.onDeleteCard,
    required this.onEditCardInfo,
  });

  final CardAccount card;
  final Function() onToggleCardStatus;
  final Function() onEditCardInfo;
  final Function() onDeleteCard;

  @override
  State<CardEditActions> createState() => _CardEditActionsState();
}

class _CardEditActionsState extends State<CardEditActions> {
  late bool _isCardDisabled = widget.card.status == CardStatus.inactive;

  void _toggleCardStatus() {
    setState(() {
      _isCardDisabled = !_isCardDisabled;
      widget.onToggleCardStatus();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Responsive.isDesktop(context) ? _renderDesktop(context) : _renderMobile(context);
  }

  Widget _renderDesktop(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      width: 226,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        color: theme.colorScheme.surfaceContainer,
        boxShadow: [
          BoxShadow(color: theme.colorScheme.outline, blurRadius: 2),
          BoxShadow(
            color: theme.colorScheme.outline,
            offset: const Offset(0, 15),
            blurRadius: 35,
            spreadRadius: -5,
          ),
        ],
      ),
      child: Material(
        color: theme.colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(6),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: _renderContent(context),
        ),
      ),
    );
  }

  Widget _renderMobile(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 10, 16, 8),
          child: Text(FlutterI18n.translate(context, 'card'), style: Theme.of(context).textTheme.headlineSmall),
        ),
        ..._renderContent(context),
      ],
    );
  }

  List<Widget> _renderContent(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return [
      InkWell(
        onTap: _toggleCardStatus,
        child: Ink(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              SvgPicture.asset(ConstAsset.icDisableButton, width: 32),
              const SizedBox(width: 16),
              Expanded(child: Text(FlutterI18n.translate(context, 'cardDisable'), style: theme.textTheme.bodyMedium)),
              const SizedBox(width: 16),
              MySwitch(value: _isCardDisabled, onChanged: (_) => _toggleCardStatus())
            ],
          ),
        ),
      ),
      if (widget.card.cardType != CardType.sb)
        InkWell(
          onTap: widget.onEditCardInfo,
          child: Ink(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                SvgPicture.asset(ConstAsset.icEditButton, width: 32),
                const SizedBox(width: 16),
                Text(FlutterI18n.translate(context, 'cardEditInfo'), style: theme.textTheme.bodyMedium),
              ],
            ),
          ),
        ),
      InkWell(
        onTap: widget.onDeleteCard,
        child: Ink(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              SvgPicture.asset(ConstAsset.icDeleteButton, width: 32),
              const SizedBox(width: 16),
              Text(FlutterI18n.translate(context, 'cardDelete'), style: theme.textTheme.bodyMedium),
            ],
          ),
        ),
      ),
    ];
  }
}
