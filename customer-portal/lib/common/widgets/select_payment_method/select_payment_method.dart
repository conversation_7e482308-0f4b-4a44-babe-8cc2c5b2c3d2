import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pacific_2_customer_portal/models/payment_method.model.dart';

class SelectPaymentMethod extends StatelessWidget {
  const SelectPaymentMethod({
    super.key,
    required this.paymentMethods,
    required this.selectedPaymentMethod,
    required this.onSelectPaymentMethod,
  });

  final List<PaymentMethod> paymentMethods;
  final PaymentMethod? selectedPaymentMethod;
  final Function(PaymentMethod method) onSelectPaymentMethod;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            FlutterI18n.translate(context, 'selectPaymentMethod'),
            style: theme.textTheme.headlineSmall,
          ),
          ...paymentMethods.map((method) {
            final bool isSelected = method.id == selectedPaymentMethod?.id;
            return Container(
              margin: const EdgeInsets.only(top: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: theme.colorScheme.outline,
                          offset: const Offset(-20, 20),
                          blurRadius: 40,
                          spreadRadius: -4,
                        ),
                      ]
                    : null,
              ),
              child: Card(
                elevation: 0,
                margin: const EdgeInsets.all(0),
                color: isSelected ? theme.colorScheme.surfaceContainer : theme.colorScheme.surface,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: theme.colorScheme.outline),
                ),
                child: InkWell(
                  onTap: () {
                    onSelectPaymentMethod(method);
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: IntrinsicHeight(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          SvgPicture.asset(
                            'assets/images/ic-radio-${isSelected ? 'on' : 'off'}.svg',
                            width: 24,
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${method.displayName}',
                                  style: theme.textTheme.titleSmall,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  method.paymentInstruction ?? '-',
                                  style:
                                      theme.textTheme.bodyMedium!.copyWith(color: theme.colorScheme.onSurfaceVariant),
                                ),
                              ],
                            ),
                          ),
                          if (method.icon?.url != null)
                            Padding(
                              padding: const EdgeInsets.only(left: 8),
                              child: Image.network(
                                method.icon!.url!,
                                width: 52,
                                errorBuilder: (context, error, stackTrace) => const SizedBox(width: 52),
                              ),
                            )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}
