import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/widgets/account_list/account_list.controller.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/user_avatar/user_avatar.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';
import 'package:pacific_2_customer_portal/themes/colors.dart';

class AccountListFilter extends StatefulWidget {
  const AccountListFilter({
    super.key,
    this.controllerTag,
    required this.selectedAccounts,
    required this.onSelectAccounts,
  });

  final String? controllerTag;
  final List<UserProfile> selectedAccounts;
  final Function(List<UserProfile> account) onSelectAccounts;

  @override
  State<AccountListFilter> createState() => _AccountListFilterState();
}

class _AccountListFilterState extends State<AccountListFilter> {
  late final AccountListController _accountListController = Get.find<AccountListController>(tag: widget.controllerTag);

  late final List<UserProfile> _selectedAccounts = widget.selectedAccounts;

  void _onClickItem(UserProfile item, bool checked) {
    setState(() {
      if (checked) {
        _selectedAccounts.add(item);
      } else {
        _selectedAccounts.remove(item);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Obx(() {
      final accountList = _accountListController.accountList;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
            child: Text(
              FlutterI18n.translate(context, 'selectAccount'),
              style: theme.textTheme.headlineSmall,
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: accountList.length,
              itemBuilder: (ctx, index) {
                var item = accountList[index];
                return CheckboxListTile(
                  value: _selectedAccounts.contains(item),
                  onChanged: (checked) => _onClickItem(item, checked!),
                  controlAffinity: ListTileControlAffinity.trailing,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                  title: Row(children: [
                    UserAvatar(url: item.avatar?.url, size: 40),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(item.getFullName(), style: theme.textTheme.bodyMedium),
                    ),
                    if (index == 0)
                      Container(
                        margin: const EdgeInsets.only(left: 12),
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: successLight,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          FlutterI18n.translate(context, 'mainUser'),
                          style: theme.textTheme.bodySmall!.copyWith(color: successDark),
                        ),
                      )
                  ]),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, top: 10),
            child: MainButton(
              text: FlutterI18n.translate(context, 'select'),
              onPressed: () {
                widget.onSelectAccounts(_selectedAccounts);
                Navigator.of(context).pop();
              },
            ),
          ),
        ],
      );
    });
  }
}
