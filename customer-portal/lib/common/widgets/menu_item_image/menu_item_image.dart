import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class MenuItemImage extends StatelessWidget {
  const MenuItemImage({
    super.key,
    required this.url,
    this.width = double.infinity,
    this.height = 0,
  });

  final String? url;
  final double width;
  final double height;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Image.network(
      url ?? '',
      width: width,
      height: height,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) => Container(
        color: theme.colorScheme.primary.withValues(alpha: 0.08),
        width: width,
        height: height,
        child: SvgPicture.asset(
          'assets/images/image-default-food.svg',
          width: width,
          height: height,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}
