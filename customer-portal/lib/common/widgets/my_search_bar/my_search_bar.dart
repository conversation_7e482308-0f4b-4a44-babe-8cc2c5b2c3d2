import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';

class MySearchBar extends StatelessWidget {
  const MySearchBar({
    super.key,
    required this.onTextChanged,
    this.hintText,
    this.controller,
    this.focusNode,
    this.maxLength,
  });

  final Function(String text) onTextChanged;
  final String? hintText;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final int? maxLength;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return SizedBox(
      height: 34,
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        style: theme.textTheme.bodyMedium,
        decoration: InputDecoration(
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(6)),
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
          prefixIcon: Padding(
            padding: const EdgeInsets.only(left: 12, right: 8),
            child: SvgPicture.asset('assets/images/ic-search.svg'),
          ),
          prefixIconConstraints: const BoxConstraints(maxWidth: 36, maxHeight: 16),
          hintText: hintText,
          hintStyle: theme.textTheme.bodyMedium!.copyWith(color: const Color(0xFFA1A9B8)),
        ),
        inputFormatters: [LengthLimitingTextInputFormatter(maxLength)],
        onChanged: onTextChanged,
      ),
    );
  }
}
