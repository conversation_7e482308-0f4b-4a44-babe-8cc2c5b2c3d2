import 'package:flutter/material.dart';

class MainButton extends StatelessWidget {
  final String text;
  final Color textColor;
  final TextStyle? textStyle;
  final Color? color;
  final Color? hoverColor;
  final Function() onPressed;
  final double height;
  final double? width;
  final bool disabled;
  final Widget? icon;
  final double spacing;
  final double borderRadius;

  const MainButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.height = 48,
    this.width,
    this.textColor = const Color(0xFFFFFFFF),
    this.textStyle,
    this.color,
    this.hoverColor,
    this.disabled = false,
    this.icon,
    this.spacing = 0,
    this.borderRadius = 24,
  });

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return SizedBox(
      height: height,
      width: width,
      child: TextButton(
        onPressed: disabled ? null : onPressed,
        style: TextButton.styleFrom(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius)),
          padding: const EdgeInsets.all(0),
          foregroundColor: hoverColor ?? theme.colorScheme.surfaceContainer,
          backgroundColor: color ?? theme.primaryColor,
          disabledBackgroundColor: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.24),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null) icon!,
            SizedBox(width: spacing),
            Text(
              text,
              style: (textStyle ?? theme.textTheme.labelLarge)!.copyWith(
                color: disabled ? theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.8) : textColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
