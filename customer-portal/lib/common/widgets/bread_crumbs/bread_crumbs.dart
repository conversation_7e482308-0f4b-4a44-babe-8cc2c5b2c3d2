import 'package:flutter/material.dart';

class BreadCrumbs extends StatelessWidget {
  final List<String> path;

  const BreadCrumbs({super.key, required this.path});

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.center,
      children: path.asMap().entries.fold(
        <Widget>[],
        (previousValue, entry) => [
          ...previousValue,
          if (entry.key != 0)
            Container(
              width: 4,
              height: 4,
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurfaceVariant,
                shape: BoxShape.circle,
              ),
              margin: const EdgeInsets.symmetric(horizontal: 16),
            ),
          Text(
            entry.value,
            style: theme.textTheme.bodyMedium!.copyWith(
              color: entry.key == path.length - 1 ? theme.colorScheme.onSurfaceVariant : theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}
