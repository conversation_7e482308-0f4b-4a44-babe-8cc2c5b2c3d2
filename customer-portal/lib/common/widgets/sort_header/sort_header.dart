import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pacific_2_customer_portal/common/tap_gesture_detector/tap_gesture_detector.dart';
import 'package:pacific_2_customer_portal/models/sort.model.dart';

const Color colorActive = Color(0xFF454F5B);
const Color colorInactive = Color(0xFFBCC2CE);

class SortHeader extends StatelessWidget {
  const SortHeader({
    super.key,
    required this.header,
    required this.currentKey,
    required this.sortOrder,
    required this.onTap,
  });

  final SortHeaderData header;
  final String? currentKey;
  final SortOrder? sortOrder;
  final void Function(String key) onTap;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return TapGestureDetector(
      onTap: () => onTap(header.key),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Text(
              header.label,
              style: theme.textTheme.labelSmall!.copyWith(color: colorActive),
            ),
          ),
          const SizedBox(width: 6),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                'assets/images/ic-arrow-drop-up.svg',
                width: 7,
                height: 5,
                colorFilter: ColorFilter.mode(
                    (currentKey == header.key && sortOrder == SortOrder.DESC) ? colorActive : colorInactive,
                    BlendMode.srcIn),
              ),
              const SizedBox(height: 2),
              SvgPicture.asset(
                'assets/images/ic-arrow-drop-down.svg',
                width: 7,
                height: 5,
                colorFilter: ColorFilter.mode(
                    (currentKey == header.key && sortOrder == SortOrder.ASC) ? colorActive : colorInactive,
                    BlendMode.srcIn),
              ),
            ],
          )
        ],
      ),
    );
  }
}
