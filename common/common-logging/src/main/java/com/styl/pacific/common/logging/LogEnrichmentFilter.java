/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.common.logging;

import com.styl.pacific.common.constant.PacificRestConstants;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.TraceContext;
import io.micrometer.tracing.Tracer;
import jakarta.servlet.ServletException;
import java.io.IOException;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * <AUTHOR>
 */
@Component
public class LogEnrichmentFilter extends OncePerRequestFilter {

	private static final Logger log = LoggerFactory.getLogger(LogEnrichmentFilter.class);

	private final Tracer tracer;

	public LogEnrichmentFilter(@Autowired(required = false) Tracer tracer) {
		this.tracer = tracer;
	}

	@Override
	protected void doFilterInternal(jakarta.servlet.http.HttpServletRequest request,
			jakarta.servlet.http.HttpServletResponse response, jakarta.servlet.FilterChain filterChain)
			throws ServletException, IOException {
		try {
			MDC.put("requestId", request.getHeader(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID));
			MDC.put("tenantId", request.getHeader(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID));
			MDC.put("userId", request.getHeader(PacificRestConstants.PlatformHeader.HEADER_X_USER_ID));
			MDC.put("method", request.getMethod());
			MDC.put("uri", request.getRequestURI());
			MDC.put("agent", request.getHeader("User-Agent"));
			if (log.isTraceEnabled() && !request.getRequestURI()
					.startsWith("/actuator")) {
				log.trace("Request URI: {}", request.getRequestURI());
			}

			// Add request id and trace id to response header
			Optional.ofNullable(tracer)
					.map(Tracer::currentSpan)
					.map(Span::context)
					.map(TraceContext::traceId)
					.ifPresent(traceId -> response.setHeader(PacificRestConstants.PlatformHeader.HEADER_X_TRACE_ID,
							traceId));
			response.addHeader(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, MDC.get("requestId"));

			filterChain.doFilter(request, response);
		} finally {
			MDC.clear();
		}
	}
}