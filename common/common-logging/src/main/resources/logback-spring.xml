<configuration>
    <springProperty name="APP_NAME" source="spring.application.name"/>
    <property name="LOG_FILE"
              value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/pacific/application.log}"/>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
    <include resource="org/springframework/boot/logging/logback/file-appender.xml"/>
    <appender name="jsonFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <pattern>
                    <pattern>
                        {
                        "level": "%level",
                        "message": "%msg",
                        "logger": "%logger",
                        "app": "${APP_NAME:-}",
                        "thread": "%thread",
                        "trace": "%X{traceId}",
                        "span": "%X{spanId}",
                        "requestId": "%X{requestId}",
                        "tenantId": "%X{tenantId:-}",
                        "userId": "%X{userId:-}",
                        "method": "%X{method:-}",
                        "uri": "%X{uri:-}",
                        "agent": "%X{agent:-}",
                        "stacktrace": "%ex"
                        }
                    </pattern>
                </pattern>
                <timestamp>
                    <fieldName>timestamp</fieldName>
                    <pattern>[UNIX_TIMESTAMP_AS_NUMBER]</pattern>
                </timestamp>
                <arguments>
                    <includeNonStructuredArguments>false</includeNonStructuredArguments>
                </arguments>
            </providers>
        </encoder>
        <file>${LOG_FILE}.json</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}.json.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
            <maxFileSize>${LOG_FILE_MAX_SIZE:-10MB}</maxFileSize>
            <maxHistory>${LOG_FILE_MAX_HISTORY:-30}</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="jsonFile"/>
    </root>
</configuration>