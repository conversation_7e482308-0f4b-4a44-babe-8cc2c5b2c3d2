/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.data.access.datasource;

import java.sql.Connection;
import java.sql.SQLException;
import java.time.Instant;
import javax.sql.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class ReadOnlyDataSourceWrapper extends DataSourceWrapper {

	public static final long RECOVERY_PERIOD = 60000L;
	private static final Logger log = LoggerFactory.getLogger(ReadOnlyDataSourceWrapper.class);

	private final DataSource master;
	private Instant lastReplicaFailure = Instant.ofEpochMilli(0);

	public ReadOnlyDataSourceWrapper(String name, DataSource replica, DataSource master, String applicationName) {
		super(name, replica, applicationName);
		this.master = master;
	}

	@Override
	public Connection getConnection() throws SQLException {
		// If the replica has failed, wait for the recovery period before trying again
		if (Instant.now()
				.isAfter(lastReplicaFailure.plusMillis(RECOVERY_PERIOD))) {
			try {
				return super.getConnection();
			} catch (SQLException e) {
				lastReplicaFailure = Instant.now();
				log.error("Error getting connection from replica, fallback to master", e);
			}
		}
		// If unable to get connection from replica, fallback to master
		return master.getConnection();
	}
}
