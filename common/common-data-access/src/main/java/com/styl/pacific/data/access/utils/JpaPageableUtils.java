/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.data.access.utils;

import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

/**
 * <AUTHOR>
 */
public class JpaPageableUtils {
	private static final Logger logger = LoggerFactory.getLogger(JpaPageableUtils.class);

	private JpaPageableUtils() {
	}

	public static Pageable createPageable(int pageNumber, int pageSize, Set<String> orderBy, String orderDir,
			Set<String> sortableFields, String defaultSortField) {

		Sort sort = null;
		Set<String> orderByFilter = orderBy.stream()
				.filter(sortableFields::contains)
				.collect(Collectors.toSet());
		if (orderByFilter.isEmpty()) {
			sort = Sort.by(Optional.ofNullable(orderDir)
					.map(Sort.Direction::fromString)
					.orElse(Sort.Direction.ASC), defaultSortField);
		} else {
			sort = Sort.unsorted();
			for (String field : orderByFilter) {
				sort = sort.and(Sort.by(Sort.Direction.fromString(orderDir), field));
			}
		}
		return pageSize > 0 ? PageRequest.of(pageNumber, pageSize, sort) : Pageable.unpaged(sort);
	}

	public static Sort createSort(Set<String> orderBy, String orderDir, Set<String> sortableFields,
			String defaultSortField) {
		Set<String> orderByFilter = orderBy.stream()
				.filter(sortableFields::contains)
				.collect(Collectors.toSet());
		if (orderByFilter.isEmpty()) {
			return Sort.by(Optional.ofNullable(orderDir)
					.map(Sort.Direction::fromString)
					.orElse(Sort.Direction.ASC), defaultSortField);
		} else {
			Sort sort = Sort.unsorted();
			for (String field : orderByFilter) {
				sort = sort.and(Sort.by(Sort.Direction.fromString(orderDir), field));
			}
			return sort;
		}
	}
}
