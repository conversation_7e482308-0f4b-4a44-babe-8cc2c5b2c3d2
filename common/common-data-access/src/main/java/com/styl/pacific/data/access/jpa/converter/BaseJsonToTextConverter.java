/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.data.access.jpa.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.styl.pacific.data.access.exceptions.ObjectParsingJsonException;
import jakarta.persistence.AttributeConverter;
import java.io.IOException;
import java.time.Instant;
import java.util.Optional;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

public class BaseJsonToTextConverter<T> implements AttributeConverter<T, String> {
	private static final ObjectMapper OBJECT_MAPPER;

	static {
		final var simpleModule = new SimpleModule();
		simpleModule.addDeserializer(Instant.class, new JsonDeserializer<>() {
			@Override
			public Instant deserialize(JsonParser p, DeserializationContext context) throws IOException {

				if (p.currentToken() == null || !StringUtils.isNumeric(p.getValueAsString())) {
					return null;
				}

				return Instant.ofEpochMilli(p.getValueAsLong());
			}
		});
		simpleModule.addSerializer(Instant.class, new JsonSerializer<>() {
			@SneakyThrows
			@Override
			public void serialize(Instant instant, JsonGenerator generator, SerializerProvider provider) {
				if (instant != null) {
					generator.writeNumber(instant.toEpochMilli());
				} else {
					generator.writeNull();
				}
			}
		});
		OBJECT_MAPPER = new ObjectMapper();
		OBJECT_MAPPER.registerModules(simpleModule, new JavaTimeModule());
	}

	private final JavaType javaType;

	public BaseJsonToTextConverter(Class<T> javaType) {
		this.javaType = OBJECT_MAPPER.getTypeFactory()
				.constructType(javaType);
	}

	public BaseJsonToTextConverter(TypeReference<T> typeReference) {
		this.javaType = OBJECT_MAPPER.getTypeFactory()
				.constructType(typeReference);
	}

	@Override
	public String convertToDatabaseColumn(T attribute) {
		return Optional.ofNullable(attribute)
				.map(it -> {
					try {
						return OBJECT_MAPPER.writeValueAsString(it);
					} catch (JsonProcessingException e) {
						throw new ObjectParsingJsonException(e.getMessage(), e);
					}
				})
				.orElse(null);
	}

	@Override
	@SuppressWarnings("unchecked")
	public T convertToEntityAttribute(String value) {
		return Optional.ofNullable(value)
				.filter(StringUtils::isNotBlank)
				.map(it -> {
					try {
						return (T) OBJECT_MAPPER.readValue(it, javaType);
					} catch (JsonProcessingException e) {
						throw new ObjectParsingJsonException(e.getMessage(), e);
					}
				})
				.orElse(null);
	}
}