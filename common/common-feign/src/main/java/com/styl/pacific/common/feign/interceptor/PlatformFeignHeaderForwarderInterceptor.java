/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.common.feign.interceptor;

import com.styl.pacific.application.rest.context.AsyncRequestContextHolder;
import com.styl.pacific.application.rest.context.RequestContextSupporter;
import com.styl.pacific.common.constant.PacificRestConstants;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class PlatformFeignHeaderForwarderInterceptor implements RequestInterceptor {

	private final RequestContextSupporter requestContextSupporter;

	@Override
	public void apply(RequestTemplate template) {
		final var httpRequestContext = requestContextSupporter.tryGetRequestContext();

		if (httpRequestContext.isPresent()) {
			final var requestContext = httpRequestContext.get();
			template.header(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, requestContext.getRequestId());
			template.header(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Optional.ofNullable(requestContext
					.getTenantId())
					.map(String::valueOf)
					.orElse(null));
			template.header(HttpHeaders.AUTHORIZATION, Optional.ofNullable(requestContext.getRawUserToken())
					.map(String::valueOf)
					.orElse(null));
		} else {
			AsyncRequestContextHolder.getContext()
					.ifPresent(context -> template.header(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID,
							Optional.ofNullable(context.getTenantId())
									.map(String::valueOf)
									.orElse(null)));
		}
	}
}
