/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.common.feign.config;

import com.styl.pacific.common.feign.exception.PacificFeignErrorDecoder;
import com.styl.pacific.common.feign.utils.CustomQueryMapEncoder;
import feign.Logger;
import feign.QueryMapEncoder;
import feign.Retryer;
import feign.codec.ErrorDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class FeignClientConfiguration {
	@Bean
	public Retryer retryer() {
		return new CommonFeignRetryer();
	}

	@Bean
	public QueryMapEncoder queryMapEncoder() {
		return new CustomQueryMapEncoder();
	}

	@Bean
	public ErrorDecoder errorDecoder() {
		return new PacificFeignErrorDecoder();
	}

	@Bean
	public Logger logger() {
		return new FeignSingleLineLogger();
	}
}
