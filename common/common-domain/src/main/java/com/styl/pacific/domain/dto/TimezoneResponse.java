/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.domain.dto;

import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class TimezoneResponse {

	private String zoneId;
	private String gtmOffset;
	private String displayName;

	public TimezoneResponse() {
	}

	public TimezoneResponse(TimeZone timeZone) {
		this.zoneId = timeZone.getID();
		this.gtmOffset = toGtmOffset(timeZone);
		this.displayName = String.format("(%s) %s", gtmOffset, zoneId);
	}

	private String toGtmOffset(TimeZone timeZone) {
		long hours = TimeUnit.MILLISECONDS.toHours(timeZone.getRawOffset());
		long minutes = TimeUnit.MILLISECONDS.toMinutes(timeZone.getRawOffset()) - TimeUnit.HOURS.toMinutes(hours);

		minutes = Math.abs(minutes);

		if (hours > 0) {
			return String.format("GMT+%02d:%02d", hours, minutes);
		}
		return String.format("GMT%02d:%02d", hours, minutes);
	}

	public String getZoneId() {
		return zoneId;
	}

	public void setZoneId(String zoneId) {
		this.zoneId = zoneId;
	}

	public String getGtmOffset() {
		return gtmOffset;
	}

	public void setGtmOffset(String gtmOffset) {
		this.gtmOffset = gtmOffset;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		TimezoneResponse that = (TimezoneResponse) o;
		return Objects.equals(zoneId, that.zoneId) && Objects.equals(gtmOffset, that.gtmOffset) && Objects.equals(
				displayName, that.displayName);
	}

	@Override
	public int hashCode() {
		return Objects.hash(zoneId, gtmOffset, displayName);
	}
}
