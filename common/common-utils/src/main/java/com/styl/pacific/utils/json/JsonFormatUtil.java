/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utils.json;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */

// TODO: aNinh fixes issue in this class. Ensure object map can serialize for java time module

public class JsonFormatUtil {
	private static final ObjectMapper objectMapper = new ObjectMapper();

	private JsonFormatUtil() {
	}

	public static <T> T convertToSingleObject(String json, Class<T> target) {
		try {
			if (StringUtils.isBlank(json)) {
				return null;
			}
			return objectMapper.readValue(json, target);
		} catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
	}

	public static <T> String convertToString(T data) {
		try {
			if (Objects.isNull(data)) {
				return null;
			}
			return objectMapper.writeValueAsString(data);
		} catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
	}

	public static <T> List<T> convertToList(String json, Class<T> target) {
		try {
			if (StringUtils.isBlank(json)) {
				return null;
			}
			JavaType type = objectMapper.getTypeFactory()
					.constructCollectionType(ArrayList.class, target);
			return objectMapper.readValue(json, type);
		} catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
	}

	public static <T> T convertToMap(String json, TypeReference<T> typeRef) {
		try {
			if (StringUtils.isBlank(json)) {
				return null;
			}
			return objectMapper.readValue(json, typeRef);
		} catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
	}
}
