/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utils.string;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StringRandomUtils {
	private static final SecureRandom secureRandom;
	private static final String ALLOWED_CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
	private static final int PIN_CODE_LENGTH = 6;
	private static final Logger log = LoggerFactory.getLogger(StringRandomUtils.class);

	// Initialize the SecureRandom instance
	static {
		SecureRandom tempRandom = null;
		try {
			tempRandom = SecureRandom.getInstanceStrong();  // Use the strong algorithm if available
			log.info("SecureRandom instance created using strong algorithm.");
		} catch (NoSuchAlgorithmException e) {
			log.warn("Strong SecureRandom algorithm not available. Falling back to default SecureRandom.");
			tempRandom = new SecureRandom();  // Fallback to default SecureRandom
		}
		secureRandom = tempRandom;
	}

	// Generate a random string of the specified length
	public static String generateRandomString(int length) {
		StringBuilder randomString = new StringBuilder(length);
		for (int i = 0; i < length; i++) {
			int randomIndex = secureRandom.nextInt(ALLOWED_CHARACTERS.length());
			randomString.append(ALLOWED_CHARACTERS.charAt(randomIndex));
		}
		return randomString.toString();
	}

	// Generate a random PIN code of fixed length (6 digits)
	public static String generateRandomPinCode() {
		int min = (int) Math.pow(10, PIN_CODE_LENGTH - 1); // 100000
		int max = (int) Math.pow(10, PIN_CODE_LENGTH) - 1; // 999999
		int pin = secureRandom.nextInt((max - min) + 1) + min;
		return String.valueOf(pin);
	}
}
