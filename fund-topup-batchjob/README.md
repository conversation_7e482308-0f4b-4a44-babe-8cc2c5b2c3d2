# Fund Topup Batch Job

This project provides CSV-based batch processing capabilities for fund topup and reset operations using OpenCSV's CSVBean functionality.

## Features

- **CSV Reader Service**: Generic CSV reading using OpenCSV CSVBean annotations
- **CSV Writer Service**: Generic CSV writing with support for custom configurations
- **Fund Topup Processing**: Handles fund topup CSV operations with validation
- **Fund Reset Processing**: Handles fund reset CSV operations with validation
- **Configurable**: Supports custom CSV separators, encoding, and batch sizes
- **Validation**: Built-in validation for CSV records
- **Error Handling**: Comprehensive error handling and logging

## Project Structure

```
fund-topup-batchjob/
├── src/main/java/com/styl/pacific/wilmar/batchjob/
│   ├── config/
│   │   └── CsvConfiguration.java          # CSV processing configuration
│   ├── exception/
│   │   └── CsvProcessingException.java    # Custom exception for CSV errors
│   ├── model/
│   │   ├── FundTopupCsvRecord.java        # Fund topup CSV record model
│   │   └── FundResetCsvRecord.java        # Fund reset CSV record model
│   ├── processor/
│   │   ├── FundProcessor.java             # Base processor interface
│   │   ├── FundTopupProcessor.java        # Fund topup processor
│   │   └── FundResetProcessor.java        # Fund reset processor
│   ├── service/
│   │   ├── CsvReaderService.java          # Generic CSV reader service
│   │   ├── CsvWriterService.java          # Generic CSV writer service
│   │   ├── FundTopupCsvProcessor.java     # Fund topup CSV processor
│   │   └── FundResetCsvProcessor.java     # Fund reset CSV processor
│   └── FundTopupJobApplication.java       # Main application class
├── sample-data/
│   ├── fund_topup_sample.csv              # Sample fund topup CSV
│   └── fund_reset_sample.csv              # Sample fund reset CSV
└── src/test/java/                         # Unit tests
```

## CSV Record Models

### FundTopupCsvRecord

Represents a fund topup operation with the following fields:

- `tenant_id` (required): Tenant identifier
- `customer_id` (required): Customer identifier
- `fund_source_id` (required): Fund source identifier
- `amount` (required): Topup amount
- `currency` (required): Currency code (e.g., USD, EUR, GBP)
- `topup_frequency`: Frequency of topup (DAILY, WEEKLY, MONTHLY, QUARTERLY)
- `start_topup_date`: Start date for topup schedule
- `end_topup_date`: End date for topup schedule
- `fund_expires_on`: Fund expiration date
- `fund_expires_in_ms`: Fund expiration in milliseconds
- `description`: Description of the topup
- `reference_id`: External reference identifier
- `status`: Status of the topup (PENDING, ACTIVE, COMPLETED, FAILED)
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

### FundResetCsvRecord

Represents a fund reset operation with the following fields:

- `tenant_id` (required): Tenant identifier
- `customer_id` (required): Customer identifier
- `fund_source_id` (required): Fund source identifier
- `wallet_id`: Wallet identifier
- `reset_amount`: Amount to reset (0.00 for full reset)
- `currency` (required): Currency code
- `reset_reason`: Reason for the reset
- `reference_id`: External reference identifier
- `reset_type`: Type of reset (FULL_RESET, PARTIAL_RESET)
- `status`: Status of the reset (PENDING, COMPLETED, FAILED)
- `processed_at`: Processing timestamp
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

## Usage

### Running the Application

The application supports two main operations:

```bash
# Process fund topup CSV files
java -jar fund-topup-batchjob.jar --topup

# Process fund reset CSV files
java -jar fund-topup-batchjob.jar --reset
```

### Using the CSV Services Programmatically

#### Reading CSV Files

```java
@Autowired
private CsvReaderService csvReaderService;

// Read fund topup records
List<FundTopupCsvRecord> topupRecords = csvReaderService.readCsvFile(
    "path/to/fund_topup.csv", 
    FundTopupCsvRecord.class
);

// Read with custom configuration
List<FundTopupCsvRecord> records = csvReaderService.readCsvFileWithConfig(
    "path/to/file.csv", 
    FundTopupCsvRecord.class, 
    ';',  // separator
    1     // skip lines
);
```

#### Writing CSV Files

```java
@Autowired
private CsvWriterService csvWriterService;

// Write fund topup records
csvWriterService.writeCsvFile("path/to/output.csv", topupRecords);

// Append to existing file
csvWriterService.appendToCsvFile("path/to/existing.csv", additionalRecords);

// Write with custom separator
csvWriterService.writeCsvFileWithConfig("path/to/output.csv", records, ';');
```

#### Using Specific Processors

```java
@Autowired
private FundTopupCsvProcessor fundTopupProcessor;

// Process fund topup file (read, validate, transform, write)
int processedCount = fundTopupProcessor.processFundTopupRecords(
    "input/fund_topup.csv", 
    "output/processed_topup.csv"
);

// Read and validate only
List<FundTopupCsvRecord> records = fundTopupProcessor.readFundTopupRecords(
    "input/fund_topup.csv"
);

// Write with timestamps
fundTopupProcessor.writeFundTopupRecords("output/topup.csv", records);
```

## Configuration

Configure CSV processing behavior in `application.yml`:

```yaml
fund-topup:
  csv:
    separator: ','
    skip-lines: 0
    ignore-leading-white-space: true
    ignore-empty-line: true
    apply-quotes-to-all: false
    input-directory: './input'
    output-directory: './output'
    error-directory: './error'
    processed-directory: './processed'
    batch-size: 1000
    validate-records: true
    create-backup: true
    encoding: 'UTF-8'
    date-format: 'yyyy-MM-dd HH:mm:ss'
```

## Validation

Both CSV record types include built-in validation:

### Fund Topup Validation
- `tenant_id` must be positive
- `customer_id` must be positive
- `fund_source_id` must be positive
- `amount` must be positive
- `currency` must not be empty

### Fund Reset Validation
- `tenant_id` must be positive
- `customer_id` must be positive
- `fund_source_id` must be positive
- `currency` must not be empty

## Error Handling

The application provides comprehensive error handling:

- **CsvProcessingException**: Thrown for CSV-related errors
- **Validation Errors**: Detailed validation error messages
- **File I/O Errors**: Proper handling of file access issues
- **Logging**: Extensive logging for debugging and monitoring

## Testing

Run the unit tests:

```bash
mvn test
```

The test suite includes:
- CSV reading and writing functionality
- Validation logic
- Error handling scenarios
- Custom configuration testing

## Sample Data

Sample CSV files are provided in the `sample-data/` directory:

- `fund_topup_sample.csv`: Example fund topup records
- `fund_reset_sample.csv`: Example fund reset records

These files demonstrate the expected CSV format and can be used for testing.

## Dependencies

- **OpenCSV**: For CSV processing with bean mapping
- **Spring Boot**: For dependency injection and application framework
- **Lombok**: For reducing boilerplate code
- **SLF4J**: For logging

## Best Practices

1. **Validation**: Always validate CSV records before processing
2. **Error Handling**: Use try-catch blocks and proper error logging
3. **Batch Processing**: Process large files in batches to avoid memory issues
4. **Backup**: Create backups of input files before processing
5. **Monitoring**: Use logging to monitor processing progress and errors
6. **Testing**: Write comprehensive unit tests for CSV operations
