/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.job.topup.service;

import static org.junit.jupiter.api.Assertions.*;

import com.styl.pacific.wallet.service.job.topup.model.FundTopupCsvRecord;
import com.styl.pacific.wallet.service.job.topup.service.csv.CsvReaderService;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Path;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

/**
 * Test class for CsvReaderService
 */
class CsvReaderServiceTest {

	private CsvReaderService csvReaderService;

	@TempDir
	Path tempDir;

	@BeforeEach
	void setUp() {
		csvReaderService = new CsvReaderService();
	}

	@Test
	void testReadFundTopupCsvFile() throws IOException {
		// Create a test CSV file
		Path csvFile = tempDir.resolve("test_fund_topup.csv");
		try (FileWriter writer = new FileWriter(csvFile.toFile())) {
			writer.write(
					"customer_no (* Unique identifier *),first_name,last_name,email,parent_email,group_name,amount_cent\n");
			writer.write("CUST001,John,Doe,<EMAIL>,<EMAIL>,Grade 5A,5000\n");
			writer.write("CUST002,Jane,Smith,<EMAIL>,<EMAIL>,Grade 6B,7500\n");
		}

		// Read the CSV file
		List<FundTopupCsvRecord> records = csvReaderService.readCsvFile(csvFile.toString(), FundTopupCsvRecord.class);

		// Verify the results
		assertNotNull(records);
		assertEquals(2, records.size());

		FundTopupCsvRecord record1 = records.get(0);
		assertEquals("CUST001", record1.getCustomerNo());
		assertEquals("John", record1.getFirstName());
		assertEquals("Doe", record1.getLastName());
		assertEquals("<EMAIL>", record1.getEmail());
		assertEquals("<EMAIL>", record1.getParentEmail());
		assertEquals("Grade 5A", record1.getGroupName());
		assertEquals(new BigDecimal("5000"), record1.getAmount());

		FundTopupCsvRecord record2 = records.get(1);
		assertEquals("CUST002", record2.getCustomerNo());
		assertEquals("Jane", record2.getFirstName());
		assertEquals("Smith", record2.getLastName());
		assertEquals("<EMAIL>", record2.getEmail());
		assertEquals("<EMAIL>", record2.getParentEmail());
		assertEquals("Grade 6B", record2.getGroupName());
		assertEquals(new BigDecimal("7500"), record2.getAmount());
	}

	@Test
	void testReadEmptyCsvFile() throws IOException {
		// Create an empty CSV file (with headers only)
		Path csvFile = tempDir.resolve("empty_fund_topup.csv");
		try (FileWriter writer = new FileWriter(csvFile.toFile())) {
			writer.write(
					"customer_no (* Unique identifier *),first_name,last_name,email,parent_email,group_name,amount_cent\n");
		}

		// Read the CSV file
		List<FundTopupCsvRecord> records = csvReaderService.readCsvFile(csvFile.toString(), FundTopupCsvRecord.class);

		// Verify the results
		assertNotNull(records);
		assertEquals(0, records.size());
	}

	@Test
	void testReadCsvFileWithCustomConfig() throws IOException {
		// Create a test CSV file with semicolon separator
		Path csvFile = tempDir.resolve("test_fund_topup_semicolon.csv");
		try (FileWriter writer = new FileWriter(csvFile.toFile())) {
			writer.write("customer_no (* Unique identifier *);first_name;last_name;email;amount_cent\n");
			writer.write("CUST001;John;Doe;<EMAIL>;5000\n");
		}

		// Read the CSV file with custom separator
		List<FundTopupCsvRecord> records = csvReaderService.readCsvFileWithConfig(csvFile.toString(),
				FundTopupCsvRecord.class, ';', 0);

		// Verify the results
		assertNotNull(records);
		assertEquals(1, records.size());

		FundTopupCsvRecord record = records.get(0);
		assertEquals("CUST001", record.getCustomerNo());
		assertEquals("John", record.getFirstName());
		assertEquals("Doe", record.getLastName());
		assertEquals("<EMAIL>", record.getEmail());
		assertEquals(new BigDecimal("5000"), record.getAmount());
	}
}
