/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wilmar.batchjob.service.csv;

import com.opencsv.bean.MappingStrategy;
import com.opencsv.bean.StatefulBeanToCsv;
import com.opencsv.bean.StatefulBeanToCsvBuilder;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import com.opencsv.exceptions.CsvRequiredFieldEmptyException;
import com.styl.pacific.wilmar.batchjob.exception.CsvProcessingException;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Generic CSV writer service using OpenCSV CSVBean
 * Provides methods to write Java objects to CSV files
 */
@Slf4j
@Service
public class CsvWriterService {

	/**
	 * Writes list of objects to CSV file using CSVBean
	 *
	 * @param filePath Path to the output CSV file
	 * @param records  List of objects to write to CSV
	 * @param <T>      Type of the objects
	 * @throws CsvProcessingException if writing fails
	 */
	public <T> void writeCsvFile(String filePath, List<T> records, MappingStrategy<T> mappingStrategy) {
		log.info("Writing {} records to CSV file: {}", records.size(), filePath);

		if (records.isEmpty()) {
			log.warn("No records to write to CSV file: {}", filePath);
			return;
		}

		try (Writer writer = new FileWriter(filePath, StandardCharsets.UTF_8)) {
			writeCsvToWriter(writer, records, mappingStrategy);
			log.info("Successfully wrote {} records to CSV file: {}", records.size(), filePath);
		} catch (IOException e) {
			String errorMessage = String.format("Failed to write CSV file: %s", filePath);
			log.error(errorMessage, e);
			throw new CsvProcessingException(errorMessage, e);
		}
	}

	public <T> void writeCsvToWriter(Writer writer, List<T> records) {
		writeCsvToWriter(writer, records, null);
	}

	/**
	 * Writes list of objects to Writer using CSVBean
	 *
	 * @param writer          Writer to write CSV data to
	 * @param records         List of objects to write to CSV
	 * @param mappingStrategy CSVBean mapping strategy
	 * @param <T>             Type of the objects
	 * @throws CsvProcessingException if writing fails
	 */
	public <T> void writeCsvToWriter(Writer writer, List<T> records, MappingStrategy<T> mappingStrategy) {
		log.debug("Writing {} records to CSV writer", records.size());

		if (records.isEmpty()) {
			log.warn("No records to write to CSV writer");
			return;
		}

		try {
			StatefulBeanToCsv<T> beanToCsv = new StatefulBeanToCsvBuilder<T>(writer).withMappingStrategy(
					mappingStrategy)
					.withApplyQuotesToAll(false)
					.build();

			beanToCsv.write(records);
			log.debug("Successfully wrote {} records to CSV writer", records.size());
		} catch (CsvDataTypeMismatchException | CsvRequiredFieldEmptyException e) {
			String errorMessage = "Failed to write records to CSV due to data validation error";
			log.error(errorMessage, e);
			throw new CsvProcessingException(errorMessage, e);
		} catch (Exception e) {
			String errorMessage = "Failed to write records to CSV";
			log.error(errorMessage, e);
			throw new CsvProcessingException(errorMessage, e);
		}
	}

	/**
	 * Appends list of objects to existing CSV file
	 *
	 * @param filePath Path to the CSV file
	 * @param records  List of objects to append to CSV
	 * @param <T>      Type of the objects
	 * @throws CsvProcessingException if writing fails
	 */
	public <T> void appendToCsvFile(String filePath, List<T> records) {
		log.info("Appending {} records to CSV file: {}", records.size(), filePath);

		if (records.isEmpty()) {
			log.warn("No records to append to CSV file: {}", filePath);
			return;
		}

		try (Writer writer = new FileWriter(filePath, StandardCharsets.UTF_8, true)) {
			// For append mode, we don't want to write headers again
			StatefulBeanToCsv<T> beanToCsv = new StatefulBeanToCsvBuilder<T>(writer).withApplyQuotesToAll(false)
					.build();

			beanToCsv.write(records);
			log.info("Successfully appended {} records to CSV file: {}", records.size(), filePath);
		} catch (IOException e) {
			String errorMessage = String.format("Failed to append to CSV file: %s", filePath);
			log.error(errorMessage, e);
			throw new CsvProcessingException(errorMessage, e);
		} catch (CsvDataTypeMismatchException | CsvRequiredFieldEmptyException e) {
			String errorMessage = "Failed to append records to CSV due to data validation error";
			log.error(errorMessage, e);
			throw new CsvProcessingException(errorMessage, e);
		}
	}

	/**
	 * Writes list of objects to CSV file with custom configuration
	 *
	 * @param filePath  Path to the output CSV file
	 * @param records   List of objects to write to CSV
	 * @param separator CSV field separator character
	 * @param <T>       Type of the objects
	 * @throws CsvProcessingException if writing fails
	 */
	public <T> void writeCsvFileWithConfig(String filePath, List<T> records, char separator) {
		log.info("Writing {} records to CSV file: {} with separator: '{}'", records.size(), filePath, separator);

		if (records.isEmpty()) {
			log.warn("No records to write to CSV file: {}", filePath);
			return;
		}

		try (Writer writer = new FileWriter(filePath, StandardCharsets.UTF_8)) {
			StatefulBeanToCsv<T> beanToCsv = new StatefulBeanToCsvBuilder<T>(writer).withSeparator(separator)
					.withApplyQuotesToAll(false)
					.build();

			beanToCsv.write(records);
			log.info("Successfully wrote {} records to CSV file with custom config: {}", records.size(), filePath);
		} catch (IOException e) {
			String errorMessage = String.format("Failed to write CSV file with custom config: %s", filePath);
			log.error(errorMessage, e);
			throw new CsvProcessingException(errorMessage, e);
		} catch (CsvDataTypeMismatchException | CsvRequiredFieldEmptyException e) {
			String errorMessage = "Failed to write records to CSV due to data validation error";
			log.error(errorMessage, e);
			throw new CsvProcessingException(errorMessage, e);
		}
	}
}
