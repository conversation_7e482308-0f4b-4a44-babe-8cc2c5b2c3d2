/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.job.topup.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for file processing
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "pacific.config.file")
public class FileConfiguration {

	/**
	 * Input file name
	 */
	private String inputFile;

	/**
	 * Output file name suffix
	 */
	private String outputFileSuffix = "_topup_result.csv";

	/**
	 * Default input directory for CSV files
	 */
	private String inputDirectory = "./input";

	/**
	 * Default output directory for CSV files
	 */
	private String outputDirectory = "./output";

	/**
	 * Default processed directory for successfully processed CSV files
	 */
	private String processedDirectory = "./processed";
}
