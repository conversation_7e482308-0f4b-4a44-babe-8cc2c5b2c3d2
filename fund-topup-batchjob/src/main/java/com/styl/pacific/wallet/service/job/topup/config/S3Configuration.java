/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.job.topup.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for S3 integration
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "pacific.s3")
public class S3Configuration {

	/**
	 * Whether S3 integration is enabled
	 */
	private boolean enabled = false;

	/**
	 * AWS region
	 */
	private String region = "ap-southeast-1";

	/**
	 * S3 bucket name
	 */
	private String bucketName = "fund-topup-bucket";

	/**
	 * Input files prefix in S3
	 */
	private String inputPrefix = "input/";

	/**
	 * Output files prefix in S3
	 */
	private String outputPrefix = "output/";

	/**
	 * Error files prefix in S3
	 */
	private String errorPrefix = "error/";

	/**
	 * Processed files prefix in S3
	 */
	private String processedPrefix = "processed/";
}
