/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.job.topup.service.file;

import com.styl.pacific.wallet.service.job.topup.config.FileConfiguration;
import com.styl.pacific.wallet.service.job.topup.config.TopupConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBooleanProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
@ConditionalOnBooleanProperty(name = "pacific.s3.enabled", matchIfMissing = false)
public class S3FileManagementService extends BaseFileManagementService {

	public S3FileManagementService(TopupConfiguration topupConfiguration, FileConfiguration fileConfiguration) {
		super(topupConfiguration, fileConfiguration);
	}

	@Override
	public String copyFileToInputDirectory(String filePath) {
		// TODO
		return "";
	}

	@Override
	public void moveToProcessedDirectory(String outputFilePath) {
		// TODO
	}
}
